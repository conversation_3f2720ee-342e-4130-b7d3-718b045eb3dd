"""
Debug script to test captcha solution application.
Use this to understand why the solution application is failing.
"""

import logging
from Captcha_files.captcha_solver import CaptchaSolver

# Setup detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='[%(asctime)s - %(levelname)s - %(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def test_solution_application_with_browser(browser, test_solution=None):
    """
    Test solution application with your existing browser instance.
    Call this from your GMX code when you have a solved captcha.
    
    Args:
        browser: Your Selenium WebDriver instance
        test_solution: Optional test solution token (use real one if available)
    """
    
    print("=" * 60)
    print("CAPTCHA SOLUTION APPLICATION DEBUG")
    print("=" * 60)
    
    # Use a test solution if none provided
    if not test_solution:
        test_solution = "8109342249595ef97e487d803f6ead85c3ec8cf9aadfa989910b9dee41324dfd"
    
    print(f"Testing with solution: {test_solution[:20]}...")
    print(f"Current URL: {browser.current_url}")
    print()
    
    # Initialize captcha solver
    captcha_solver = CaptchaSolver()
    
    # Run the debug method
    print("Step 1: Running solution application debug...")
    result = captcha_solver.debug_solution_application(browser, test_solution)
    
    print()
    print(f"Final result: {'SUCCESS' if result else 'FAILED'}")
    print("=" * 60)
    
    return result

def manual_solution_test(browser, solution):
    """
    Manual test of solution application methods.
    This tests each method individually to see which ones work.
    """
    
    print("=" * 60)
    print("MANUAL SOLUTION APPLICATION TEST")
    print("=" * 60)
    
    results = {}
    
    # Test Method 1: Direct textarea setting
    print("Testing Method 1: Direct textarea setting...")
    try:
        result = browser.execute_script(f"""
            try {{
                var responseField = document.querySelector('textarea[name="cf-captcha-response"]');
                if (responseField) {{
                    responseField.value = '{solution}';
                    responseField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    responseField.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    return {{ success: true, value: responseField.value.length }};
                }}
                return {{ success: false, reason: 'No response field found' }};
            }} catch (e) {{
                return {{ success: false, reason: e.message }};
            }}
        """)
        results['method1'] = result
        print(f"  Result: {result}")
    except Exception as e:
        results['method1'] = {'success': False, 'reason': str(e)}
        print(f"  Error: {e}")
    
    # Test Method 2: CaptchaFox API
    print("\nTesting Method 2: CaptchaFox API...")
    try:
        result = browser.execute_script(f"""
            try {{
                var info = {{
                    hasCaptchafox: typeof window.captchafox !== 'undefined',
                    hasWapi: typeof window.__cf_wapi !== 'undefined',
                    captchafoxMethods: typeof window.captchafox === 'object' ? Object.keys(window.captchafox) : [],
                    widgetExists: !!document.querySelector('[id^="cf-widget-"]')
                }};
                
                if (info.hasCaptchafox) {{
                    // Try to call available methods
                    if (window.captchafox.getResponse) {{
                        var currentResponse = window.captchafox.getResponse();
                        info.currentResponse = currentResponse;
                    }}
                }}
                
                return info;
            }} catch (e) {{
                return {{ success: false, reason: e.message }};
            }}
        """)
        results['method2'] = result
        print(f"  Result: {result}")
    except Exception as e:
        results['method2'] = {'success': False, 'reason': str(e)}
        print(f"  Error: {e}")
    
    # Test Method 3: Checkbox manipulation
    print("\nTesting Method 3: Checkbox manipulation...")
    try:
        result = browser.execute_script(f"""
            try {{
                var checkboxes = document.querySelectorAll('.cf-checkbox, .cf-checkbox-circle, [role="checkbox"]');
                var results = [];
                
                for (var i = 0; i < checkboxes.length; i++) {{
                    var checkbox = checkboxes[i];
                    var before = {{
                        ariaChecked: checkbox.getAttribute('aria-checked'),
                        classes: Array.from(checkbox.classList)
                    }};
                    
                    // Apply changes
                    checkbox.setAttribute('aria-checked', 'true');
                    checkbox.classList.add('checked', 'cf-checked');
                    checkbox.classList.remove('false');
                    
                    var after = {{
                        ariaChecked: checkbox.getAttribute('aria-checked'),
                        classes: Array.from(checkbox.classList)
                    }};
                    
                    results.push({{ before: before, after: after }});
                }}
                
                return {{ success: true, checkboxCount: checkboxes.length, results: results }};
            }} catch (e) {{
                return {{ success: false, reason: e.message }};
            }}
        """)
        results['method3'] = result
        print(f"  Result: {result}")
    except Exception as e:
        results['method3'] = {'success': False, 'reason': str(e)}
        print(f"  Error: {e}")
    
    # Final verification
    print("\nFinal verification...")
    try:
        verification = browser.execute_script("""
            try {
                var responseField = document.querySelector('textarea[name="cf-captcha-response"]');
                var checkbox = document.querySelector('.cf-checkbox');
                
                return {
                    responseValue: responseField ? responseField.value : null,
                    responseLength: responseField ? responseField.value.length : 0,
                    checkboxChecked: checkbox ? checkbox.getAttribute('aria-checked') : null,
                    checkboxClasses: checkbox ? Array.from(checkbox.classList) : []
                };
            } catch (e) {
                return { error: e.message };
            }
        """)
        results['verification'] = verification
        print(f"  Verification: {verification}")
    except Exception as e:
        results['verification'] = {'error': str(e)}
        print(f"  Verification error: {e}")
    
    print("\n" + "=" * 60)
    print("MANUAL TEST COMPLETE")
    print("=" * 60)
    
    return results

def integration_example():
    """Show how to integrate the debug methods with your GMX code."""
    
    example_code = '''
    # Add this to your GMX Worker class for debugging
    
    def debug_captcha_solution_application(self, solution):
        """Debug method to test captcha solution application."""
        from debug_solution_application import test_solution_application_with_browser, manual_solution_test
        
        self.logger.info("🔍 Starting captcha solution application debug...")
        
        # Test the automated method
        auto_result = test_solution_application_with_browser(self.browser, solution)
        
        # Test manual methods
        manual_results = manual_solution_test(self.browser, solution)
        
        self.logger.info(f"Auto result: {auto_result}")
        self.logger.info(f"Manual results: {manual_results}")
        
        return auto_result, manual_results
    
    # Use it in your captcha solving workflow:
    def solve_gmx_captcha_with_debug(self):
        captcha_solver = CaptchaSolver()
        
        # Solve the captcha
        solution = captcha_solver.solve_page_captcha(driver=self.browser)
        
        if solution:
            # Debug the solution application
            auto_result, manual_results = self.debug_captcha_solution_application(solution)
            
            if auto_result:
                self.logger.info("✅ Captcha solution applied successfully!")
                return True
            else:
                self.logger.error("❌ Captcha solution application failed")
                self.logger.info("Manual test results for troubleshooting:")
                for method, result in manual_results.items():
                    self.logger.info(f"  {method}: {result}")
                return False
        else:
            self.logger.error("❌ Failed to solve captcha")
            return False
    '''
    
    print("INTEGRATION EXAMPLE:")
    print("=" * 40)
    print(example_code)

if __name__ == "__main__":
    print("Captcha Solution Application Debug Tool")
    print()
    
    integration_example()
    
    print("\n✅ Debug tool ready!")
    print("Call test_solution_application_with_browser(your_browser, solution) from your GMX code")
