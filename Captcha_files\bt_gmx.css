/* 1200px */
/* 767px */
/* 1199px */
/* 991px */
/* posdoc
  name: Media Query Mixin
  description: This mixin offers a predefined set of media queries.
  data:
*/
/* posdoc
    pos-scss/tools/media-query.scss:
        '<code>
            <b>Usage:</b>@include media-query($screen-size)<br><br>
            $screen-size can take one of the following values:<br>
            <b>small</b><br>
            only for small screen<br>
            max-width: 767px<br><br>
            <b>medium</b><br>
            only for medium screens<br>
            min-width: 768px and max-width: 1199px<br><br>
            <b>medium-portrait</b><br>
             only for medium screens in portrait mode<br>
             min-width: 768px and max-width: 991px<br><br>
            <b>medium-landscape</b><br>
            only for medium screens in landscape mode<br>
            min-width: 992px and 1199px<br><br>
            <b>large</b><br>
            only for large screens<br>
            min-width: 1200px<br><br>
            <b>small-medium</b><br>
            small and medium screen sizes<br>
            max-width: 1199px<br><br>
            <b>small-medium-portrait</b><br>
            small and medium in portrait mode screen sizes<br>
            max-width: 991px<br><br>
            <b>medium-large</b><br>
            medium and large screen sizes<br>
            min-width: 768px<br><br>
            <b>medium-landscape-large</b><br>
            medium in landscape mode and large screen sizes<br>
            min-width: 992px<br><br>
        </code>'
*/
:root {
  --font-weight-header: 500;
  --font-family-header: Roboto, Arial, Helvetica, sans-serif;
  --font-family-hero: RobotoCondensed, Arial, Helvetica, sans-serif;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("https://img.ui-portal.de/ci/gmx/global/fonts/roboto/Roboto-Regular-webfont.woff") format("woff");
}
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("https://img.ui-portal.de/ci/gmx/global/fonts/roboto/Roboto-Medium-webfont.woff") format("woff");
}
@font-face {
  font-family: RobotoCondensed;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("https://img.ui-portal.de/ci/gmx/global/fonts/roboto/RobotoCondensed-Regular-webfont.woff") format("woff");
}
@font-face {
  font-family: RobotoCondensed;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("https://img.ui-portal.de/ci/gmx/global/fonts/roboto/RobotoCondensed-Light-webfont.woff") format("woff");
}
.pos-brand-icon--default {
  width: 68px;
  height: 22px;
}

.pos-brand-icon--small {
  width: 62px;
  height: 20px;
}

.pos-brand-title {
  margin-left: 11px;
  font-family: RobotoCondensed;
  font-weight: 300;
  font-size: 28px;
}

.pos-brand-title--small {
  margin-left: 10px;
  font-size: 26px;
}

.pos-header .pos-brand-logo {
  margin-top: -4px;
}

.onereg-mdh-info-box__icon-title,
.onereg-footer {
  font-family: var(--font-family-header);
  font-weight: 500;
}

.onereg-pay-info-box__icon-subtitle {
  font-family: var(--font-family-header);
  font-weight: 400;
}

onereg-app .pos-header .pos-brand-title {
  font-size: 26px;
}
@media screen and (min-width: 768px) {
  onereg-app .pos-header .pos-brand-title {
    margin-left: 16px;
    font-size: 42px;
  }
}

.pos-brand-icon--default {
  width: 62px;
  height: 22px;
}
@media screen and (min-width: 768px) {
  .pos-brand-icon--default {
    width: 98px;
    height: 32px;
  }
}

@media screen and (min-width: 768px) {
  .onereg-app.ismultistep--always .pos-brand-icon--default {
    width: 68px;
    height: 22px;
  }
}

.onereg-pay-info-box__leading-description {
  font-family: var(--font-family-header);
  font-weight: 400;
}

.pos-message-modal {
  padding: 16px;
}
.pos-message-modal .pos-message-modal__wrapper {
  border: none;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.5);
}
.pos-message-modal .pos-message__icon--info {
  margin-top: 8px;
  margin-bottom: 24px;
}
.pos-message-modal .pos-message-modal__heading {
  margin-bottom: 16px;
}
.pos-message-modal .pos-message-modal__heading h3 {
  margin-bottom: 0;
}
.pos-message-modal .pos-message-modal__details {
  font-size: 14px;
  line-height: 22px;
}
.pos-message-modal .pos-message-modal__footer {
  border: none;
}
/*# sourceMappingURL=bt_gmx.css.map */
