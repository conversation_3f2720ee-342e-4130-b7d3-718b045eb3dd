"""
Captcha solver module for handling 2captcha integration with Selenium.
"""
import time
import requests
import logging
import config

class CaptchaSolver:
    """Class to handle captcha solving using 2captcha service."""

    def __init__(self, api_key=None):
        """Initialize the captcha solver.

        Args:
            api_key: 2captcha API key (default from config)
        """
        if api_key is None:
            api_key = config.TWOCAPTCHA_API_KEY

        self.api_key = api_key
        self.max_retries = 3
        self.base_url = "https://api.2captcha.com"

        # Setup logging
        self.logger = logging.getLogger("CaptchaSolver")

    def solve_captcha_fox(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Solve CaptchaFox captcha using 2captcha service.

        Args:
            website_url: URL of the website with captcha
            website_key: Site key for the captcha
            user_agent: User agent string (optional)
            proxy_config: Proxy configuration dict (optional)

        Returns:
            str: Captcha solution token or None if failed
        """
        try:
            self.logger.info("Starting CaptchaFox solving process...")

            # Create task
            task_id = self._create_captcha_task(website_url, website_key, user_agent, proxy_config)
            if not task_id:
                self.logger.error("Failed to create captcha task")
                return None

            # Get solution
            solution = self._get_task_result(task_id)
            if solution:
                self.logger.info("Captcha solved successfully")
                return solution
            else:
                self.logger.error("Failed to get captcha solution")
                return None

        except Exception as e:
            self.logger.error(f"Error solving captcha: {e}")
            return None

    def _create_captcha_task(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Create a captcha solving task.

        Returns:
            str: Task ID or None if failed
        """
        try:
            # Prepare task data
            task_data = {
                "clientKey": self.api_key,
                "task": {
                    "type": "CaptchaFoxTask",
                    "websiteURL": website_url,
                    "websiteKey": website_key
                }
            }

            # Add user agent if provided
            if user_agent:
                task_data["task"]["userAgent"] = user_agent

            # Add proxy configuration if provided
            if proxy_config:
                task_data["task"].update({
                    "proxyType": "http",
                    "proxyAddress": "*************",
                    "proxyPort": "12323",
                    "proxyLogin": "14a638c2105fb",
                    "proxyPassword": "10ef8e713c"
                })

            # Send request
            response = requests.post(
                f"{self.base_url}/createTask",
                json=task_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("errorId") == 0:
                    task_id = result.get("taskId")
                    self.logger.info(f"Task created successfully: {task_id}")
                    return task_id
                else:
                    self.logger.error(f"API error: {result.get('errorDescription', 'Unknown error')}")
                    return None
            else:
                self.logger.error(f"HTTP error: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating task: {e}")
            return None
        



    def _get_task_result(self, task_id, max_wait_time=120):
        """Get the result of a captcha solving task.

        Args:
            task_id: Task ID from create task
            max_wait_time: Maximum time to wait in seconds

        Returns:
            str: Solution token or None if failed
        """
        try:
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                # Check task status
                response = requests.post(
                    f"{self.base_url}/getTaskResult",
                    json={
                        "clientKey": self.api_key,
                        "taskId": task_id
                    },
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()

                    if result.get("errorId") == 0:
                        status = result.get("status")

                        if status == "ready":
                            solution = result.get("solution", {}).get("token")
                            if solution:
                                self.logger.info("Captcha solution received")
                                return solution
                            else:
                                self.logger.error("No solution token in response")
                                return None
                        elif status == "processing":
                            self.logger.info("Captcha is being processed, waiting...")
                            time.sleep(5)
                            continue
                        else:
                            self.logger.error(f"Unexpected status: {status}")
                            return None
                    else:
                        self.logger.error(f"API error: {result.get('errorDescription', 'Unknown error')}")
                        return None
                else:
                    self.logger.error(f"HTTP error: {response.status_code}")
                    return None

            self.logger.error("Timeout waiting for captcha solution")
            return None

        except Exception as e:
            self.logger.error(f"Error getting task result: {e}")
            return None



    def detect_captcha(self, driver):
        """Detect if there's a captcha on the current page.

        Args:
            driver: Selenium WebDriver instance

        Returns:
            dict: Captcha information or None if no captcha found
        """
        try:
            # Common captcha selectors
            captcha_selectors = [
                # CaptchaFox selectors
                'iframe[src*="captchafox"]',
                'div[data-captchafox]',
                '[data-sitekey]',
                # Generic captcha selectors
                'iframe[src*="captcha"]',
                '.captcha-container',
                '#captcha',
                '.cf-captcha',
                '[data-cf-captcha]'
            ]

            for selector in captcha_selectors:
                try:
                    elements = driver.find_elements("css selector", selector)
                    if elements and elements[0].is_displayed():
                        self.logger.info(f"Captcha detected with selector: {selector}")

                        # Extract captcha information
                        captcha_info = self._extract_captcha_info(driver, elements[0])
                        if captcha_info:
                            return captcha_info

                except Exception as e:
                    self.logger.debug(f"Error checking selector {selector}: {e}")
                    continue

            self.logger.info("No captcha detected on page")
            return None

        except Exception as e:
            self.logger.error(f"Error detecting captcha: {e}")
            return None



    def _extract_captcha_info(self, driver, captcha_element):
        """Extract captcha information from the detected element.

        Args:
            driver: Selenium WebDriver instance
            captcha_element: WebElement containing captcha

        Returns:
            dict: Captcha information
        """
        try:
            captcha_info = {
                'type': 'unknown',
                'website_url': driver.current_url,
                'website_key': None,
                'element': captcha_element
            }

            # Try to extract site key from various attributes
            site_key_attributes = ['data-sitekey', 'data-site-key', 'data-captchafox-sitekey']
            for attr in site_key_attributes:
                site_key = captcha_element.get_attribute(attr)
                if site_key:
                    captcha_info['website_key'] = site_key
                    captcha_info['type'] = 'captchafox'
                    break

            # If no site key found in element, try to find it in page source
            if not captcha_info['website_key']:
                site_key = self._extract_site_key_from_page(driver)
                if site_key:
                    captcha_info['website_key'] = site_key
                    captcha_info['type'] = 'captchafox'
                else:
                    captcha_info['website_key'] = "sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w"
                    captcha_info['type'] = 'captchafox'
                    

            # Extract iframe src if it's an iframe
            if captcha_element.tag_name.lower() == 'iframe':
                iframe_src = captcha_element.get_attribute('src')
                if iframe_src:
                    captcha_info['iframe_src'] = iframe_src
                    # Try to extract site key from iframe src
                    if not captcha_info['website_key']:
                        site_key = self._extract_site_key_from_url(iframe_src)
                        print(site_key)
                        if site_key:
                            captcha_info['website_key'] = site_key
                            captcha_info['type'] = 'captchafox'
                        else:
                            captcha_info['website_key'] = "sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w"
                            captcha_info['type'] = 'captchafox'

            self.logger.info(f"Extracted captcha info: {captcha_info}")
            return captcha_info

        except Exception as e:
            self.logger.error(f"Error extracting captcha info: {e}")
            return None



    def _extract_site_key_from_page(self, driver):
        """Extract site key from page source.

        Args:
            driver: Selenium WebDriver instance

        Returns:
            str: Site key or None if not found
        """
        try:
            page_source = driver.page_source

            # Common patterns for site keys
            import re
            patterns = [
                r'data-sitekey["\s]*=["\s]*([^"\'>\s]+)',
                r'data-site-key["\s]*=["\s]*([^"\'>\s]+)',
                r'sitekey["\s]*:["\s]*["\']([^"\']+)["\']',
                r'site_key["\s]*:["\s]*["\']([^"\']+)["\']',
                r'websiteKey["\s]*:["\s]*["\']([^"\']+)["\']'
            ]

            for pattern in patterns:
                match = re.search(pattern, page_source, re.IGNORECASE)
                if match:
                    site_key = match.group(1)
                    self.logger.info(f"Found site key in page source: {site_key}")
                    return site_key

            return None

        except Exception as e:
            self.logger.error(f"Error extracting site key from page: {e}")
            return None

    def _extract_site_key_from_url(self, url):
        """Extract site key from URL parameters.

        Args:
            url: URL to extract site key from

        Returns:
            str: Site key or None if not found
        """
        try:
            import re
            patterns = [
                r'[?&]sitekey=([^&]+)',
                r'[?&]site_key=([^&]+)',
                r'[?&]k=([^&]+)'
            ]

            for pattern in patterns:
                match = re.search(pattern, url, re.IGNORECASE)
                if match:
                    site_key = match.group(1)
                    self.logger.info(f"Found site key in URL: {site_key}")
                    return site_key

            return None

        except Exception as e:
            self.logger.error(f"Error extracting site key from URL: {e}")
            return None

    def solve_page_captcha(self, driver, user_agent=None, proxy_config=None):
        """Detect and solve any captcha on the current page.

        Args:
            driver: Selenium WebDriver instance
            user_agent: User agent string (optional)
            proxy_config: Proxy configuration dict (optional)

        Returns:
            str: Captcha solution token or None if no captcha or failed
        """
        try:
            # Detect captcha on page
            captcha_info = self.detect_captcha(driver)
            if not captcha_info:
                self.logger.info("No captcha found on page")
                return None

            if not captcha_info.get('website_key'):
                self.logger.error("No website key found for captcha")
                return None

            # Solve the captcha
            solution = self.solve_captcha_fox(
                website_url=captcha_info['website_url'],
                website_key=captcha_info['website_key'],
                user_agent=user_agent,
                proxy_config=proxy_config
            )

            if solution:
                # Apply the solution to the page
                success = self._apply_captcha_solution(driver, solution)
                if success:
                    self.logger.info("Captcha solved and applied successfully")
                    return solution
                else:
                    self.logger.error("Failed to apply captcha solution")
                    return None
            else:
                self.logger.error("Failed to solve captcha")
                return None

        except Exception as e:
            self.logger.error(f"Error solving page captcha: {e}")
            return None

    def _apply_captcha_solution(self, driver, solution):
        """Apply the captcha solution to the page.

        Args:
            driver: Selenium WebDriver instance
            solution: Solution token from 2captcha

        Returns:
            bool: True if solution applied successfully
        """
        try:
            # Look for common captcha response fields
            response_selectors = [
                'input[name="cf-turnstile-response"]',
                'textarea[name="cf-turnstile-response"]',
                'input[name="captcha-response"]',
                'textarea[name="captcha-response"]',
                'input[name="g-recaptcha-response"]',
                'textarea[name="g-recaptcha-response"]'
            ]

            for selector in response_selectors:
                try:
                    elements = driver.find_elements("css selector", selector)
                    if elements:
                        element = elements[0]
                        # Set the solution token
                        driver.execute_script(f"arguments[0].value = '{solution}';", element)
                        self.logger.info(f"Applied solution to element: {selector}")

                        # Trigger change event
                        driver.execute_script("arguments[0].dispatchEvent(new Event('change'));", element)

                        return True

                except Exception as e:
                    self.logger.debug(f"Error applying solution to {selector}: {e}")
                    continue

            # If no standard response field found, try to inject solution via JavaScript
            try:
                # Common JavaScript methods to set captcha response
                js_methods = [
                    f"if (window.turnstile) {{ window.turnstile.setResponse('{solution}'); }}",
                    f"if (window.cf_turnstile_callback) {{ window.cf_turnstile_callback('{solution}'); }}",
                    f"if (window.captchaCallback) {{ window.captchaCallback('{solution}'); }}"
                ]

                for js_method in js_methods:
                    try:
                        driver.execute_script(js_method)
                        self.logger.info("Applied solution via JavaScript")
                        return True
                    except Exception as e:
                        self.logger.debug(f"JavaScript method failed: {e}")
                        continue

            except Exception as e:
                self.logger.debug(f"Error applying solution via JavaScript: {e}")

            self.logger.warning("Could not find a way to apply captcha solution")
            return False

        except Exception as e:
            self.logger.error(f"Error applying captcha solution: {e}")
            return False
