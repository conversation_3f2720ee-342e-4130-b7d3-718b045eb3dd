"""
Integration code for adding captcha detection and solving to your GMX creator.
Add these methods to your Worker class in gmx.py.
"""

def add_captcha_methods_to_worker():
    """
    These methods should be added to your Worker class in gmx.py
    """
    
    methods_code = '''
    def debug_captcha_detection(self):
        """Debug method to check what captcha elements are on the page."""
        try:
            from Captcha_files.captcha_solver import CaptchaSolver
            
            self.logger.info("🔍 Starting captcha detection debug...")
            captcha_solver = CaptchaSolver()
            
            # Run comprehensive debug
            captcha_solver.debug_captcha_elements(self.browser)
            
            # Try detection
            captcha_info = captcha_solver.detect_captcha(self.browser)
            
            if captcha_info:
                self.logger.info(f"✅ Captcha detected: {captcha_info}")
                return True
            else:
                self.logger.warning("❌ No captcha detected")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in captcha debug: {e}")
            return False
    
    def solve_gmx_captcha(self):
        """Detect and solve GMX captcha on the current page."""
        try:
            from Captcha_files.captcha_solver import CaptchaSolver
            
            self.logger.info("🤖 Starting GMX captcha solving process...")
            
            # Initialize solver
            captcha_solver = CaptchaSolver()
            
            # First, debug what's on the page
            self.logger.info("Running captcha detection debug...")
            captcha_solver.debug_captcha_elements(self.browser)
            
            # Get user agent
            user_agent = None
            try:
                user_agent = self.browser.execute_script("return navigator.userAgent;")
            except Exception as e:
                self.logger.debug(f"Could not get user agent: {e}")
            
            # Try to solve captcha
            solution = captcha_solver.solve_page_captcha(
                driver=self.browser,
                user_agent=user_agent
            )
            
            if solution:
                self.logger.info(f"✅ Captcha solved successfully! Token: {solution[:20]}...")
                
                # Wait for solution to be processed
                sleep(2)
                
                # Verify solution was applied
                try:
                    response_field = self.browser.find_elements("css selector", 'textarea[name="cf-captcha-response"]')
                    if response_field and response_field[0].get_attribute('value'):
                        self.logger.info("✅ Solution verified in response field")
                        return True
                    else:
                        self.logger.warning("⚠️ Solution may not have been applied correctly")
                        return True  # Still return True as we got a solution
                except Exception as e:
                    self.logger.debug(f"Could not verify solution application: {e}")
                    return True
                    
            else:
                self.logger.error("❌ Failed to solve captcha")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in solve_gmx_captcha: {e}")
            return False
    
    def handle_gmx_registration_step_with_captcha(self):
        """Handle a GMX registration step that might have a captcha."""
        try:
            # Wait for page to load
            sleep(2)
            
            # Check if there's a captcha on the page
            self.logger.info("Checking for captcha on registration step...")
            
            # Method 1: Quick check for captcha elements
            captcha_present = False
            try:
                # Check for various captcha indicators
                captcha_indicators = [
                    self.browser.find_elements("css selector", "onereg-captcha"),
                    self.browser.find_elements("xpath", "//onereg-captcha"),
                    self.browser.find_elements("css selector", ".captchafox"),
                    self.browser.find_elements("css selector", 'textarea[name="cf-captcha-response"]')
                ]
                
                for indicator_list in captcha_indicators:
                    if indicator_list:
                        captcha_present = True
                        break
                        
            except Exception as e:
                self.logger.debug(f"Error checking for captcha indicators: {e}")
            
            if captcha_present:
                self.logger.info("🎯 Captcha detected on registration step")
                
                # Try to solve it
                if self.solve_gmx_captcha():
                    self.logger.info("✅ Captcha solved, continuing with registration")
                    return True
                else:
                    self.logger.error("❌ Failed to solve captcha")
                    return False
            else:
                self.logger.info("ℹ️ No captcha detected on this step")
                return True
                
        except Exception as e:
            self.logger.error(f"Error handling registration step with captcha: {e}")
            return False
    '''
    
    return methods_code

def integration_instructions():
    """Instructions for integrating with your GMX creator."""
    
    instructions = """
    INTEGRATION INSTRUCTIONS:
    ========================
    
    1. Add the methods above to your Worker class in gmx.py
    
    2. In your GMX registration flow, add captcha handling:
    
       # Before clicking "Next" or submitting forms:
       if not self.handle_gmx_registration_step_with_captcha():
           self.logger.error("Captcha handling failed")
           return False
    
    3. For debugging, you can call:
       self.debug_captcha_detection()
    
    4. The methods will:
       - Automatically detect GMX captchas
       - Log detailed debug information
       - Solve captchas using 2captcha
       - Apply solutions to the page
       - Verify the solution was applied
    
    TROUBLESHOOTING:
    ===============
    
    If captcha detection fails:
    1. Run debug_captcha_detection() to see what elements are found
    2. Check the logs for detailed information
    3. Verify you're on the correct GMX registration page
    4. Make sure the page has fully loaded before checking for captcha
    
    The debug method will show:
    - All captcha-related elements found
    - Page source analysis
    - Element visibility status
    - Detailed error messages
    """
    
    return instructions

if __name__ == "__main__":
    print("GMX Captcha Integration Code")
    print("=" * 50)
    
    methods = add_captcha_methods_to_worker()
    print("METHODS TO ADD TO YOUR WORKER CLASS:")
    print(methods)
    
    print("\n" + "=" * 50)
    instructions = integration_instructions()
    print(instructions)
