"""
Captcha solver module for handling 2captcha integration with Selenium.
"""
import time
import requests
import logging
import config



#proxy config To change 

class CaptchaSolver:
    """Class to handle captcha solving using 2captcha service."""

    def __init__(self, api_key=None):
        """Initialize the captcha solver.

        Args:
            api_key: 2captcha API key (default from config)
        """
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        self.api_key = api_key
        self.solvecaptcha_api_key = "6d471eb76317bea61bc7fc56bc18d1e6"
        self.max_retries = 3
        self.base_url = "https://api.2captcha.com"

        # Setup logging
        self.logger = logging.getLogger("CaptchaSolver")

    def solve_captcha_fox(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Solve CaptchaFox captcha using 2captcha service.

        Args:
            website_url: URL of the website with captcha
            website_key: Site key for the captcha
            user_agent: User agent string (optional)
            proxy_config: Proxy configuration dict (optional)

        Returns:
            str: Captcha solution token or None if failed
        """
        try:
            self.logger.info("Starting CaptchaFox solving process...")

            # Create task
            task_id = self._create_captcha_task(website_url, website_key, user_agent, proxy_config)
            if not task_id:
                self.logger.error("Failed to create captcha task")
                return None

            # Get solution
            solution = self._get_task_result(task_id)
            if solution:
                self.logger.info("Captcha solved successfully with 2captcha")
                return solution
            else:
                self.logger.warning("2captcha failed, trying solvecaptcha.com fallback...")
                return self._solve_with_solvecaptcha_fallback(website_url, website_key, user_agent, proxy_config)

        except Exception as e:
            self.logger.error(f"Error solving captcha: {e}")
            return None




    def _create_captcha_task(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Create a captcha solving task.

        Returns:
            str: Task ID or None if failed
        """
        try:
            # Prepare task data
            task_data = {
                "clientKey": self.api_key,
                "task": {
                    "type": "CaptchaFoxTask",
                    "websiteURL": website_url,
                    "websiteKey": website_key
                }
            }

            # Add user agent if provided
            if user_agent:
                task_data["task"]["userAgent"] = user_agent

            if proxy_config:
                task_data["task"].update({
                    "proxyType": proxy_config.get("type", "http"),
                    "proxyAddress": proxy_config.get("address", "*************"),
                    "proxyPort": proxy_config.get("port", "12323"),
                    "proxyLogin": proxy_config.get("username", "14a638c2105fb"),
                    "proxyPassword": proxy_config.get("password", "10ef8e713c")
                })


            # Send request
            response = requests.post(
                f"{self.base_url}/createTask",
                json=task_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("errorId") == 0:
                    task_id = result.get("taskId")
                    self.logger.info(f"Task created successfully: {task_id}")
                    return task_id
                else:
                    self.logger.error(f"API error: {result.get('errorDescription', 'Unknown error')}")
                    return None
            else:
                self.logger.error(f"HTTP error: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating task: {e}")
            return None
        



    def _get_task_result(self, task_id, max_wait_time=120):
        """Get the result of a captcha solving task.

        Args:
            task_id: Task ID from create task
            max_wait_time: Maximum time to wait in seconds

        Returns:
            str: Solution token or None if failed
        """
        try:
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                # Check task status
                response = requests.post(
                    f"{self.base_url}/getTaskResult",
                    json={
                        "clientKey": self.api_key,
                        "taskId": task_id
                    },
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()

                    if result.get("errorId") == 0:
                        status = result.get("status")

                        if status == "ready":
                            solution = result.get("solution", {}).get("token")
                            if solution:
                                self.logger.info("Captcha solution received")
                                return solution
                            else:
                                self.logger.error("No solution token in response")
                                return None
                        elif status == "processing":
                            self.logger.info("Captcha is being processed, waiting...")
                            time.sleep(5)
                            continue
                        else:
                            self.logger.error(f"Unexpected status: {status}")
                            return None
                    else:
                        self.logger.error(f"API error: {result.get('errorDescription', 'Unknown error')}")
                        return None
                else:
                    self.logger.error(f"HTTP error: {response.status_code}")
                    return None

            self.logger.error("Timeout waiting for captcha solution")
            return None

        except Exception as e:
            self.logger.error(f"Error getting task result: {e}")
            return None

    def _solve_with_solvecaptcha_fallback(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Fallback method using solvecaptcha.com API when 2captcha fails."""
        try:
            self.logger.info("Starting solvecaptcha.com fallback...")

            # Create task with solvecaptcha
            task_id = self._create_solvecaptcha_task(website_url, website_key, user_agent, proxy_config)
            if not task_id:
                self.logger.error("Failed to create solvecaptcha task")
                return None

            # Get solution from solvecaptcha
            solution = self._get_solvecaptcha_result(task_id)
            if solution:
                self.logger.info("Captcha solved successfully with solvecaptcha.com")
                return solution
            else:
                self.logger.error("Failed to get solvecaptcha solution")
                return None

        except Exception as e:
            self.logger.error(f"Error in solvecaptcha fallback: {e}")
            return None

    def _create_solvecaptcha_task(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Create a captcha solving task with solvecaptcha.com."""
        try:
            # Prepare form data
            form_data = {
                "key": self.solvecaptcha_api_key,
                "method": "captchafox",
                "sitekey": website_key,
                "pageurl": website_url,
                "json": "1"
            }

            # Add user agent if provided
            if user_agent:
                form_data["useragent"] = user_agent

            # Add proxy configuration if provided
            if proxy_config:
                # Convert proxy config to solvecaptcha format: login:password@ip:port
                proxy_string = f"{proxy_config.get('username', '')}:{proxy_config.get('password', '')}@{proxy_config.get('address', '')}:{proxy_config.get('port', '')}"
                form_data["proxy"] = proxy_string
                form_data["proxytype"] = proxy_config.get("type", "http").upper()

            # Send request
            response = requests.post(
                "https://api.solvecaptcha.com/in.php",
                data=form_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("status") == 1:
                    task_id = result.get("request")
                    self.logger.info(f"Solvecaptcha task created successfully: {task_id}")
                    return task_id
                else:
                    self.logger.error(f"Solvecaptcha API error: {result.get('request', 'Unknown error')}")
                    return None
            else:
                self.logger.error(f"Solvecaptcha HTTP error: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating solvecaptcha task: {e}")
            return None

    def _get_solvecaptcha_result(self, task_id, max_wait_time=120):
        """Get the result of a solvecaptcha task."""
        try:
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                # Check task status
                form_data = {
                    "key": self.solvecaptcha_api_key,
                    "action": "get",
                    "id": task_id,
                    "json": "1"
                }

                response = requests.post(
                    "https://api.solvecaptcha.com/res.php",
                    data=form_data,
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()

                    if result.get("status") == 1:
                        solution = result.get("request")
                        if solution:
                            self.logger.info("Solvecaptcha solution received")
                            return solution
                        else:
                            self.logger.error("No solution in solvecaptcha response")
                            return None
                    elif result.get("request") == "CAPCHA_NOT_READY":
                        self.logger.info("Solvecaptcha is processing, waiting...")
                        time.sleep(5)
                        continue
                    else:
                        self.logger.error(f"Solvecaptcha error: {result.get('request', 'Unknown error')}")
                        return None
                else:
                    self.logger.error(f"Solvecaptcha HTTP error: {response.status_code}")
                    return None

            self.logger.error("Timeout waiting for solvecaptcha solution")
            return None

        except Exception as e:
            self.logger.error(f"Error getting solvecaptcha result: {e}")
            return None



    def _detect_captcha_from_source(self, driver):
        """Detect captcha by analyzing page source when element detection fails."""
        try:

            # Check for GMX captcha indicators in page source
            captcha_indicators = [
                '//onereg-captcha',
                '//captchafox',
                '//textarea[@name="cf-captcha-response"]'
            ]
            used_selector = None
            for selector in captcha_indicators:
                try:
                    element = driver.find_xpath_silent(selector)
                    used_selector = selector
                    break
                except Exception as e:
                    print(str(e))
                    try:
                        element = driver.find_xpath(selector)
                        used_selector = selector
                        break
                    except Exception as e:
                        print(str(e))
                        element = None


            if element:
                self.logger.info(f"Found captcha indicators in page source: {element}")

                # Create basic captcha info
                captcha_info = {
                    'type': 'captchafox',
                    'website_url': driver.this_url(),
                    'website_key': "sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w",  # Fallback key
                    'detected_from': 'captcha_xpath',
                    'indicators': used_selector is not None
                }

                # Try to extract actual site key from source
                site_key = "sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w"
                if site_key:
                    captcha_info['website_key'] = site_key

                return captcha_info

            return None

        except Exception as e:
            self.logger.error(f"Error detecting captcha from source: {e}")
            return None



    def _extract_captcha_info(self, driver, captcha_element):
        """Extract captcha information from the detected element.

        Args:
            driver: Selenium WebDriver instance
            captcha_element: WebElement containing captcha

        Returns:
            dict: Captcha information
        """
        try:
            captcha_info = {
                'type': 'captchafox',
                'website_url': driver.this_url(),
                'website_key': "sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w",
                'element': captcha_element
            }


            # Look for the response textarea to confirm it's CaptchaFox
            try:
                response_textarea = driver.find_elements("css selector", 'textarea[name="cf-captcha-response"]')
                if response_textarea:
                    captcha_info['response_element'] = response_textarea[0]
                    captcha_info['response_element_id'] = response_textarea[0].get_attribute('id')
                    self.logger.info(f"Found CaptchaFox response textarea: {captcha_info['response_element_id']}")
            except Exception as e:
                self.logger.debug(f"Could not find response textarea: {e}")

            self.logger.info(f"Extracted captcha info: {captcha_info}")
            return captcha_info

        except Exception as e:
            self.logger.error(f"Error extracting captcha info: {e}")
            return None



    def solve_page_captcha(self, driver, proxy_config=None):
        """Detect and solve any captcha on the current page.

        Args:
            driver: Selenium WebDriver instance
            user_agent: User agent string (optional)
            proxy_config: Proxy configuration dict (optional)

        Returns:
            str: Captcha solution token or None if no captcha or failed
        """
        try:
            # Detect captcha on page
            captcha_info = self._detect_captcha_from_source(driver)
            if not captcha_info:
                self.logger.info("No captcha found on page")
                return None

            if not captcha_info.get('website_key'):
                self.logger.error("No website key found for captcha")
                return None
            
            # Solve the captcha
            solution = self.solve_captcha_fox(
                website_url=captcha_info['website_url'],
                website_key=captcha_info['website_key'],
                user_agent=driver.execute_js("return navigator.userAgent;"),
                proxy_config=proxy_config
            )
            print(solution)
            if solution:
                # Apply the solution to the page
                success = self._apply_captcha_solution(driver, solution)
                if success:
                    self.logger.info("Captcha solved and applied successfully")
                    return solution
                else:
                    self.logger.error("Failed to apply captcha solution")
                    return None
            else:
                self.logger.error("Failed to solve captcha")
                return None

        except Exception as e:
            self.logger.error(f"Error solving page captcha: {e}")
            return None




    def _apply_captcha_solution(self, driver, solution):
        """Apply the captcha solution to the CaptchaFox widget with comprehensive fallback methods."""
        try:
            self.logger.info(f"Applying CaptchaFox solution: {solution[:20]}...")
            
            success_methods = []
            
            # Method 1: Set the solution directly in the response textarea
            try:
                driver.execute_script(f"""
                    var responseField = document.querySelector('textarea[name="cf-captcha-response"]');
                    if (responseField) {{
                        responseField.value = '{solution}';
                        responseField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        responseField.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        console.log('CaptchaFox: Set solution in response field');
                    }}
                """)
                success_methods.append("response_textarea")
                self.logger.info("✅ Method 1: Response textarea updated")
            except Exception as e:
                self.logger.debug(f"Method 1 failed: {e}")
            
            # Method 2: Use CaptchaFox API to set the response
            try:
                widget_info = driver.execute_script("""
                    var widgets = document.querySelectorAll('[id^="cf-widget-"]');
                    var widgetIds = [];
                    for (var i = 0; i < widgets.length; i++) {
                        widgetIds.push(widgets[i].id);
                    }
                    return {
                        widgetIds: widgetIds,
                        hasCaptchafox: typeof window.captchafox !== 'undefined',
                        hasWapi: typeof window.__cf_wapi !== 'undefined'
                    };
                """)
                
                if widget_info.get('hasCaptchafox'):
                    driver.execute_script(f"""
                        try {{
                            if (window.captchafox && window.captchafox.execute) {{
                                var widget = document.querySelector('[id^="cf-widget-"]');
                                if (widget) {{
                                    var widgetId = widget.id;
                                    window.captchafox.execute(widgetId, '{solution}');
                                    console.log('CaptchaFox: Called execute with solution');
                                }}
                            }}
                            
                            if (window.__cf_wapi) {{
                                window.__cf_wapi('{solution}');
                                console.log('CaptchaFox: Called __cf_wapi with solution');
                            }}
                            
                        }} catch (e) {{
                            console.log('CaptchaFox API error:', e);
                        }}
                    """)
                    success_methods.append("captchafox_api")
                    self.logger.info("✅ Method 2: CaptchaFox API called")
                
            except Exception as e:
                self.logger.debug(f"Method 2 failed: {e}")
            
            # Method 3: Comprehensive checkbox manipulation (your code integrated)
            try:
                driver.execute_script(f"""
                    try {{
                        console.log('CaptchaFox: Starting comprehensive checkbox manipulation...');
                        
                        // Your enhanced checkbox selectors
                        const checkboxSelectors = [
                            '.cf-checkbox',
                            '.cf-checkbox-circle',
                            '[role="checkbox"][aria-labelledby^="cf"]',
                            '[data-testid="cf-checkbox"]',
                            'input[type="checkbox"][id*="captcha"]',
                            'input[type="checkbox"][class*="captcha"]',
                            '[role="checkbox"]'
                        ];
                        
                        const checkboxElements = document.querySelectorAll(checkboxSelectors.join(', '));
                        console.log(`Found ${{checkboxElements.length}} potential checkbox elements`);
                        
                        for (const checkbox of checkboxElements) {{
                            // Set aria-checked to true
                            checkbox.setAttribute('aria-checked', 'true');
                            
                            // Handle actual input checkboxes
                            if (checkbox.tagName === 'INPUT' && checkbox.type === 'checkbox') {{
                                checkbox.checked = true;
                            }}
                            
                            // Add visual classes
                            if (checkbox.classList) {{
                                checkbox.classList.add('checked', 'cf-checked');
                                checkbox.classList.remove('false'); // Remove the 'false' class
                            }}
                            
                            // Simulate mouse events
                            const mouseEvents = [
                                new MouseEvent('mousedown', {{ bubbles: true, cancelable: true }}),
                                new MouseEvent('mouseup', {{ bubbles: true, cancelable: true }}),
                                new MouseEvent('click', {{ bubbles: true, cancelable: true }})
                            ];
                            
                            mouseEvents.forEach(event => checkbox.dispatchEvent(event));
                            
                            // Trigger form events
                            ['change', 'input', 'blur', 'focus'].forEach(eventType => {{
                                checkbox.dispatchEvent(new Event(eventType, {{ bubbles: true, cancelable: true }}));
                            }});
                        }}
                        
                        // Update button and widget visual state
                        var button = document.querySelector('.cf-button');
                        if (button) {{
                            button.classList.add('cf-success');
                            console.log('CaptchaFox: Updated button state');
                        }}
                        
                        var widget = document.querySelector('[id^="cf-widget-"]');
                        if (widget) {{
                            widget.classList.add('cf-success');
                            console.log('CaptchaFox: Updated widget state');
                        }}
                        
                    }} catch (e) {{
                        console.log('CaptchaFox checkbox manipulation error:', e);
                    }}
                """)
                success_methods.append("checkbox_manipulation")
                self.logger.info("✅ Method 3: Comprehensive checkbox manipulation completed")
            except Exception as e:
                self.logger.debug(f"Method 3 failed: {e}")
            
            # Method 4: Enhanced event triggering (your code integrated)
            try:
                driver.execute_script(f"""
                    try {{
                        console.log('CaptchaFox: Starting enhanced event triggering...');
                        const token = '{solution}';
                        
                        // Your comprehensive event types
                        const eventTypes = [
                            'captchafox:solved',
                            'captchafox:verified',
                            'captchaFox:success',
                            'cf:verified',
                            'cf:token',
                            'cf:completed',
                            'captcha:solved',
                            'captcha:verified',
                            'captcha:success',
                            'captchaFox:response',
                            'cf:response',
                            // Additional events
                            'captcha-completed',
                            'captcha-verified',
                            'captchafox-completed',
                            'captchafox-success'
                        ];
                        
                        eventTypes.forEach(eventType => {{
                            const event = new CustomEvent(eventType, {{
                                bubbles: true,
                                cancelable: true,
                                detail: {{
                                    token: token,
                                    success: true,
                                    response: token,
                                    verified: true
                                }}
                            }});
                            
                            // Dispatch on both document and window
                            document.dispatchEvent(event);
                            window.dispatchEvent(event);
                            console.log(`Dispatched event: ${{eventType}}`);
                        }});
                        
                        // Try to trigger on the form and widget specifically
                        var form = document.querySelector('form');
                        if (form) {{
                            var formEvent = new CustomEvent('captcha-completed', {{
                                detail: {{ token: token }},
                                bubbles: true
                            }});
                            form.dispatchEvent(formEvent);
                            console.log('CaptchaFox: Triggered form-specific events');
                        }}
                        
                        var widget = document.querySelector('[id^="cf-widget-"]');
                        if (widget) {{
                            var widgetEvent = new CustomEvent('cf:completed', {{
                                detail: {{ token: token }},
                                bubbles: true
                            }});
                            widget.dispatchEvent(widgetEvent);
                            console.log('CaptchaFox: Triggered widget-specific events');
                        }}
                        
                    }} catch (e) {{
                        console.error('Error dispatching events:', e);
                    }}
                """)
                success_methods.append("enhanced_events")
                self.logger.info("✅ Method 4: Enhanced event triggering completed")
            except Exception as e:
                self.logger.debug(f"Method 4 failed: {e}")
            
            # Method 5: Final state verification and cleanup
            try:
                driver.execute_script(f"""
                    try {{
                        // Ensure the response field has the token
                        var responseField = document.querySelector('textarea[name="cf-captcha-response"]');
                        if (responseField && !responseField.value) {{
                            responseField.value = '{solution}';
                        }}
                        
                        // Final visual state update
                        var checkbox = document.querySelector('.cf-checkbox');
                        if (checkbox) {{
                            checkbox.setAttribute('aria-checked', 'true');
                            checkbox.classList.remove('false');
                            checkbox.classList.add('cf-success');
                        }}
                        
                        // Update accessibility prompt
                        var prompt = document.querySelector('#cf-a11y-prompt');
                        if (prompt) {{
                            prompt.textContent = 'CaptchaFox checkbox is checked. You are a human.';
                            prompt.style.display = 'none';
                        }}
                        
                        console.log('CaptchaFox: Final state verification completed');
                        
                    }} catch (e) {{
                        console.log('CaptchaFox final state error:', e);
                    }}
                """)
                success_methods.append("final_state")
                self.logger.info("✅ Method 5: Final state verification completed")
            except Exception as e:
                self.logger.debug(f"Method 5 failed: {e}")
            
            # Wait a moment for all events to process
            import time
            time.sleep(2)
            
            # Comprehensive verification
            try:
                verification = driver.execute_script("""
                    var responseField = document.querySelector('textarea[name="cf-captcha-response"]');
                    var checkbox = document.querySelector('.cf-checkbox');
                    var button = document.querySelector('.cf-button');
                    var widget = document.querySelector('[id^="cf-widget-"]');
                    
                    return {
                        responseValue: responseField ? responseField.value : null,
                        responseLength: responseField ? responseField.value.length : 0,
                        checkboxChecked: checkbox ? checkbox.getAttribute('aria-checked') : null,
                        checkboxHasSuccess: checkbox ? checkbox.classList.contains('cf-success') : false,
                        buttonHasSuccess: button ? button.classList.contains('cf-success') : false,
                        widgetHasSuccess: widget ? widget.classList.contains('cf-success') : false,
                        checkboxClasses: checkbox ? Array.from(checkbox.classList) : [],
                        buttonClasses: button ? Array.from(button.classList) : []
                    };
                """)
                
                self.logger.info(f"Comprehensive verification: {verification}")
                
                # Determine success based on multiple criteria
                response_value = verification.get('responseValue', '')
                response_length = verification.get('responseLength', 0)
                checkbox_checked = verification.get('checkboxChecked') == 'true'
                has_success_indicators = (
                    verification.get('checkboxHasSuccess') or 
                    verification.get('buttonHasSuccess') or 
                    verification.get('widgetHasSuccess')
                )
                
                success = (response_length > 10) or checkbox_checked or has_success_indicators
                
                if success:
                    self.logger.info(f"✅ CaptchaFox solution applied successfully!")
                    self.logger.info(f"   Response field: {response_length} chars")
                    self.logger.info(f"   Checkbox checked: {checkbox_checked}")
                    self.logger.info(f"   Success indicators: {has_success_indicators}")
                    self.logger.info(f"   Methods used: {success_methods}")
                    return True
                else:
                    self.logger.warning(f"⚠️ Solution application uncertain")
                    self.logger.warning(f"   Response field: {response_length} chars")
                    self.logger.warning(f"   Checkbox state: {checkbox_checked}")
                    self.logger.warning(f"   Methods attempted: {success_methods}")
                    return len(success_methods) >= 3  # Return True if most methods succeeded
                    
            except Exception as e:
                self.logger.debug(f"Verification failed: {e}")
                return len(success_methods) > 0
            
        except Exception as e:
            self.logger.error(f"Error applying CaptchaFox solution: {e}")
            return False
