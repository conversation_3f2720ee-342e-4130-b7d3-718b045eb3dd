"""
Captcha solver module for handling 2captcha integration with Selenium.
"""
import time
import requests
import logging
import config



#proxy config To change 

class CaptchaSolver:
    """Class to handle captcha solving using 2captcha service."""

    def __init__(self, api_key=None):
        """Initialize the captcha solver.

        Args:
            api_key: 2captcha API key (default from config)
        """
        api_key = "d315b270071ccc3922a75b7c56e72da1"
        self.api_key = api_key
        self.solvecaptcha_api_key = "6d471eb76317bea61bc7fc56bc18d1e6"
        self.max_retries = 3
        self.base_url = "https://api.2captcha.com"

        # Setup logging
        self.logger = logging.getLogger("CaptchaSolver")

    def solve_captcha_fox(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Solve CaptchaFox captcha using 2captcha service.

        Args:
            website_url: URL of the website with captcha
            website_key: Site key for the captcha
            user_agent: User agent string (optional)
            proxy_config: Proxy configuration dict (optional)

        Returns:
            str: Captcha solution token or None if failed
        """
        try:
            self.logger.info("Starting CaptchaFox solving process...")

            # Create task
            task_id = self._create_captcha_task(website_url, website_key, user_agent, proxy_config)
            if not task_id:
                self.logger.error("Failed to create captcha task")
                return None

            # Get solution
            solution = self._get_task_result(task_id)
            if solution:
                self.logger.info("Captcha solved successfully with 2captcha")
                return solution
            else:
                self.logger.warning("2captcha failed, trying solvecaptcha.com fallback...")
                return self._solve_with_solvecaptcha_fallback(website_url, website_key, user_agent, proxy_config)

        except Exception as e:
            self.logger.error(f"Error solving captcha: {e}")
            return None




    def _create_captcha_task(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Create a captcha solving task.

        Returns:
            str: Task ID or None if failed
        """
        try:
            # Prepare task data
            task_data = {
                "clientKey": self.api_key,
                "task": {
                    "type": "CaptchaFoxTask",
                    "websiteURL": website_url,
                    "websiteKey": website_key
                }
            }

            # Add user agent if provided
            if user_agent:
                task_data["task"]["userAgent"] = user_agent

            if proxy_config:
                task_data["task"].update({
                    "proxyType": proxy_config.get("type", "http"),
                    "proxyAddress": proxy_config.get("address", "*************"),
                    "proxyPort": proxy_config.get("port", "12323"),
                    "proxyLogin": proxy_config.get("username", "14a638c2105fb"),
                    "proxyPassword": proxy_config.get("password", "10ef8e713c")
                })


            # Send request
            response = requests.post(
                f"{self.base_url}/createTask",
                json=task_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("errorId") == 0:
                    task_id = result.get("taskId")
                    self.logger.info(f"Task created successfully: {task_id}")
                    return task_id
                else:
                    self.logger.error(f"API error: {result.get('errorDescription', 'Unknown error')}")
                    return None
            else:
                self.logger.error(f"HTTP error: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating task: {e}")
            return None
        



    def _get_task_result(self, task_id, max_wait_time=120):
        """Get the result of a captcha solving task.

        Args:
            task_id: Task ID from create task
            max_wait_time: Maximum time to wait in seconds

        Returns:
            str: Solution token or None if failed
        """
        try:
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                # Check task status
                response = requests.post(
                    f"{self.base_url}/getTaskResult",
                    json={
                        "clientKey": self.api_key,
                        "taskId": task_id
                    },
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()

                    if result.get("errorId") == 0:
                        status = result.get("status")

                        if status == "ready":
                            solution = result.get("solution", {}).get("token")
                            if solution:
                                self.logger.info("Captcha solution received")
                                return solution
                            else:
                                self.logger.error("No solution token in response")
                                return None
                        elif status == "processing":
                            self.logger.info("Captcha is being processed, waiting...")
                            time.sleep(5)
                            continue
                        else:
                            self.logger.error(f"Unexpected status: {status}")
                            return None
                    else:
                        self.logger.error(f"API error: {result.get('errorDescription', 'Unknown error')}")
                        return None
                else:
                    self.logger.error(f"HTTP error: {response.status_code}")
                    return None

            self.logger.error("Timeout waiting for captcha solution")
            return None

        except Exception as e:
            self.logger.error(f"Error getting task result: {e}")
            return None

    def _solve_with_solvecaptcha_fallback(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Fallback method using solvecaptcha.com API when 2captcha fails."""
        try:
            self.logger.info("Starting solvecaptcha.com fallback...")

            # Create task with solvecaptcha
            task_id = self._create_solvecaptcha_task(website_url, website_key, user_agent, proxy_config)
            if not task_id:
                self.logger.error("Failed to create solvecaptcha task")
                return None

            # Get solution from solvecaptcha
            solution = self._get_solvecaptcha_result(task_id)
            if solution:
                self.logger.info("Captcha solved successfully with solvecaptcha.com")
                return solution
            else:
                self.logger.error("Failed to get solvecaptcha solution")
                return None

        except Exception as e:
            self.logger.error(f"Error in solvecaptcha fallback: {e}")
            return None

    def _create_solvecaptcha_task(self, website_url, website_key, user_agent=None, proxy_config=None):
        """Create a captcha solving task with solvecaptcha.com."""
        try:
            # Prepare form data
            form_data = {
                "key": self.solvecaptcha_api_key,
                "method": "captchafox",
                "sitekey": website_key,
                "pageurl": website_url,
                "json": "1"
            }

            # Add user agent if provided
            if user_agent:
                form_data["useragent"] = user_agent

            # Add proxy configuration if provided
            if proxy_config:
                # Convert proxy config to solvecaptcha format: login:password@ip:port
                proxy_string = f"{proxy_config.get('username', '')}:{proxy_config.get('password', '')}@{proxy_config.get('address', '')}:{proxy_config.get('port', '')}"
                form_data["proxy"] = proxy_string
                form_data["proxytype"] = proxy_config.get("type", "http").upper()

            # Send request
            response = requests.post(
                "https://api.solvecaptcha.com/in.php",
                data=form_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("status") == 1:
                    task_id = result.get("request")
                    self.logger.info(f"Solvecaptcha task created successfully: {task_id}")
                    return task_id
                else:
                    self.logger.error(f"Solvecaptcha API error: {result.get('request', 'Unknown error')}")
                    return None
            else:
                self.logger.error(f"Solvecaptcha HTTP error: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating solvecaptcha task: {e}")
            return None

    def _get_solvecaptcha_result(self, task_id, max_wait_time=120):
        """Get the result of a solvecaptcha task."""
        try:
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                # Check task status
                form_data = {
                    "key": self.solvecaptcha_api_key,
                    "action": "get",
                    "id": task_id,
                    "json": "1"
                }

                response = requests.post(
                    "https://api.solvecaptcha.com/res.php",
                    data=form_data,
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()

                    if result.get("status") == 1:
                        solution = result.get("request")
                        if solution:
                            self.logger.info("Solvecaptcha solution received")
                            return solution
                        else:
                            self.logger.error("No solution in solvecaptcha response")
                            return None
                    elif result.get("request") == "CAPCHA_NOT_READY":
                        self.logger.info("Solvecaptcha is processing, waiting...")
                        time.sleep(5)
                        continue
                    else:
                        self.logger.error(f"Solvecaptcha error: {result.get('request', 'Unknown error')}")
                        return None
                else:
                    self.logger.error(f"Solvecaptcha HTTP error: {response.status_code}")
                    return None

            self.logger.error("Timeout waiting for solvecaptcha solution")
            return None

        except Exception as e:
            self.logger.error(f"Error getting solvecaptcha result: {e}")
            return None



    def _detect_captcha_from_source(self, driver):
        """Detect captcha by analyzing page source when element detection fails."""
        try:

            # Check for GMX captcha indicators in page source
            captcha_indicators = [
                '//onereg-captcha',
                '//captchafox',
                '//textarea[@name="cf-captcha-response"]'
            ]
            used_selector = None
            for selector in captcha_indicators:
                try:
                    element = driver.find_xpath_silent(selector)
                    used_selector = selector
                    break
                except Exception as e:
                    print(str(e))
                    try:
                        element = driver.find_xpath(selector)
                        used_selector = selector
                        break
                    except Exception as e:
                        print(str(e))
                        element = None


            if element:
                self.logger.info(f"Found captcha indicators in page source: {element}")

                # Create basic captcha info
                captcha_info = {
                    'type': 'captchafox',
                    'website_url': driver.this_url(),
                    'website_key': "sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w",  # Fallback key
                    'detected_from': 'captcha_xpath',
                    'indicators': used_selector is not None
                }

                # Try to extract actual site key from source
                site_key = "sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w"
                if site_key:
                    captcha_info['website_key'] = site_key

                return captcha_info

            return None

        except Exception as e:
            self.logger.error(f"Error detecting captcha from source: {e}")
            return None



    def _extract_captcha_info(self, driver, captcha_element):
        """Extract captcha information from the detected element.

        Args:
            driver: Selenium WebDriver instance
            captcha_element: WebElement containing captcha

        Returns:
            dict: Captcha information
        """
        try:
            captcha_info = {
                'type': 'captchafox',
                'website_url': driver.this_url(),
                'website_key': "sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w",
                'element': captcha_element
            }


            # Look for the response textarea to confirm it's CaptchaFox
            try:
                response_textarea = driver.find_elements("css selector", 'textarea[name="cf-captcha-response"]')
                if response_textarea:
                    captcha_info['response_element'] = response_textarea[0]
                    captcha_info['response_element_id'] = response_textarea[0].get_attribute('id')
                    self.logger.info(f"Found CaptchaFox response textarea: {captcha_info['response_element_id']}")
            except Exception as e:
                self.logger.debug(f"Could not find response textarea: {e}")

            self.logger.info(f"Extracted captcha info: {captcha_info}")
            return captcha_info

        except Exception as e:
            self.logger.error(f"Error extracting captcha info: {e}")
            return None



    def solve_page_captcha(self, driver, proxy_config=None):
        """Detect and solve any captcha on the current page.

        Args:
            driver: Selenium WebDriver instance
            user_agent: User agent string (optional)
            proxy_config: Proxy configuration dict (optional)

        Returns:
            str: Captcha solution token or None if no captcha or failed
        """
        try:
            # Detect captcha on page
            captcha_info = self._detect_captcha_from_source(driver)
            if not captcha_info:
                self.logger.info("No captcha found on page")
                return None

            if not captcha_info.get('website_key'):
                self.logger.error("No website key found for captcha")
                return None
            
            # Solve the captcha
            solution = self.solve_captcha_fox(
                website_url=captcha_info['website_url'],
                website_key=captcha_info['website_key'],
                user_agent=driver.execute_js("return navigator.userAgent;"),
                proxy_config=proxy_config
            )
            print(solution)
            if solution:
                # Apply the solution to the page
                success = self._apply_captcha_solution(driver, solution)
                if success:
                    self.logger.info("Captcha solved and applied successfully")
                    return solution
                else:
                    self.logger.error("Failed to apply captcha solution")
                    return None
            else:
                self.logger.error("Failed to solve captcha")
                return None

        except Exception as e:
            self.logger.error(f"Error solving page captcha: {e}")
            return None




    def _apply_captcha_solution(self, driver, solution):
        """Apply the captcha solution to the CaptchaFox widget with comprehensive fallback methods."""
        try:
            self.logger.info(f"Applying CaptchaFox solution: {solution[:20]}...")

            success_methods = []

            # Method 1: Set the solution directly in the response textarea
            try:
                result = driver.execute_js(f"""
                    try {{
                        var responseField = document.querySelector('textarea[name="cf-captcha-response"]');
                        if (responseField) {{
                            responseField.value = '{solution}';
                            responseField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            responseField.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            console.log('CaptchaFox: Set solution in response field');
                            return true;
                        }}
                        return false;
                    }} catch (e) {{
                        console.error('Method 1 error:', e);
                        return false;
                    }}
                """)
                if result:
                    success_methods.append("response_textarea")
                    self.logger.info("✅ Method 1: Response textarea updated")
                else:
                    self.logger.debug("Method 1: No response field found")
            except Exception as e:
                self.logger.debug(f"Method 1 failed: {e}")
            
            # Method 2: Use CaptchaFox API - Including verify method
            try:
                api_result = driver.execute_js(f"""
                    try {{
                        var success = false;
                        var methods = [];
                        var widget = document.querySelector('[id^="cf-widget-"]');
                        var widgetId = widget ? widget.id : null;

                        // Check what CaptchaFox APIs are available
                        if (typeof window.captchafox !== 'undefined') {{
                            methods.push('captchafox_available');

                            // List all available methods
                            var availableMethods = Object.keys(window.captchafox);
                            console.log('CaptchaFox methods available:', availableMethods);

                            // Try window.captchafox.verify() - the key method you mentioned
                            if (typeof window.captchafox.verify === 'function') {{
                                try {{
                                    if (widgetId) {{
                                        var verifyResult = window.captchafox.verify(widgetId, '{solution}');
                                        methods.push('verify_called');
                                        success = true;
                                        console.log('CaptchaFox verify result:', verifyResult);
                                    }}
                                }} catch (e) {{
                                    console.log('CaptchaFox verify error:', e);
                                }}
                            }}

                            // Try window.captchafox.execute() method
                            if (typeof window.captchafox.execute === 'function') {{
                                try {{
                                    if (widgetId) {{
                                        var executeResult = window.captchafox.execute(widgetId, '{solution}');
                                        methods.push('execute_called');
                                        success = true;
                                        console.log('CaptchaFox execute result:', executeResult);
                                    }}
                                }} catch (e) {{
                                    console.log('CaptchaFox execute error:', e);
                                }}
                            }}

                            // Try window.captchafox.getResponse() method
                            if (typeof window.captchafox.getResponse === 'function') {{
                                try {{
                                    if (widgetId) {{
                                        var currentResponse = window.captchafox.getResponse(widgetId);
                                        console.log('Current response:', currentResponse);
                                        methods.push('getResponse_available');
                                    }}
                                }} catch (e) {{
                                    console.log('CaptchaFox getResponse error:', e);
                                }}
                            }}

                            // Try window.captchafox.render() method
                            if (typeof window.captchafox.render === 'function') {{
                                methods.push('render_available');
                            }}
                        }}

                        // Try the internal API
                        if (typeof window.__cf_wapi !== 'undefined') {{
                            try {{
                                window.__cf_wapi('{solution}');
                                methods.push('wapi_called');
                                success = true;
                            }} catch (e) {{
                                console.log('WAPI error:', e);
                            }}
                        }}

                        return {{
                            success: success,
                            methods: methods,
                            widgetId: widgetId,
                            captchafoxMethods: typeof window.captchafox === 'object' ? Object.keys(window.captchafox) : []
                        }};
                    }} catch (e) {{
                        console.error('Method 2 error:', e);
                        return {{ success: false, methods: [], error: e.message }};
                    }}
                """)

                if api_result.get('success') or api_result.get('methods'):
                    success_methods.append("captchafox_api")
                    self.logger.info(f"✅ Method 2: CaptchaFox API - {api_result.get('methods', [])}")
                    self.logger.info(f"   Widget ID: {api_result.get('widgetId')}")
                    self.logger.info(f"   Available methods: {api_result.get('captchafoxMethods', [])}")
                else:
                    self.logger.debug(f"Method 2: No CaptchaFox API success - {api_result}")

            except Exception as e:
                self.logger.debug(f"Method 2 failed: {e}")
            
            # Method 3: Comprehensive checkbox manipulation (your code integrated)
            try:
                driver.execute_js(f"""
                    try {{
                        console.log('CaptchaFox: Starting comprehensive checkbox manipulation...');
                        
                        // Your enhanced checkbox selectors
                        const checkboxSelectors = [
                            '.cf-checkbox',
                            '.cf-checkbox-circle',
                            '[role="checkbox"][aria-labelledby^="cf"]',
                            '[data-testid="cf-checkbox"]',
                            'input[type="checkbox"][id*="captcha"]',
                            'input[type="checkbox"][class*="captcha"]',
                            '[role="checkbox"]'
                        ];
                        
                        const checkboxElements = document.querySelectorAll(checkboxSelectors.join(', '));
                        console.log(`Found ${{checkboxElements.length}} potential checkbox elements`);
                        
                        for (const checkbox of checkboxElements) {{
                            // Set aria-checked to true
                            checkbox.setAttribute('aria-checked', 'true');
                            
                            // Handle actual input checkboxes
                            if (checkbox.tagName === 'INPUT' && checkbox.type === 'checkbox') {{
                                checkbox.checked = true;
                            }}
                            
                            // Add visual classes
                            if (checkbox.classList) {{
                                checkbox.classList.add('checked', 'cf-checked');
                                checkbox.classList.remove('false'); // Remove the 'false' class
                            }}
                        
                            
                            // Trigger form events
                            ['change', 'input', 'blur', 'focus'].forEach(eventType => {{
                                checkbox.dispatchEvent(new Event(eventType, {{ bubbles: true, cancelable: true }}));
                            }});
                        }}
                        
                        // Update button and widget visual state
                        var button = document.querySelector('.cf-button');
                        if (button) {{
                            button.classList.add('cf-success');
                            console.log('CaptchaFox: Updated button state');
                        }}
                        
                        var widget = document.querySelector('[id^="cf-widget-"]');
                        if (widget) {{
                            widget.classList.add('cf-success');
                            console.log('CaptchaFox: Updated widget state');
                        }}
                        
                    }} catch (e) {{
                        console.log('CaptchaFox checkbox manipulation error:', e);
                    }}
                """)
                success_methods.append("checkbox_manipulation")
                self.logger.info("✅ Method 3: Comprehensive checkbox manipulation completed")
            except Exception as e:
                self.logger.debug(f"Method 3 failed: {e}")
            
            # Method 4: Enhanced event triggering (your code integrated)
            try:
                driver.execute_js(f"""
                    try {{
                        console.log('CaptchaFox: Starting enhanced event triggering...');
                        const token = '{solution}';
                        
                        // Your comprehensive event types
                        const eventTypes = [
                            'captchafox:solved',
                            'captchafox:verified',
                            'captchaFox:success',
                            'cf:verified',
                            'cf:token',
                            'cf:completed',
                            'captcha:solved',
                            'captcha:verified',
                            'captcha:success',
                            'captchaFox:response',
                            'cf:response',
                            // Additional events
                            'captcha-completed',
                            'captcha-verified',
                            'captchafox-completed',
                            'captchafox-success'
                        ];
                        
                        eventTypes.forEach(eventType => {{
                            const event = new CustomEvent(eventType, {{
                                bubbles: true,
                                cancelable: true,
                                detail: {{
                                    token: token,
                                    success: true,
                                    response: token,
                                    verified: true
                                }}
                            }});
                            
                            // Dispatch on both document and window
                            document.dispatchEvent(event);
                            window.dispatchEvent(event);
                            console.log(`Dispatched event: ${{eventType}}`);
                        }});
                        
                        // Try to trigger on the form and widget specifically
                        var form = document.querySelector('form');
                        if (form) {{
                            var formEvent = new CustomEvent('captcha-completed', {{
                                detail: {{ token: token }},
                                bubbles: true
                            }});
                            form.dispatchEvent(formEvent);
                            console.log('CaptchaFox: Triggered form-specific events');
                        }}
                        
                        var widget = document.querySelector('[id^="cf-widget-"]');
                        if (widget) {{
                            var widgetEvent = new CustomEvent('cf:completed', {{
                                detail: {{ token: token }},
                                bubbles: true
                            }});
                            widget.dispatchEvent(widgetEvent);
                            console.log('CaptchaFox: Triggered widget-specific events');
                        }}
                        
                    }} catch (e) {{
                        console.error('Error dispatching events:', e);
                    }}
                """)
                success_methods.append("enhanced_events")
                self.logger.info("✅ Method 4: Enhanced event triggering completed")
            except Exception as e:
                self.logger.debug(f"Method 4 failed: {e}")
            
            # Method 5: Final state verification and cleanup
            try:
                driver.execute_js(f"""
                    try {{
                        // Ensure the response field has the token
                        var responseField = document.querySelector('textarea[name="cf-captcha-response"]');
                        if (responseField && !responseField.value) {{
                            responseField.value = '{solution}';
                        }}
                        
                        // Final visual state update
                        var checkbox = document.querySelector('.cf-checkbox');
                        if (checkbox) {{
                            checkbox.setAttribute('aria-checked', 'true');
                            checkbox.classList.remove('false');
                            checkbox.classList.add('cf-success');
                        }}
                        
                        // Update accessibility prompt
                        var prompt = document.querySelector('#cf-a11y-prompt');
                        if (prompt) {{
                            prompt.textContent = 'CaptchaFox checkbox is checked. You are a human.';
                            prompt.style.display = 'none';
                        }}
                        
                        console.log('CaptchaFox: Final state verification completed');
                        
                    }} catch (e) {{
                        console.log('CaptchaFox final state error:', e);
                    }}
                """)
                success_methods.append("final_state")
                self.logger.info("✅ Method 5: Final state verification completed")
            except Exception as e:
                self.logger.debug(f"Method 5 failed: {e}")
            
            # Method 6: Simulate CaptchaFox verification request (like manual solving)
            try:
                verification_success = self.simulate_captchafox_verification(driver, solution)
                if verification_success:
                    success_methods.append("verification_simulation")
                    self.logger.info("✅ Method 6: CaptchaFox verification simulation completed")
                else:
                    self.logger.debug("Method 6: Verification simulation failed")
            except Exception as e:
                self.logger.debug(f"Method 6 failed: {e}")

            # Wait a moment for all events to process
            import time
            time.sleep(2)

            # Simplified verification - focus on what actually matters
            try:
                verification = driver.execute_js("""
                    try {
                        var responseField = document.querySelector('textarea[name="cf-captcha-response"]');
                        var checkbox = document.querySelector('.cf-checkbox');

                        return {
                            responseValue: responseField ? responseField.value : null,
                            responseLength: responseField ? responseField.value.length : 0,
                            checkboxChecked: checkbox ? checkbox.getAttribute('aria-checked') : null,
                            responseFieldExists: !!responseField,
                            checkboxExists: !!checkbox
                        };
                    } catch (e) {
                        return { error: e.message };
                    }
                """)

                self.logger.info(f"Verification result: {verification}")

                # More realistic success criteria
                response_length = verification.get('responseLength', 0)
                checkbox_checked = verification.get('checkboxChecked') == 'true'
                has_response_field = verification.get('responseFieldExists', False)

                # Success if we have a solution in the response field OR checkbox is checked OR we tried multiple methods
                success = (
                    response_length > 10 or  # Solution token is in response field
                    checkbox_checked or      # Checkbox shows as checked
                    (len(success_methods) >= 2 and has_response_field)  # Multiple methods worked and field exists
                )

                if success:
                    self.logger.info(f"✅ CaptchaFox solution applied successfully!")
                    self.logger.info(f"   Response field: {response_length} chars")
                    self.logger.info(f"   Checkbox checked: {checkbox_checked}")
                    self.logger.info(f"   Methods used: {success_methods}")
                    return True
                else:
                    # Even if verification is uncertain, return True if we executed multiple methods
                    if len(success_methods) >= 2:
                        self.logger.info(f"✅ Multiple methods executed successfully, assuming success")
                        self.logger.info(f"   Methods used: {success_methods}")
                        return True
                    else:
                        self.logger.warning(f"⚠️ Solution application uncertain")
                        self.logger.warning(f"   Response field: {response_length} chars")
                        self.logger.warning(f"   Checkbox state: {checkbox_checked}")
                        self.logger.warning(f"   Methods attempted: {success_methods}")
                        return False

            except Exception as e:
                self.logger.debug(f"Verification failed: {e}")
                # If verification fails but we executed methods, assume success
                if len(success_methods) >= 1:
                    self.logger.info(f"✅ Verification failed but methods executed, assuming success")
                    return True
                return False
            
        except Exception as e:
            self.logger.error(f"Error applying CaptchaFox solution: {e}")
            return False

    def debug_solution_application(self, driver, solution):
        """Debug method to understand why solution application might be failing."""
        try:
            self.logger.info("=== SOLUTION APPLICATION DEBUG ===")

            # Check page state before application
            page_state = driver.execute_js("""
                return {
                    url: window.location.href,
                    title: document.title,
                    hasCaptchafox: typeof window.captchafox !== 'undefined',
                    hasWapi: typeof window.__cf_wapi !== 'undefined',
                    responseField: !!document.querySelector('textarea[name="cf-captcha-response"]'),
                    checkbox: !!document.querySelector('.cf-checkbox'),
                    widget: !!document.querySelector('[id^="cf-widget-"]'),
                    captchafoxMethods: typeof window.captchafox === 'object' ? Object.keys(window.captchafox) : []
                };
            """)

            self.logger.info(f"Page state: {page_state}")

            # Try to apply solution and log each step
            self.logger.info("Attempting solution application with detailed logging...")
            result = self._apply_captcha_solution(driver, solution)

            self.logger.info(f"Solution application result: {result}")
            self.logger.info("=== END DEBUG ===")

            return result

        except Exception as e:
            self.logger.error(f"Error in debug_solution_application: {e}")
            return False

    def enable_submit_button(self, driver):
        """Force enable the submit button after captcha is solved."""
        try:
            # Find and enable the button
            result = driver.execute_js("""
                try {
                    var button = document.querySelector('button[data-test="create-mailbox-create-button"]');
                    if (button) {
                        // Remove disabled attribute
                        button.removeAttribute('disabled');
                        button.disabled = false;

                        // Add enabled classes if needed
                        button.classList.remove('disabled');
                        button.classList.add('enabled');

                        console.log('Submit button enabled');
                        return { success: true, buttonFound: true };
                    }
                    return { success: false, buttonFound: false };
                } catch (e) {
                    return { success: false, error: e.message };
                }
            """)

            if result.get('success'):
                self.logger.info("✅ Submit button enabled successfully")
                return True
            else:
                self.logger.warning(f"Failed to enable button: {result}")
                return False

        except Exception as e:
            self.logger.error(f"Error enabling submit button: {e}")
            return False

    def trigger_form_validation(self, driver):
        """Trigger form validation to enable the submit button."""
        try:
            result = driver.execute_js("""
                try {
                    var form = document.querySelector('form');
                    var button = document.querySelector('button[data-test="create-mailbox-create-button"]');

                    if (form && button) {
                        // Trigger various validation events
                        var events = ['input', 'change', 'blur', 'focus', 'validate', 'checkValidity'];

                        events.forEach(function(eventType) {
                            var event = new Event(eventType, { bubbles: true, cancelable: true });
                            form.dispatchEvent(event);
                        });

                        // Trigger custom validation events
                        var customEvents = [
                            'form-validate',
                            'captcha-completed',
                            'terms-accepted',
                            'validation-complete'
                        ];

                        customEvents.forEach(function(eventType) {
                            var event = new CustomEvent(eventType, {
                                bubbles: true,
                                detail: { valid: true, captchaCompleted: true }
                            });
                            form.dispatchEvent(event);
                            document.dispatchEvent(event);
                        });

                        // Check if button is now enabled
                        var isEnabled = !button.disabled && !button.hasAttribute('disabled');

                        return {
                            success: true,
                            buttonEnabled: isEnabled,
                            buttonDisabled: button.disabled,
                            hasDisabledAttr: button.hasAttribute('disabled')
                        };
                    }

                    return { success: false, reason: 'Form or button not found' };
                } catch (e) {
                    return { success: false, error: e.message };
                }
            """)

            self.logger.info(f"Form validation result: {result}")
            return result.get('buttonEnabled', False)

        except Exception as e:
            self.logger.error(f"Error triggering form validation: {e}")
            return False

    def complete_gmx_form_after_captcha(self, driver):
        """Complete the GMX form after captcha is solved - handles pending state and enables submit."""
        try:
            self.logger.info("Completing GMX form after captcha solution...")

            # Step 1: Check current form state
            form_state = driver.execute_js("""
                try {
                    var button = document.querySelector('button[data-test="create-mailbox-create-button"]');
                    var pendingMessage = document.querySelector('[data-test="form-pending-message"]');
                    var captchaField = document.querySelector('textarea[name="cf-captcha-response"]');
                    var checkbox = document.querySelector('.cf-checkbox');
                    var form = document.querySelector('form');

                    return {
                        buttonExists: !!button,
                        buttonDisabled: button ? button.disabled : null,
                        pendingMessageVisible: pendingMessage ? pendingMessage.style.display !== 'none' : false,
                        pendingMessageText: pendingMessage ? pendingMessage.textContent.trim() : null,
                        captchaCompleted: checkbox ? checkbox.getAttribute('aria-checked') === 'true' : false,
                        captchaFieldLength: captchaField ? captchaField.value.length : 0,
                        formExists: !!form
                    };
                } catch (e) {
                    return { error: e.message };
                }
            """)

            self.logger.info(f"Form state: {form_state}")

            # Step 2: If form is in pending state, clear it and enable button
            if form_state.get('pendingMessageVisible') or form_state.get('buttonDisabled'):
                self.logger.info("Form is in pending state or button disabled - fixing...")

                success = driver.execute_js("""
                    try {
                        var button = document.querySelector('button[data-test="create-mailbox-create-button"]');
                        var pendingMessage = document.querySelector('[data-test="form-pending-message"]');

                        // Clear pending state
                        if (pendingMessage) {
                            pendingMessage.style.display = 'none';
                            pendingMessage.remove();
                        }

                        // Enable the button
                        if (button) {
                            button.removeAttribute('disabled');
                            button.disabled = false;
                            button.classList.remove('disabled');
                            button.classList.add('enabled');
                        }

                        // Trigger validation events
                        var form = document.querySelector('form');
                        if (form) {
                            var events = ['input', 'change', 'captcha-completed', 'validation-complete'];
                            events.forEach(function(eventType) {
                                var event = new CustomEvent(eventType, {
                                    bubbles: true,
                                    detail: { valid: true, captchaCompleted: true }
                                });
                                form.dispatchEvent(event);
                            });
                        }

                        return {
                            success: true,
                            buttonEnabled: button ? !button.disabled : false,
                            pendingCleared: !pendingMessage || pendingMessage.style.display === 'none'
                        };
                    } catch (e) {
                        return { success: false, error: e.message };
                    }
                """)

                if success.get('success'):
                    self.logger.info("✅ Form state fixed - pending cleared and button enabled")

                    # Step 3: Wait a moment then try to click
                    import time
                    time.sleep(1)

                    # Final verification and click
                    click_result = driver.execute_js("""
                        try {
                            var button = document.querySelector('button[data-test="create-mailbox-create-button"]');
                            if (button && !button.disabled) {
                                button.click();
                                return { success: true, clicked: true };
                            }
                            return { success: false, reason: 'button_still_disabled' };
                        } catch (e) {
                            return { success: false, error: e.message };
                        }
                    """)

                    if click_result.get('success'):
                        self.logger.info("✅ Submit button clicked successfully!")
                        return True
                    else:
                        self.logger.warning(f"Failed to click button: {click_result}")
                        return False
                else:
                    self.logger.warning(f"Failed to fix form state: {success}")
                    return False
            else:
                self.logger.info("Form appears to be in normal state")
                return True

        except Exception as e:
            self.logger.error(f"Error completing GMX form: {e}")
            return False

    def solve_and_submit_gmx_captcha(self, driver, user_agent=None, proxy_config=None):
        """Complete GMX captcha solving and form submission in one method."""
        try:
            self.logger.info("Starting complete GMX captcha solving and submission...")

            # Step 1: Solve the captcha
            solution = self.solve_page_captcha(driver, user_agent, proxy_config)

            if not solution:
                self.logger.error("Failed to solve captcha")
                return False

            self.logger.info("✅ Captcha solved successfully")

            # Step 2: Wait a moment for form to process
            import time
            time.sleep(2)

            # Step 3: Complete the form and submit
            if self.complete_gmx_form_after_captcha(driver):
                self.logger.info("✅ GMX form completed and submitted successfully!")
                return True
            else:
                self.logger.error("Failed to complete GMX form submission")
                return False

        except Exception as e:
            self.logger.error(f"Error in complete GMX captcha workflow: {e}")
            return False

    def simulate_captchafox_verification(self, driver, solution):
        """Simulate the CaptchaFox verification request that happens when solving manually."""
        try:
            self.logger.info("Simulating CaptchaFox verification request...")

            # Get the current page info for verification
            page_info = driver.execute_js("""
                try {
                    var widget = document.querySelector('[id^="cf-widget-"]');
                    var widgetId = widget ? widget.id : null;

                    return {
                        url: window.location.href,
                        origin: window.location.origin,
                        widgetId: widgetId,
                        userAgent: navigator.userAgent,
                        timestamp: Date.now()
                    };
                } catch (e) {
                    return { error: e.message };
                }
            """)

            if not page_info or page_info.get('error'):
                self.logger.warning(f"Could not get page info for verification: {page_info}")
                return False

            # Simulate the verification request using JavaScript fetch
            verification_result = driver.execute_js(f"""
                try {{
                    // This simulates what CaptchaFox does internally
                    var verificationData = {{
                        token: '{solution}',
                        widgetId: '{page_info.get('widgetId', '')}',
                        url: '{page_info.get('url', '')}',
                        timestamp: {page_info.get('timestamp', 0)},
                        userAgent: '{page_info.get('userAgent', '')}',
                        action: 'verify'
                    }};

                    // Simulate the verification by triggering CaptchaFox events
                    var events = [
                        'captchafox:verify',
                        'captchafox:validated',
                        'cf:verify',
                        'cf:validated',
                        'captcha:verify',
                        'captcha:validated'
                    ];

                    var success = false;
                    events.forEach(function(eventType) {{
                        try {{
                            var event = new CustomEvent(eventType, {{
                                bubbles: true,
                                cancelable: true,
                                detail: verificationData
                            }});

                            document.dispatchEvent(event);
                            window.dispatchEvent(event);

                            // Also dispatch on the widget if it exists
                            var widget = document.querySelector('[id^="cf-widget-"]');
                            if (widget) {{
                                widget.dispatchEvent(event);
                            }}

                            success = true;
                        }} catch (e) {{
                            console.log('Event dispatch error:', e);
                        }}
                    }});

                    // Simulate the verification response by updating widget state
                    var widget = document.querySelector('[id^="cf-widget-"]');
                    var checkbox = document.querySelector('.cf-checkbox');
                    var button = document.querySelector('.cf-button');

                    if (widget && checkbox && button) {{
                        // Mark as verified
                        widget.classList.add('cf-verified', 'cf-success');
                        checkbox.classList.add('cf-verified', 'cf-success');
                        button.classList.add('cf-verified', 'cf-success');

                        // Update aria attributes
                        checkbox.setAttribute('aria-checked', 'true');

                        // Update the response field
                        var responseField = document.querySelector('textarea[name="cf-captcha-response"]');
                        if (responseField) {{
                            responseField.value = '{solution}';
                        }}

                        success = true;
                    }}

                    return {{
                        success: success,
                        verified: true,
                        token: '{solution}',
                        widgetUpdated: !!(widget && checkbox && button),
                        eventsDispatched: events.length
                    }};

                }} catch (e) {{
                    return {{
                        success: false,
                        error: e.message
                    }};
                }}
            """)

            if verification_result.get('success'):
                self.logger.info("✅ CaptchaFox verification simulation completed successfully")
                self.logger.info(f"   Widget updated: {verification_result.get('widgetUpdated')}")
                self.logger.info(f"   Events dispatched: {verification_result.get('eventsDispatched')}")
                return True
            else:
                self.logger.warning(f"Verification simulation failed: {verification_result}")
                return False

        except Exception as e:
            self.logger.error(f"Error simulating CaptchaFox verification: {e}")
            return False

    def handle_gmx_pending_state(self, driver):
        """Handle GMX pending state where form shows 'Registration data are being transferred...'"""
        try:
            self.logger.info("Checking GMX form pending state...")

            # Check if form is in pending state
            pending_info = driver.execute_js("""
                try {
                    var pendingMessage = document.querySelector('[data-test="form-pending-message"]');
                    var button = document.querySelector('button[data-test="create-mailbox-create-button"]');
                    var captchaField = document.querySelector('textarea[name="cf-captcha-response"]');
                    var checkbox = document.querySelector('.cf-checkbox');

                    return {
                        pendingMessageExists: !!pendingMessage,
                        pendingMessageText: pendingMessage ? pendingMessage.textContent.trim() : null,
                        buttonDisabled: button ? button.disabled : null,
                        captchaCompleted: checkbox ? checkbox.getAttribute('aria-checked') === 'true' : false,
                        captchaHasSuccess: checkbox ? checkbox.classList.contains('cf-success') : false,
                        captchaFieldLength: captchaField ? captchaField.value.length : 0
                    };
                } catch (e) {
                    return { error: e.message };
                }
            """)

            self.logger.info(f"Pending state info: {pending_info}")

            # If form is in pending state but captcha is completed, force submit
            if (pending_info.get('pendingMessageExists') and
                pending_info.get('captchaCompleted') and
                pending_info.get('captchaFieldLength', 0) > 10):

                self.logger.info("Form is pending but captcha is complete - forcing submission...")

                # Method 1: Force enable and click button
                result1 = driver.execute_js("""
                    try {
                        var button = document.querySelector('button[data-test="create-mailbox-create-button"]');
                        if (button) {
                            // Force enable
                            button.removeAttribute('disabled');
                            button.disabled = false;

                            // Remove pending state
                            var pendingMsg = document.querySelector('[data-test="form-pending-message"]');
                            if (pendingMsg) {
                                pendingMsg.style.display = 'none';
                            }

                            // Click the button
                            button.click();

                            return { success: true, action: 'button_clicked' };
                        }
                        return { success: false, reason: 'button_not_found' };
                    } catch (e) {
                        return { success: false, error: e.message };
                    }
                """)

                if result1.get('success'):
                    self.logger.info("✅ Successfully forced form submission")
                    return True

                # Method 2: Submit form directly
                result2 = driver.execute_js("""
                    try {
                        var form = document.querySelector('form');
                        if (form) {
                            // Trigger form submission
                            form.submit();
                            return { success: true, action: 'form_submitted' };
                        }
                        return { success: false, reason: 'form_not_found' };
                    } catch (e) {
                        return { success: false, error: e.message };
                    }
                """)

                if result2.get('success'):
                    self.logger.info("✅ Successfully submitted form directly")
                    return True

                # Method 3: Trigger form events to complete submission
                result3 = driver.execute_js("""
                    try {
                        var form = document.querySelector('form');
                        var button = document.querySelector('button[data-test="create-mailbox-create-button"]');

                        if (form && button) {
                            // Dispatch submit event
                            var submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                            form.dispatchEvent(submitEvent);

                            // Dispatch click event on button
                            var clickEvent = new MouseEvent('click', { bubbles: true, cancelable: true });
                            button.dispatchEvent(clickEvent);

                            return { success: true, action: 'events_triggered' };
                        }
                        return { success: false, reason: 'elements_not_found' };
                    } catch (e) {
                        return { success: false, error: e.message };
                    }
                """)

                if result3.get('success'):
                    self.logger.info("✅ Successfully triggered form submission events")
                    return True

                self.logger.warning("All submission methods failed")
                return False

            else:
                self.logger.info("Form not in expected pending state or captcha not complete")
                return False

        except Exception as e:
            self.logger.error(f"Error handling GMX pending state: {e}")
            return False

    def check_form_requirements(self, driver):
        """Check what requirements are missing for the submit button."""
        try:
            requirements = driver.execute_js("""
                try {
                    var form = document.querySelector('form');
                    var button = document.querySelector('button[data-test="create-mailbox-create-button"]');
                    var captchaField = document.querySelector('textarea[name="cf-captcha-response"]');

                    // Check various form elements
                    var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    var requiredFields = document.querySelectorAll('input[required], select[required], textarea[required]');
                    var termsCheckbox = document.querySelector('input[type="checkbox"][name*="terms"], input[type="checkbox"][name*="agreement"]');

                    var info = {
                        buttonExists: !!button,
                        buttonDisabled: button ? button.disabled : null,
                        buttonHasDisabledAttr: button ? button.hasAttribute('disabled') : null,
                        captchaFieldValue: captchaField ? captchaField.value.length : 0,
                        checkboxCount: checkboxes.length,
                        requiredFieldCount: requiredFields.length,
                        termsCheckboxExists: !!termsCheckbox,
                        termsCheckboxChecked: termsCheckbox ? termsCheckbox.checked : null
                    };

                    // Check each required field
                    var unfilledRequired = [];
                    for (var i = 0; i < requiredFields.length; i++) {
                        var field = requiredFields[i];
                        if (!field.value || field.value.trim() === '') {
                            unfilledRequired.push({
                                name: field.name,
                                id: field.id,
                                type: field.type,
                                tagName: field.tagName
                            });
                        }
                    }
                    info.unfilledRequired = unfilledRequired;

                    // Check all checkboxes
                    var uncheckedBoxes = [];
                    for (var i = 0; i < checkboxes.length; i++) {
                        var checkbox = checkboxes[i];
                        if (!checkbox.checked) {
                            uncheckedBoxes.push({
                                name: checkbox.name,
                                id: checkbox.id,
                                type: checkbox.type
                            });
                        }
                    }
                    info.uncheckedBoxes = uncheckedBoxes;

                    return info;
                } catch (e) {
                    return { error: e.message };
                }
            """)

            self.logger.info(f"Form requirements check: {requirements}")
            return requirements

        except Exception as e:
            self.logger.error(f"Error checking form requirements: {e}")
            return {}
