@charset "UTF-8";
/* 1200px */
/* 767px */
/* 1199px */
/* 991px */
.a-mr-space-0 {
  margin-right: 0px;
}

.a-mb-space-0 {
  margin-bottom: 0px;
}

.a-mt-space-0 {
  margin-top: 0px;
}

.a-mr-space-1 {
  margin-right: 8px;
}

.a-mb-space-1 {
  margin-bottom: 8px;
}

.a-mt-space-1 {
  margin-top: 8px;
}

.a-mr-space-2 {
  margin-right: 16px;
}

.a-mb-space-2 {
  margin-bottom: 16px;
}

.a-mt-space-2 {
  margin-top: 16px;
}

.a-mr-space-3 {
  margin-right: 24px;
}

.a-mb-space-3 {
  margin-bottom: 24px;
}

.a-mt-space-3 {
  margin-top: 24px;
}

.a-mr-space-4 {
  margin-right: 32px;
}

.a-mb-space-4 {
  margin-bottom: 32px;
}

.a-mt-space-4 {
  margin-top: 32px;
}

.a-mr-space-5 {
  margin-right: 40px;
}

.a-mb-space-5 {
  margin-bottom: 40px;
}

.a-mt-space-5 {
  margin-top: 40px;
}

.a-mr-space-6 {
  margin-right: 48px;
}

.a-mb-space-6 {
  margin-bottom: 48px;
}

.a-mt-space-6 {
  margin-top: 48px;
}

.a-mr-space-7 {
  margin-right: 56px;
}

.a-mb-space-7 {
  margin-bottom: 56px;
}

.a-mt-space-7 {
  margin-top: 56px;
}

.a-mr-space-8 {
  margin-right: 64px;
}

.a-mb-space-8 {
  margin-bottom: 64px;
}

.a-mt-space-8 {
  margin-top: 64px;
}

.a-mr-space-9 {
  margin-right: 72px;
}

.a-mb-space-9 {
  margin-bottom: 72px;
}

.a-mt-space-9 {
  margin-top: 72px;
}

.a-mr-space-10 {
  margin-right: 80px;
}

.a-mb-space-10 {
  margin-bottom: 80px;
}

.a-mt-space-10 {
  margin-top: 80px;
}

.a-tc-r {
  color: #f00 !important;
}

.a-p-8 {
  padding: 8px;
}

/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

[hidden],
template {
  display: none;
}

a {
  background-color: transparent;
}

a:active,
a:hover {
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b,
strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

h1 {
  margin: 0.67em 0;
  font-size: 2em;
}

mark {
  background: #ff0;
  color: #000;
}

small {
  font-size: 12px;
  line-height: 18px;
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #333;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

img {
  border: 0;
}

svg:not(:root) {
  overflow: hidden;
}

figure {
  margin: 1em 40px;
}

hr {
  height: 0;
  box-sizing: content-box;
}

pre {
  overflow: auto;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

button,
input,
optgroup,
select,
textarea {
  margin: 0;
  color: inherit;
  font: inherit;
}

button {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
  cursor: pointer;
  -webkit-appearance: button;
}

button[disabled],
html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}

input {
  line-height: normal;
}

input[type=checkbox],
input[type=radio] {
  padding: 0;
  box-sizing: border-box;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  height: auto;
}

input[type=search] {
  -webkit-appearance: textfield;
  box-sizing: content-box;
}

input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration,
input[type=search]::-webkit-search-results-button,
input[type=search]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

input[type=search]::-ms-clear {
  display: none;
  width: 0;
  height: 0;
}

input[type=search]::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}

fieldset {
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
  border: 1px solid rgb(193.8, 193.8, 193.8);
}

legend {
  padding: 0;
  border: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

/* posdoc
  name: Media Query Mixin
  description: This mixin offers a predefined set of media queries.
  data:
*/
/* posdoc
    pos-scss/tools/media-query.scss:
        '<code>
            <b>Usage:</b>@include media-query($screen-size)<br><br>
            $screen-size can take one of the following values:<br>
            <b>small</b><br>
            only for small screen<br>
            max-width: 767px<br><br>
            <b>medium</b><br>
            only for medium screens<br>
            min-width: 768px and max-width: 1199px<br><br>
            <b>medium-portrait</b><br>
             only for medium screens in portrait mode<br>
             min-width: 768px and max-width: 991px<br><br>
            <b>medium-landscape</b><br>
            only for medium screens in landscape mode<br>
            min-width: 992px and 1199px<br><br>
            <b>large</b><br>
            only for large screens<br>
            min-width: 1200px<br><br>
            <b>small-medium</b><br>
            small and medium screen sizes<br>
            max-width: 1199px<br><br>
            <b>small-medium-portrait</b><br>
            small and medium in portrait mode screen sizes<br>
            max-width: 991px<br><br>
            <b>medium-large</b><br>
            medium and large screen sizes<br>
            min-width: 768px<br><br>
            <b>medium-landscape-large</b><br>
            medium in landscape mode and large screen sizes<br>
            min-width: 992px<br><br>
        </code>'
*/
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

*:focus {
  outline: none;
}

html {
  overflow-x: hidden;
}

body {
  background-color: #fff;
  color: #333;
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 20px;
}

[role=button] {
  cursor: pointer;
}

img {
  vertical-align: middle;
}

.pos-svg {
  width: initial;
  height: initial;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

a,
a:hover,
a:focus,
a.active {
  outline: 0;
  color: #1c449b;
  text-decoration: none;
  cursor: pointer;
}

a:hover {
  text-decoration: underline;
}

hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eee;
}

h5.accessibility {
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

.u-fade-in {
  opacity: 1;
  animation: fadein 0.5s;
}

.u-fade-out {
  opacity: 0;
  animation: fadeout 0.5s;
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeout {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.app-contents {
  overflow: hidden;
}

.app-modal-message-wrapper {
  position: absolute;
  width: inherit;
  height: inherit;
  padding-top: 88px;
  z-index: 100;
}
@media screen and (min-width: 768px) {
  .app-modal-message-wrapper {
    padding-top: 176px;
  }
}

/* posdoc
  name: Flexbox Classes
  description: 'Helper classes to provide users with an easier way to interact with the flexbox layout system.
  <a href="https://css-tricks.com/snippets/css/a-guide-to-flexbox/"> More information on flexbox</a>'
  data:
*/
/* posdoc
    pos-scss/objects/flex.scss:
        '<code>
            <b>.l-inline</b><br>
            Use this class to define a inline flex container which enables
            a flex content for all direct children<br>
            display: inline-flex;<br/><br/>

            <b>.l-horizontal</b><br>
            This class defines a horizontal layout for all children of the flex container<br>
             flex-direction: row;<br><br>

             <b>.l-horizontal.l-reverse</b><br>
             This class defines a reverse horizontal layout (right to left) for all children of the flex container<br>
             flex-direction: row-reverse;<br><br>

             <b>.l-vertical</b><br>
             This class defines a vertical layout for all direct children of the flex container<br>
             flex-direction: column;<br><br>

             <b>.l-vertical.l-reverse</b><br>
             This class defines a reverse vertical layout (bottom to top) for all direct of the flex container<br>
             flex-direction: column-reverse;<br/><br/>

             <b>.l-wrap</b><br>
             Child elements will be flowed into multiple lines in the direction defined by the
             flex-direction property (they will not be forced into a single line)<br>
             flex-wrap: wrap;<br/><br/>

             <b>.l-nowrap</b><br>
             Child elements will not be flowed into multiple lines<br>
             flex-wrap: nowrap;<br/><br/>

             <b> .l-wrap-reverse</b><br>
             Child elements will be flowed into multiple lines in the opposite
             direction defined by the flex-direction property (they will not be forced into a single line)<br>
             flex-wrap: wrap-reverse;<br/><br/>

             <b>.l-flex</b><br>
             Child elements with this class will split the remaining space in the container equally.
             If there is only one element with this class it will take up all the remaining space<br>
             flex: 1;<br/><br/>

             <b>.l-flex-auto</b><br>
             Child elements with this class will split the remaining space in the container equally.
             If there is only one element with this class it will take up all the remaining space.<br>
             flex: 1 1 auto;<br/><br/>

             <b>.l-flex-none</b><br>
             Child elements with this class will not grow to occupy remaining space or shrink to
             accomodate if there are multiple elements that would not fit in the parent<br>
             flex: none;<br/><br/>

             <b>.l-grow: flex-grow: 1;</b><br>
             Child elements with this class will split the remaining space in the container equally.
             If there is only one element with this class it will take up all the remaining space<br>
             flex-grow: 1;<br><br>

             <b>.l-flex-[0-12]</b><br>
             Flex item receives an amount of space inside the flex container
             using this formula: (space/sum of all indexes) * index. So for example if the available
             space is 600px and the only elements have .l-flex classes from 1-12 (one each) the formula for .l-flex-6
             is (600/(1+2+...+12)) * 6.<br>
             flex: 0<br>
             flex: 1<br>
             flex: 2<br>
             flex: 3<br>
             flex: 4<br>
             flex: 5<br>
             flex: 6<br>
             flex: 7<br>
             flex: 8<br>
             flex: 9<br>
             flex: 10<br>
             flex: 11<br>
             flex: 12<br><br><br>

             <b> .l-start-aligned</b><br>
             Aligns flex items to the beginning of the flex container
             depending on the flex-direction. If flex-direction is row, items will be
             aligned to the left, if flex direction is column, items will be aligned to
             the top.<br>
             align-items: flex-start;<br/><br/>

             <b>.l-center-aligned</b><br>
             Aligns flex items to the center of the flex
             container based on the main axis (the one defined by flex-direction).<br>
             align-items: center;<br><br>

             <b>.l-end-aligned</b><br>
             Aligns flex items to the end of the flex container
             depending on the flex-direction. If flex-direction is row, items will be
             aligned to the right, if flex direction is column, items will be aligned to
             the bottom.<br>
             align-items: flex-end;<br><br>

             <b>.l-stretch-aligned</b><br>
             Stretches the elements in the direction defined by flex-direction.
             If flex-direction is row the flex items will be stretched vertically to fit the flex
             container, if the flex-direction is column, items will be stretched horizontally to fit
             the flex container.<br>
             align-items: stretch;<br><br>

             <b>.l-start-justified</b><br>
             Aligns flex items to the beginning of the flex container
             depending on the flex-direction. If flex-direction is row, items will be
             aligned to the top, if flex direction is column, items will be aligned to
             the left.<br>
             justify-content: flex-start;<br><br>

             <b>.l-center-justified</b><br>
             Aligns flex items to the center of the flex
             container based on the secondary axis (the one perpendicular to the one
             defined by flex-direction).<br>
             justify-content: center;<br><br>

             <b>.l-end-justified</b><br>
             Aligns flex items to the end of the flex container
             depending on the flex-direction. If flex-direction is row, items will be
             aligned to the bottom, if flex direction is column, items will be aligned to
             the right.<br>
             justify-content: flex-end;<br><br>

             <b>.l-around-justified</b><br>
             Flex items withing the container are evenly distributed
             in the line with equal space around them, more precisely, the extra space on the line
             is distributed into [number of items] * 2 equal units and each item has a `margin`
             around it on the axis perpendicular to the direction defined by flex-direction.<br>
             justify-content: space-around;<br><br>

             <b>.l-between-justified</b><br>
             Flex items within a line are arranged to
             have even space between them. All the extra space is divided into
             [numer of items - 1] parts and distributed between items.<br>
             justify-content: space-between;<br><br>

             <b> .l-self-start-aligned</b><br>
             Overrides the align-items property of the flex container
             for the current item.<br>
             align-self: flex-start;<br><br>

             <b> .l-self-center-aligned</b><br>
             Overrides the align-items property of the flex container
             for the current item.<br>
             align-self: center;<br><br>

             <b>.l-self-end-aligned</b><br>
             Overrides the align-items property of the flex container
             for the current item.<br>
             align-self: flex-end;<br><br>

             <b>.l-self-stretch-aligned</b><br>
             Overrides the align-items property of the flex container
             for the current item.<br>
             align-self: stretch;<br><br>

             <b>.l-fit</b><br>
             Expands the element to fill up its closest positioned ancestor.<br>
             position: absolute;<br>
             top: 0;<br>
             right: 0;<br>
             bottom: 0;<br>
             left: 0;<br><br>

             <b> .l-relative</b><br>
             Set position: relative.<br>
             position: relative;<br><br>
        </code>'
*/
.l-horizontal,
.l-vertical {
  display: flex;
}

.l-inline {
  display: inline-flex;
}

.l-horizontal {
  flex-direction: row;
}

.l-horizontal.l-reverse {
  flex-direction: row-reverse;
}

.l-vertical {
  flex-direction: column;
}

.l-vertical.l-reverse {
  flex-direction: column-reverse;
}

.l-wrap {
  flex-wrap: wrap;
}

.l-nowrap {
  flex-wrap: nowrap;
}

.l-wrap-reverse {
  flex-wrap: wrap-reverse;
}

.l-flex {
  flex: 1;
}

.l-flex-auto {
  flex: 1 1 auto;
}

.l-flex-none {
  flex: none;
}

.l-grow {
  flex-grow: 1;
}

.l-flex-12 {
  flex: 12;
}

.l-flex-11 {
  flex: 11;
}

.l-flex-10 {
  flex: 10;
}

.l-flex-9 {
  flex: 9;
}

.l-flex-8 {
  flex: 8;
}

.l-flex-7 {
  flex: 7;
}

.l-flex-6 {
  flex: 6;
}

.l-flex-5 {
  flex: 5;
}

.l-flex-4 {
  flex: 4;
}

.l-flex-3 {
  flex: 3;
}

.l-flex-2 {
  flex: 2;
}

.l-flex-1 {
  flex: 1;
}

.l-flex-0 {
  flex: 0;
}

.l-start-aligned {
  align-items: flex-start;
}

.l-center-aligned {
  align-items: center;
}

.l-end-aligned {
  align-items: flex-end;
}

.l-stretch-aligned {
  align-items: stretch;
}

.l-start-justified {
  justify-content: flex-start;
}

.l-center-justified {
  justify-content: center;
}

.l-end-justified {
  justify-content: flex-end;
}

.l-around-justified {
  justify-content: space-around;
}

.l-between-justified {
  justify-content: space-between;
}

.l-self-start-aligned {
  align-self: flex-start;
}

.l-self-center-aligned {
  align-self: center;
}

.l-self-end-aligned {
  align-self: flex-end;
}

.l-self-stretch-aligned {
  align-self: stretch;
}

.l-fit {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.l-relative {
  position: relative;
}

::-webkit-input-placeholder {
  color: rgb(112.2, 112.2, 112.2);
}

:-moz-placeholder {
  color: rgb(112.2, 112.2, 112.2);
}

::-moz-placeholder {
  color: rgb(112.2, 112.2, 112.2);
}

:-ms-input-placeholder {
  color: rgb(112.2, 112.2, 112.2);
}

fieldset {
  min-width: 0;
  margin: 0;
  padding: 0;
  border: 0;
}

.pos-form-wrapper {
  margin-bottom: 16px;
}

.pos-input {
  display: block;
  position: relative;
}

.pos-input-dob {
  display: inline-flex;
  align-items: center;
}

.pos-input input,
.pos-input select {
  width: 100%;
  height: 32px;
  padding: 0 8px;
  border: 1px solid rgb(193.8, 193.8, 193.8);
  border-radius: 4px;
  background-color: #fff;
  color: rgb(81.6, 81.6, 81.6);
  font-style: normal;
  font-weight: 400;
  text-decoration: none;
  box-shadow: none;
}

.pos-input select {
  padding-right: 29px;
  background: #fff url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2020%2020%22%3E%3Cpath%20d%3D%22M10%2014.5c-.3%200-.5-.1-.7-.3l-7-7c-.4-.4-.4-1%200-1.4.4-.4%201-.4%201.4%200l6.3%206.3%206.3-6.3c.4-.4%201-.4%201.4%200%20.4.4.4%201%200%201.4l-7%207c-.2.2-.5.3-.7.3z%22%2F%3E%3C%2Fsvg%3E") right 7px center no-repeat;
  background-size: 12px 12px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.pos-input select::-ms-expand {
  display: none;
}

select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000;
}

input[type=search] {
  box-sizing: border-box;
  -webkit-appearance: none;
}

input[type=file] {
  display: block;
}

.pos-input .pos-input--password,
.pos-input .pos-input--search {
  padding-right: 45px;
}

.pos-input--password::-ms-clear {
  width: 0;
  height: 0;
}

.pos-input--password::-ms-reveal {
  display: none;
}

.pos-input:hover input,
.pos-input input:hover,
.pos-input input:active,
.pos-input input:focus,
.pos-input select:hover,
.pos-input select:active,
.pos-input select:focus,
.pos-input-radio__label:hover .pos-input-radio__border,
.pos-input-radio__input:focus + .pos-input-radio__border,
.pos-input-checkbox__label:hover .pos-input-checkbox__border,
.pos-input-checkbox__input:focus + .pos-input-checkbox__border {
  border-color: #1c449b;
}

.pos-input input:disabled,
.pos-input select:disabled {
  border-color: rgb(193.8, 193.8, 193.8);
  background-color: rgb(242.76, 242.76, 242.76);
  color: #999999;
}

.pos-input--error input,
.pos-input--error input:focus,
.pos-input--error input:hover,
.pos-input--error select,
.pos-input--error select:focus,
.pos-input--error select:hover,
.pos-input-radio--error .pos-input-radio__border,
.pos-input-checkbox--error .pos-input-checkbox__border {
  border-color: #d40000;
}

.pos-input-radio--error:hover .pos-input-radio__border,
.pos-input-checkbox--error:hover .pos-input-checkbox__border {
  border-color: #1c449b;
}

.pos-input-smalltext {
  font-size: 12px;
}

.pos-label {
  color: rgb(81.6, 81.6, 81.6);
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  white-space: nowrap;
}

.pos-label--inline {
  align-self: center;
  margin-right: 16px;
  margin-bottom: 0;
}

.pos-label--block {
  display: block;
  margin-bottom: 4px;
}

.pos-input .pos-dob {
  width: 64px;
  margin-right: 16px;
}

.pos-input .pos-dob--dd,
.pos-input .pos-dob--mm {
  width: 44px;
  margin-right: 8px;
}

.pos-input select > option {
  padding: 14px 0 13px 16px;
}

.pos-input select > option:hover {
  background-color: #1c449b;
}

.pos-input select:focus::-ms-value {
  background-color: #fff;
  color: #333;
}

.pos-hint-text {
  margin-bottom: 8px;
  color: #d40000;
  white-space: nowrap;
}

.pos-hint-text--ok {
  color: #999999;
}

.pos-input-icon {
  position: absolute;
  top: 0;
  right: 8px;
  bottom: 0;
  left: auto;
  width: 20px;
  height: 20px;
  margin: auto 0;
  z-index: 2;
  fill: rgb(112.2, 112.2, 112.2);
}

.pos-input-toggle.pos-input-toggle--active,
.pos-input-toggle:hover {
  fill: #1c449b;
}

.pos-input-toggle.pos-input-toggle--active:hover,
.pos-input-toggle {
  fill: rgb(112.2, 112.2, 112.2);
}

.pos-form-message--content-right .pos-form-message__wrapper {
  justify-content: right;
}

.pos-form-message__wrapper {
  display: flex;
  min-height: 24px;
  margin-top: 8px;
  margin-bottom: 16px;
}

.pos-form-message--negative-top-margin .pos-form-message__wrapper {
  margin-top: -8px;
}

.pos-form-message .pos-svg-icon {
  width: 24px;
  height: 24px;
  padding: 2px;
}

.pos-form-message--success {
  color: #5cb82a;
  fill: #5cb82a;
}

/****************************
 DEPRECATED
 ************************* */
.pos-form-message--failure {
  color: #d40000;
  fill: #d40000;
}

.pos-form-message--error {
  color: #d40000;
  fill: #d40000;
}

.pos-form-message--warning {
  color: #333;
  fill: #f0bc00;
}

.pos-form-message--info {
  color: #333;
  fill: #a3a1a1;
}

.pos-form-message-text {
  flex-basis: auto;
  flex-grow: 1;
  flex-shrink: 1;
  align-self: center;
  padding-left: 4px;
}

.pos-input-radio,
.pos-input-checkbox {
  margin-right: 8px;
  margin-left: -8px;
}

/*Custom radio button*/
.pos-input-radio__label {
  display: inline-flex;
  position: relative;
  margin: 0;
  padding: 8px;
  font-weight: normal;
  cursor: pointer;
}

.pos-input-radio__label--disabled {
  cursor: default;
}

.pos-input-radio__border {
  position: relative;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  border: 1px solid #999999;
  border-radius: 100%;
  background-color: #fff;
  font-style: normal;
}

.pos-input-radio__checker {
  display: block;
  width: 10px;
  height: 10px;
  margin: 4px 0 0 4px;
  border: 1px solid transparent;
  border-radius: 100%;
  background-color: transparent;
}

.pos-input-radio__labeltext,
.pos-input-checkbox__labeltext {
  flex-basis: auto;
  flex-grow: 1;
  flex-shrink: 1;
  padding: 0 8px 0 6px;
  color: #333;
  font-size: 14px;
  line-height: 22px;
}

.pos-input-radio__input {
  position: absolute;
  opacity: 0;
  z-index: -1;
}

.pos-input-radio__input:checked + .pos-input-radio__border > .pos-input-radio__checker {
  border-color: #1c449b;
  background-color: #1c449b;
}

.pos-input-radio--error .pos-input-radio__input:checked + .pos-input-radio__border > .pos-input-radio__checker {
  border-color: #d40000;
  background-color: #d40000;
}

.pos-input-radio__label--disabled .pos-input-radio__border,
.pos-input-radio__label--disabled:hover .pos-input-radio__border {
  border-color: rgb(218.28, 218.28, 218.28);
}

.pos-input-radio__label--disabled .pos-input-radio__labeltext {
  color: rgb(218.28, 218.28, 218.28);
}

.pos-input-radio__label--disabled .pos-input-radio__input:checked + .pos-input-radio__border .pos-input-radio__checker {
  border-color: rgb(218.28, 218.28, 218.28);
  background-color: rgb(218.28, 218.28, 218.28);
}

/*Custom checkbox */
.pos-input-checkbox__label {
  display: inline-flex;
  position: relative;
  margin: 0;
  padding: 8px;
  font-weight: normal;
  cursor: pointer;
}

.pos-input-checkbox__label--disabled {
  cursor: default;
}

.pos-input-checkbox__border {
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 1px solid #999999;
  border-radius: 3px;
  background-color: #fff;
}

.pos-input-checkbox__checker {
  display: none;
}

.pos-input-checkbox__input {
  position: absolute;
  opacity: 0;
  z-index: -1;
}

.pos-input-checkbox__input:checked + .pos-input-checkbox__border > .pos-input-checkbox__checker {
  display: block;
  width: 8px;
  height: 12px;
  margin-top: 1px;
  transform: rotate(45deg);
  background: linear-gradient(to bottom, transparent 9px, #1c449b 9px), linear-gradient(to right, transparent 5px, #1c449b 5px);
}

.pos-input-checkbox--error .pos-input-checkbox__input:checked + .pos-input-checkbox__border > .pos-input-checkbox__checker {
  background: linear-gradient(to bottom, transparent 9px, #d40000 9px), linear-gradient(to right, transparent 5px, #d40000 5px);
}

.pos-input-checkbox__label--disabled .pos-input-checkbox__border,
.pos-input-checkbox__label--disabled:hover .pos-input-checkbox__border {
  border-color: rgb(218.28, 218.28, 218.28);
}

.pos-input-checkbox__label--disabled .pos-input-checkbox__labeltext,
.pos-input-checkbox__label--disabled:hover .pos-input-checkbox__labeltext {
  color: rgb(218.28, 218.28, 218.28);
}

.pos-input-checkbox__label--disabled .pos-input-checkbox__input:checked + .pos-input-checkbox__border .pos-input-checkbox__checker {
  background: linear-gradient(to bottom, transparent 9px, rgb(218.28, 218.28, 218.28) 9px), linear-gradient(to right, transparent 5px, rgb(218.28, 218.28, 218.28) 5px);
}

.pos-floating-label:not(:-moz-placeholder-shown) + label {
  top: 4px;
  transform: translateY(0);
  font-size: var(--Font-size-12, 12px);
  line-height: 16px;
}

.pos-floating-label:focus + label,
.pos-floating-label:not(:placeholder-shown) + label {
  top: 4px;
  transform: translateY(0);
  font-size: var(--Font-size-12, 12px);
  line-height: 16px;
}

.pos-floating-label + label {
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  top: 50%;
  left: 9px;
  transform: translateY(-50%);
  color: rgb(112.2, 112.2, 112.2);
  pointer-events: none;
  transition: 0.2s ease all;
}

.pos-floating-label:is(input, select) {
  height: 44px;
}

.pos-floating-label:is(input, select):not(:only-child) {
  padding-top: 19px;
  padding-bottom: 3px;
}

@media screen and (max-width: 767px) {
  .l-hidden-small {
    display: none !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 1199px) {
  .l-hidden-medium {
    display: none !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
  .l-hidden-medium-portrait {
    display: none !important;
  }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
  .l-hidden-medium-landscape {
    display: none !important;
  }
}

@media screen and (min-width: 1200px) {
  .l-hidden-large {
    display: none !important;
  }
}

h1 {
  font-size: 24px;
  line-height: 32px;
  color: rgb(81.6, 81.6, 81.6);
  font-family: var(--font-family-header);
  font-weight: var(--font-weight-header);
  margin: 0 0 24px;
}

h2 {
  font-size: 20px;
  line-height: 28px;
  font-weight: var(--font-weight-header);
  font-family: var(--font-family-header);
  color: rgb(81.6, 81.6, 81.6);
  margin: 0 0 16px;
}

h3 {
  font-size: 16px;
  line-height: 24px;
  font-weight: var(--font-weight-header);
  font-family: var(--font-family-header);
  color: rgb(81.6, 81.6, 81.6);
  margin: 0 0 16px;
}

p {
  font-size: 14px;
  line-height: 22px;
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #333;
  margin: 0 0 16px;
}

hr {
  margin-top: 0;
  margin-bottom: 24px;
  border: 0;
  border-top: 1px solid #eee;
}

a, a:hover, a:focus,
a a.active {
  outline: 0;
  color: #1c449b;
  text-decoration: none;
  cursor: pointer;
}
a:hover {
  text-decoration: underline;
}

ul {
  margin: 16px 0 0;
  padding-left: 24px;
  list-style: none;
}

ol {
  margin: 0 0 16px;
  padding-left: 36px;
}
ol li::before {
  width: 16px;
  display: none;
  margin-left: -16px;
  content: "•";
}
ol li::after {
  clear: both;
}

li::before {
  width: 16px;
  display: inline-block;
  margin-left: -16px;
  content: "•";
}
li::after {
  clear: both;
}

dl {
  margin: 0 0 16px;
}

dt {
  font-weight: bold;
}

dd {
  margin-left: 0;
}

small {
  font-size: 12px;
  line-height: 18px;
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #333;
}

.close {
  float: right;
  color: #000;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  text-shadow: bold;
  opacity: 0.2;
  filter: alpha(opacity=20);
}
.close:hover, .close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.5;
  filter: alpha(opacity=50);
}

button.close {
  padding: 0;
  border: 0;
  background: transparent;
  cursor: pointer;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.pos-list {
  width: 100%;
  max-width: 100%;
  margin: 0;
  margin-bottom: 20px;
  padding: 0;
  list-style: none;
}

.pos-list--content-right {
  text-align: right;
}

.pos-list--content-center {
  text-align: center;
}

.pos-list--content-left {
  text-align: left;
}

.pos-list .pos-svg-icon {
  display: flex;
  fill: #1c449b;
}

.pos-list .pos-svg {
  width: inherit;
  overflow: visible;
}

.pos-list__head-row {
  display: flex;
  align-items: center;
}

.pos-list__row {
  display: flex;
  align-items: center;
  min-height: 22px;
  border-top: 1px solid rgb(218.28, 218.28, 218.28);
  cursor: pointer;
}

.pos-list__row,
.pos-list__head-row {
  margin-bottom: 0;
}
.pos-list__row::before,
.pos-list__head-row::before {
  display: none;
}

.pos-list__head-row + .pos-list__row,
.pos-list__row:first-child {
  border-top: 0;
}

.pos-list__row:hover {
  background-color: rgb(214.14, 221.34, 237);
}

.pos-list__row--active {
  background-color: #1c449b;
}

.pos-list .pos-list__row--active:not(:hover) .pos-list__cell {
  color: #fff;
}

.pos-list .pos-list__row--active:not(:hover) .pos-svg {
  fill: #fff;
}

.pos-list .pos-list__row--active:not(:hover) .pos-input-checkbox__border {
  border-color: #fff;
}

.pos-list .pos-list__row--active:not(:hover) .pos-input-checkbox__checker {
  background: linear-gradient(to bottom, transparent 9px, #1c449b 9px), linear-gradient(to right, transparent 5px, #1c449b 5px);
}

.pos-list .pos-list__row--active:not(:hover) .pos-input-checkbox__labeltext {
  color: #fff;
}

@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-list .pos-list__row:hover {
    background-color: inherit;
  }
  .pos-list .pos-list__row--active:hover {
    background-color: #1c449b;
  }
  .pos-list .pos-list__row--active:hover .pos-list__cell {
    color: #fff;
  }
  .pos-list .pos-list__row--active:hover .pos-svg {
    fill: #fff;
  }
  .pos-list .pos-list__row--active:hover .pos-input-checkbox__border {
    border-color: #fff;
  }
  .pos-list .pos-list__row--active:hover .pos-input-checkbox__checker {
    background: linear-gradient(to bottom, transparent 9px, #1c449b 9px), linear-gradient(to right, transparent 5px, #1c449b 5px);
  }
  .pos-list .pos-list__row--active:hover .pos-input-checkbox__labeltext {
    color: #fff;
  }
}
.pos-list__cell {
  margin: 0;
  padding: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  line-height: 22px;
}

.pos-table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}

.pos-table--content-right {
  text-align: right;
}

.pos-table--content-center {
  text-align: center;
}

.pos-table--content-left {
  text-align: left;
}

.pos-table .pos-svg-icon {
  display: flex;
  fill: #1c449b;
}

.pos-table .pos-svg {
  width: inherit;
  overflow: visible;
}

.pos-table__row {
  min-height: 22px;
  border-top: 1px solid rgb(218.28, 218.28, 218.28);
  cursor: pointer;
}

.pos-table__head .pos-table__row,
.pos-table__body .pos-table__row:first-child {
  border-top: 0;
}

.pos-table__body .pos-table__row--active {
  background-color: #1c449b;
}

.pos-table__body .pos-table__row--active:not(:hover) .pos-table__cell {
  color: #fff;
}

.pos-table__body .pos-table__row--active:not(:hover) .pos-svg {
  fill: #fff;
}

.pos-table__body .pos-table__row--active:not(:hover) .pos-input-checkbox__border {
  border-color: #fff;
}

.pos-table__body .pos-table__row--active:not(:hover) .pos-input-checkbox__checker {
  background: linear-gradient(to bottom, transparent 9px, #1c449b 9px), linear-gradient(to right, transparent 5px, #1c449b 5px);
}

.pos-table__body .pos-table__row--active:not(:hover) .pos-input-checkbox__labeltext {
  color: #fff;
}

.pos-table__body .pos-table__row:hover {
  background-color: rgb(214.14, 221.34, 237);
}

@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-table__body .pos-table__row {
    cursor: unset;
  }
  .pos-table__body .pos-table__row:hover {
    background-color: inherit;
  }
  .pos-table__body .pos-table__row--active:hover {
    background-color: #1c449b;
  }
  .pos-table__body .pos-table__row--active:hover .pos-table__cell {
    color: #fff;
  }
  .pos-table__body .pos-table__row--active:hover .pos-svg {
    fill: #fff;
  }
  .pos-table__body .pos-table__row--active:hover .pos-input-checkbox__border {
    border-color: #fff;
  }
  .pos-table__body .pos-table__row--active:hover .pos-input-checkbox__checker {
    background: linear-gradient(to bottom, transparent 9px, #1c449b 9px), linear-gradient(to right, transparent 5px, #1c449b 5px);
  }
  .pos-table__body .pos-table__row--active:hover .pos-input-checkbox__labeltext {
    color: #fff;
  }
}
.pos-table__cell {
  margin: 0;
  padding: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  line-height: 22px;
}

.pos-brand-logo {
  display: flex;
  height: 100%;
  align-items: center;
  white-space: nowrap;
  fill: #fff;
  cursor: pointer;
}

.pos-button-group {
  display: inline-flex;
}

.pos-button-group .pos-button + .pos-button {
  border-radius: 0;
}

.pos-button-group .pos-button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.pos-button-group .pos-button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-right: 0 solid transparent;
}

/****************************
 DEPRECATED
 ************************* */
.pos-button-group--vertical {
  display: inline-block;
}

.pos-button-group--justified {
  display: table;
  width: 100%;
  border-collapse: separate;
  table-layout: fixed;
}

.pos-button-group--justified > .pos-button {
  display: table-cell;
  width: 1%;
}

.pos-button {
  display: inline-flex;
  margin-bottom: 0;
  border: 0;
  background-color: transparent;
  background-image: none;
  font-weight: normal;
  text-decoration: none;
  white-space: nowrap;
  cursor: pointer;
  vertical-align: middle;
  touch-action: manipulation;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 24px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

[pos-button].pos-button {
  padding: 4px 12px;
  border: 0 solid transparent;
}

.pos-button.active,
.pos-button:active,
.pos-button:hover,
.pos-button:focus,
.pos-button.focus {
  outline: 0;
}

.pos-button[disabled],
.pos-button.disabled {
  cursor: not-allowed;
  opacity: 0.65;
  filter: alpha(opacity=65);
}

@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-button:hover {
    outline: unset;
  }
}
.pos-button--cta {
  border-color: transparent;
  background-color: #6e9804;
  color: #fff;
  fill: #fff;
}
.pos-button--cta:focus, .pos-button--cta.focus {
  border-color: transparent;
  background-color: #6e9804;
  color: #fff;
}
.pos-button--cta:hover {
  border-color: rgba(0, 0, 0, 0.2);
  background-color: rgb(88, 121.6, 3.2);
  color: #fff;
  text-decoration: none;
}
@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-button--cta:hover {
    border-color: transparent;
    background-color: #6e9804;
    color: #fff;
    text-decoration: unset;
  }
}
.pos-button--cta:active, .pos-button--cta.active {
  border-color: transparent;
  background-color: rgb(99, 136.8, 3.6);
  color: #fff;
}
.pos-button--cta:active:hover, .pos-button--cta:active:focus, .pos-button--cta:active.focus, .pos-button--cta.active:hover, .pos-button--cta.active:focus, .pos-button--cta.active.focus {
  border-color: transparent;
  background-color: rgb(99, 136.8, 3.6);
  color: #fff;
}
.pos-button--cta.disabled:hover, .pos-button--cta[disabled]:hover, fieldset[disabled] .pos-button--cta:hover {
  border-color: transparent;
  background-color: #6e9804;
}
.pos-button--cta.disabled:focus, .pos-button--cta.disabled.focus, .pos-button--cta[disabled]:focus, .pos-button--cta[disabled].focus, fieldset[disabled] .pos-button--cta:focus, fieldset[disabled] .pos-button--cta.focus {
  border-color: transparent;
  background-color: #6e9804;
}
.pos-button--cta.pos-button--selected {
  border-color: rgba(0, 0, 0, 0.1);
  background-color: rgb(99, 136.8, 3.6);
  color: #fff;
  fill: #fff;
}
.pos-button--cta.pos-button--selected:hover {
  border-color: rgba(0, 0, 0, 0.2);
  background-color: rgb(88, 121.6, 3.2);
  color: #fff;
  text-decoration: none;
}
@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-button--cta.pos-button--selected:hover {
    border-color: rgba(0, 0, 0, 0.1);
    background-color: rgb(99, 136.8, 3.6);
    color: #fff;
    fill: #fff;
  }
}

.pos-button--primary {
  border-color: #1c449b;
  background-color: #1c449b;
  color: #fff;
  fill: #fff;
}
.pos-button--primary:focus, .pos-button--primary.focus {
  border-color: #1c449b;
  background-color: #1c449b;
  color: #fff;
}
.pos-button--primary:hover {
  border-color: rgb(22.4, 54.4, 124);
  background-color: rgb(22.4, 54.4, 124);
  color: #fff;
  text-decoration: none;
}
@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-button--primary:hover {
    border-color: #1c449b;
    background-color: #1c449b;
    color: #fff;
    text-decoration: unset;
  }
}
.pos-button--primary:active, .pos-button--primary.active {
  border-color: #1c449b;
  background-color: rgb(25.2, 61.2, 139.5);
  color: #fff;
}
.pos-button--primary:active:hover, .pos-button--primary:active:focus, .pos-button--primary:active.focus, .pos-button--primary.active:hover, .pos-button--primary.active:focus, .pos-button--primary.active.focus {
  border-color: #1c449b;
  background-color: rgb(25.2, 61.2, 139.5);
  color: #fff;
}
.pos-button--primary.disabled:hover, .pos-button--primary[disabled]:hover, fieldset[disabled] .pos-button--primary:hover {
  border-color: #1c449b;
  background-color: #1c449b;
}
.pos-button--primary.disabled:focus, .pos-button--primary.disabled.focus, .pos-button--primary[disabled]:focus, .pos-button--primary[disabled].focus, fieldset[disabled] .pos-button--primary:focus, fieldset[disabled] .pos-button--primary.focus {
  border-color: #1c449b;
  background-color: #1c449b;
}
.pos-button--primary.pos-button--selected {
  border-color: rgb(25.2, 61.2, 139.5);
  background-color: rgb(25.2, 61.2, 139.5);
  color: #fff;
  fill: #fff;
}
.pos-button--primary.pos-button--selected:hover {
  border-color: rgb(22.4, 54.4, 124);
  background-color: rgb(22.4, 54.4, 124);
  color: #fff;
  text-decoration: none;
}
@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-button--primary.pos-button--selected:hover {
    border-color: rgb(25.2, 61.2, 139.5);
    background-color: rgb(25.2, 61.2, 139.5);
    color: #fff;
    fill: #fff;
  }
}

.pos-button--tertiary {
  border-color: #fff;
  background-color: rgb(218.28, 218.28, 218.28);
  color: rgb(40.8, 40.8, 40.8);
  fill: rgb(40.8, 40.8, 40.8);
}
.pos-button--tertiary:focus, .pos-button--tertiary.focus {
  border-color: #fff;
  background-color: rgb(218.28, 218.28, 218.28);
  color: rgb(40.8, 40.8, 40.8);
}
.pos-button--tertiary:hover {
  border-color: #cccccc;
  background-color: rgb(174.624, 174.624, 174.624);
  color: rgb(40.8, 40.8, 40.8);
  text-decoration: none;
}
@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-button--tertiary:hover {
    border-color: #fff;
    background-color: rgb(218.28, 218.28, 218.28);
    color: rgb(40.8, 40.8, 40.8);
    text-decoration: unset;
  }
}
.pos-button--tertiary:active, .pos-button--tertiary.active {
  border-color: #fff;
  background-color: #999999;
  color: rgb(40.8, 40.8, 40.8);
}
.pos-button--tertiary:active:hover, .pos-button--tertiary:active:focus, .pos-button--tertiary:active.focus, .pos-button--tertiary.active:hover, .pos-button--tertiary.active:focus, .pos-button--tertiary.active.focus {
  border-color: #fff;
  background-color: #999999;
  color: rgb(40.8, 40.8, 40.8);
}
.pos-button--tertiary.disabled:hover, .pos-button--tertiary[disabled]:hover, fieldset[disabled] .pos-button--tertiary:hover {
  border-color: #fff;
  background-color: rgb(218.28, 218.28, 218.28);
}
.pos-button--tertiary.disabled:focus, .pos-button--tertiary.disabled.focus, .pos-button--tertiary[disabled]:focus, .pos-button--tertiary[disabled].focus, fieldset[disabled] .pos-button--tertiary:focus, fieldset[disabled] .pos-button--tertiary.focus {
  border-color: #fff;
  background-color: rgb(218.28, 218.28, 218.28);
}
.pos-button--tertiary.pos-button--selected {
  border-color: rgb(25.2, 61.2, 139.5);
  background-color: rgb(25.2, 61.2, 139.5);
  color: #fff;
  fill: #fff;
}
.pos-button--tertiary.pos-button--selected:hover {
  border-color: rgb(22.4, 54.4, 124);
  background-color: rgb(22.4, 54.4, 124);
  color: #fff;
  text-decoration: none;
}
@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-button--tertiary.pos-button--selected:hover {
    border-color: rgb(25.2, 61.2, 139.5);
    background-color: rgb(25.2, 61.2, 139.5);
    color: #fff;
    fill: #fff;
  }
}

.pos-button--link {
  transition: background-color 0.2s ease;
  border-color: transparent;
  background-color: transparent;
  color: #1c449b;
  fill: #1c449b;
}
.pos-button--link:focus, .pos-button--link.focus {
  border-color: transparent;
  background-color: transparent;
  color: #1c449b;
}
.pos-button--link:hover {
  border-color: rgba(0, 0, 0, 0.2);
  background-color: rgb(214.14, 221.34, 237);
  color: #1c449b;
  text-decoration: none;
}
@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-button--link:hover {
    border-color: transparent;
    background-color: transparent;
    color: #1c449b;
    text-decoration: unset;
  }
}
.pos-button--link:active, .pos-button--link.active {
  border-color: transparent;
  background-color: rgb(25.2, 61.2, 139.5);
  color: #fff;
  fill: #fff;
}
.pos-button--link:active:hover, .pos-button--link:active:focus, .pos-button--link:active.focus, .pos-button--link.active:hover, .pos-button--link.active:focus, .pos-button--link.active.focus {
  border-color: transparent;
  background-color: rgb(25.2, 61.2, 139.5);
  color: #fff;
  fill: #fff;
}
.pos-button--link.disabled:hover, .pos-button--link[disabled]:hover, fieldset[disabled] .pos-button--link:hover {
  border-color: transparent;
  background-color: transparent;
}
.pos-button--link.disabled:focus, .pos-button--link.disabled.focus, .pos-button--link[disabled]:focus, .pos-button--link[disabled].focus, fieldset[disabled] .pos-button--link:focus, fieldset[disabled] .pos-button--link.focus {
  border-color: transparent;
  background-color: transparent;
}
.pos-button--link.pos-button--selected {
  border-color: rgb(25.2, 61.2, 139.5);
  background-color: rgb(25.2, 61.2, 139.5);
  color: #fff;
  fill: #fff;
}
.pos-button--link.pos-button--selected:hover {
  border-color: rgb(22.4, 54.4, 124);
  background-color: rgb(22.4, 54.4, 124);
  color: #fff;
  text-decoration: none;
}
@media (-moz-touch-enabled: 1), (pointer: coarse) {
  .pos-button--link.pos-button--selected:hover {
    border-color: rgb(25.2, 61.2, 139.5);
    background-color: rgb(25.2, 61.2, 139.5);
    color: #fff;
    fill: #fff;
  }
}
.pos-button--link[disabled] {
  color: #999999;
}

[pos-button].pos-button .pos-svg-icon {
  width: 24px;
  height: 24px;
  margin-right: -4px;
  margin-left: -4px;
}

[pos-button].pos-button .pos-svg-icon .pos-svg {
  padding: 2px;
}

.pos-button.pos-button--icon-left .pos-svg-icon {
  margin-right: 12px;
}

.pos-button.pos-button--icon-right .pos-svg-icon {
  margin-left: 12px;
}

/****************************
 DEPRECATED
 ************************* */
.pos-button__button {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 6px 12px;
  border: 0;
  background: transparent;
  line-height: 24px;
}

.pos-button__button[disabled] {
  cursor: not-allowed;
  pointer-events: none;
}

.pos-button--lg,
.pos-button-group--lg .pos-button__button {
  padding: 0 0;
  border-radius: 6px;
  font-size: 16px;
  line-height: 24px;
}

.pos-button--lg .pos-button__button {
  padding: 10px 16px;
}

.pos-button--sm,
.pos-button-group--sm .pos-button__button {
  padding: 0 0;
  border-radius: 3px;
  font-size: 12px;
  line-height: 18px;
}

.pos-button--sm .pos-button__button {
  padding: 5px 10px;
}

.pos-button--xs,
.pos-button-group--xs .pos-button__button {
  padding: 0 0;
  border-radius: 3px;
  font-size: 12px;
  line-height: 18px;
}

.pos-button--xs .pos-button__button {
  padding: 1px 5px;
}

.pos-button--cta.pos-button--lg {
  font-size: 14px;
  font-weight: normal;
}

.pos-button--cta.pos-button--lg .pos-button__button {
  padding: 8px;
}

/*
    .pos-button--fab: Button modifier for round FAB (Floating Action Button) with shadow
*/
.pos-button--fab {
  padding: 0;
  transition: background-color 0.1s ease 0s;
  border-radius: 50%;
  box-shadow: 0 4px 4px rgba(45.9, 45.9, 45.9, 0.4);
}

.pos-button--fab .pos-button__button {
  padding: 8px;
}

.pos-button--fab.pos-button--lg .pos-button__button {
  padding: 16px;
}

.pos-button--fab.pos-button--small .pos-button__button {
  padding: 10px;
}

.pos-button--fab.pos-button--xs .pos-button__button {
  padding: 5px;
}

.pos-button-group--vertical > .pos-button {
  display: block;
  width: 100%;
  max-width: 100%;
}

.pos-button-group--vertical > .pos-button + .pos-button {
  margin-top: -1px;
  margin-left: 0;
}

.pos-button-group--vertical > .pos-button:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.pos-button-group--vertical > .pos-button:first-child:not(:last-child) {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.pos-button-group--vertical > .pos-button:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.pos-button-toolbar {
  display: flex;
  margin-left: -5px;
}

.pos-button-toolbar > .pos-button,
.pos-button-toolbar > .pos-button-group {
  margin-left: 5px;
}

.pos-button--form-row .pos-button__button {
  padding: 3px 10px;
}

/*
    .pos-button__text: Style for text element inside of a button
*/
.pos-button__text + .pos-button__icon {
  margin-left: 8px;
}

.pos-button--block {
  display: block;
  width: 100%;
}

.pos-button--block + .pos-button--block {
  margin-top: 5px;
}

input[type=submit].pos-button--block,
input[type=reset].pos-button--block,
input[type=button].pos-button--block {
  width: 100%;
}

/*
    .pos-button__icon: Style for icon element inside of a button
    .pos-button__text: Style for text element inside of a button
*/
.pos-button__icon {
  padding: 2px;
}

.pos-button__icon + .pos-button__text {
  margin-left: 8px;
}

.pos-content-box {
  display: block;
  margin: 0;
  overflow: hidden;
}

.pos-content-box--visible {
  margin: 8px 0;
  transition: height 0.3s linear, margin 0.3s linear;
}

.pos-info-box {
  border-radius: 4px;
  background-color: #1c449b;
  color: #fff;
  fill: #fff;
}

.pos-info-box .pos-info-box__outer-wrapper {
  display: flex;
  padding: 8px;
}

.pos-info-box .pos-svg-icon {
  margin-left: 16px;
  padding: 2px;
}

.pos-info-box .pos-info-box__inner-wrapper {
  flex: 1;
  padding: 8px 4px 8px 12px;
  line-height: 18px;
}

.pos-header {
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-top: 4px solid #1c449b;
  background-color: #1c449b;
  color: #fff;
}

.pos-header--small {
  padding: 0 8px;
}
.pos-header--small .pos-header__content {
  height: 44px;
}

.pos-header__content {
  height: 52px;
  display: flex;
  align-items: center;
}

/****************************
 DEPRECATED
 ************************* */
.pos-modal-wrapper {
  z-index: 4;
}

.pos-message-modal {
  align-self: center;
  min-width: 320px;
  max-width: 480px;
  margin: auto;
}

.pos-message-modal__wrapper {
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  outline: 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  background-color: #fff;
}

@media screen and (min-width: 768px) {
  .pos-message-modal__wrapper {
    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  }
}
.pos-message-modal__body {
  padding: 16px 16px 32px;
}

.pos-message-modal__icon {
  margin-bottom: 12px;
}

.pos-message-modal__heading {
  margin-bottom: 24px;
  font-size: 18px;
  line-height: 24px;
}

.pos-message-modal__heading,
.pos-message-modal__details {
  text-align: center;
}

.pos-message-modal__footer {
  padding: 8px;
  border-top: 1px solid #e5e5e5;
  border-radius: 0 0 4px 4px;
  background-color: rgb(242.76, 242.76, 242.76);
  text-align: right;
}
.pos-message-modal__footer::before, .pos-message-modal__footer::after {
  display: table;
  content: " ";
}
.pos-message-modal__footer::after {
  clear: both;
}

.pos-message-modal__action-button + .pos-message-modal__action-button {
  margin-left: 8px;
}

.pos-message {
  width: 100%;
  margin-bottom: 16px;
}
@media screen and (min-width: 768px) {
  .pos-message {
    width: 480px;
  }
}

.pos-message__wrapper {
  width: inherit;
  padding: 16px;
  border: 1px solid #999999;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  .pos-message__wrapper {
    border-width: 1px 0;
  }
}
@media screen and (min-width: 768px) {
  .pos-message__wrapper {
    border-radius: 4px;
  }
}

.pos-message__heading {
  font-size: 18px;
  line-height: 24px;
}

.pos-message__details {
  flex-basis: auto;
  margin-top: 8px;
}

.pos-message__details:empty {
  display: none;
}

.pos-message__action-link {
  margin-left: 8px;
  font-size: 14px;
  line-height: 20px;
}

.pos-message__close-button {
  width: 32px;
  height: 32px;
  margin: -4px -4px -8px 12px;
}

.pos-message__icon {
  margin-right: 12px;
}

.pos-message__icon--info {
  fill: #a3a1a1;
}

.pos-message__icon--success {
  fill: #5cb82a;
}

.pos-message__icon--warning {
  fill: #f0bc00;
}

/****************************
 DEPRECATED
 ************************* */
.pos-message__icon--failure {
  fill: #d40000;
}

.pos-message__icon--error {
  fill: #d40000;
}

.pos-message__wrapper--info {
  border-bottom: 5px solid #a3a1a1;
}

.pos-message__wrapper--success {
  border-bottom: 5px solid #5cb82a;
}

.pos-message__wrapper--warning {
  border-bottom: 5px solid #f0bc00;
}

/****************************
 DEPRECATED
 ************************* */
.pos-message__wrapper--failure {
  border-bottom: 5px solid #d40000;
}

.pos-message__wrapper--error {
  border-bottom: 5px solid #d40000;
}

.pos-message--floating {
  position: absolute;
  top: 0;
  z-index: 99;
}
@media screen and (min-width: 768px) {
  .pos-message--floating {
    left: 50%;
    margin-left: -240px;
  }
}

.pos-modal-layer {
  display: none;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(51, 51, 51, 0.5);
  cursor: pointer;
  z-index: 3;
}

.pos-modal-layer--visible {
  display: block;
}

.pos-progressbar {
  display: block;
  width: 100%;
  height: 8px;
  margin-top: 8px;
  background-color: rgb(218.28, 218.28, 218.28);
  border-radius: 4px;
  overflow: hidden;
}

.pos-progressbar-indicator {
  width: 0;
  height: 100%;
  border-radius: 4px 0 0 4px;
}

.pos-progressbar--medium {
  width: 100%;
  height: 8px;
}

.pos-progressbar--large {
  width: 100%;
  height: 16px;
}

.pos-progressbar--small {
  width: 100%;
  height: 4px;
}

.pos-progressbar .pos-progressbar-indicator,
.pos-progressbar--default .pos-progressbar-indicator {
  background-color: #1c449b;
}

.pos-progressbar--error .pos-progressbar-indicator {
  background-color: #d40000;
}

.pos-progressbar--warning .pos-progressbar-indicator {
  background-color: #f0bc00;
}

.pos-progressbar--confirm .pos-progressbar-indicator {
  background-color: #5cb82a;
}

.pos-spinner {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}

.pos-spinner__bar {
  position: absolute;
  top: 0;
  width: 2px;
  height: 8px;
  transform-origin: 1px 15px;
  background-color: #333;
  opacity: 0.05;
  animation: fadeit 0.8s linear infinite;
}

@keyframes fadeit {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.pos-spinner__bar--bar15 {
  transform: rotate(337.5deg);
  animation-delay: 0.75s;
}

.pos-spinner__bar--bar14 {
  transform: rotate(315deg);
  animation-delay: 0.7s;
}

.pos-spinner__bar--bar13 {
  transform: rotate(292.5deg);
  animation-delay: 0.65s;
}

.pos-spinner__bar--bar12 {
  transform: rotate(270deg);
  animation-delay: 0.6s;
}

.pos-spinner__bar--bar11 {
  transform: rotate(247.5deg);
  animation-delay: 0.55s;
}

.pos-spinner__bar--bar10 {
  transform: rotate(225deg);
  animation-delay: 0.5s;
}

.pos-spinner__bar--bar9 {
  transform: rotate(202.5deg);
  animation-delay: 0.45s;
}

.pos-spinner__bar--bar8 {
  transform: rotate(180deg);
  animation-delay: 0.4s;
}

.pos-spinner__bar--bar7 {
  transform: rotate(157.5deg);
  animation-delay: 0.35s;
}

.pos-spinner__bar--bar6 {
  transform: rotate(135deg);
  animation-delay: 0.3s;
}

.pos-spinner__bar--bar5 {
  transform: rotate(112.5deg);
  animation-delay: 0.25s;
}

.pos-spinner__bar--bar4 {
  transform: rotate(90deg);
  animation-delay: 0.2s;
}

.pos-spinner__bar--bar3 {
  transform: rotate(67.5deg);
  animation-delay: 0.15s;
}

.pos-spinner__bar--bar2 {
  transform: rotate(45deg);
  animation-delay: 0.1s;
}

.pos-spinner__bar--bar1 {
  transform: rotate(22.5deg);
  animation-delay: 0.05s;
}

.pos-spinner__bar--bar0 {
  transform: rotate(0deg);
  animation-delay: 0s;
}

.pos-svg-icon {
  display: inline-flex;
}

.pos-svg-icon .pos-svg {
  width: inherit;
  overflow: visible;
  pointer-events: none;
}

.pos-svg-icon--48 {
  width: 48px;
  height: 48px;
}

.pos-svg-icon--32 {
  width: 32px;
  height: 32px;
}

.pos-svg-icon--24 {
  width: 24px;
  height: 24px;
}

.pos-svg-icon--16 {
  width: 16px;
  height: 16px;
}

@media (max-width: 290px) {
  html {
    overflow-x: auto;
  }
}
body {
  height: 100vh;
  color: rgb(81.6, 81.6, 81.6);
  font-size: 14px;
}
@media screen and (max-width: 767px) {
  body {
    height: 100svh;
  }
}

p {
  margin-top: 0;
  margin-bottom: 16px;
}

.hastransision {
  transition: transform 0.2s ease-in-out 0s;
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .form__panel--personal-info {
    line-height: 14px;
  }
}
.noscript-warning {
  width: 50%;
  margin: auto;
  margin-top: 20px;
  padding: 10px;
  border-radius: 4px;
  background-color: rgb(250.5, 234.9, 178.5);
}

.noscript-warning .pos-svg {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  padding: 2px;
  fill: #f0bc00;
}

.toggle-wrapper {
  padding: 8px 0;
  color: #1c449b;
  line-height: 22px;
  white-space: nowrap;
}
.toggle-wrapper:hover {
  text-decoration: underline;
}

.toggle-wrapper .pos-caret {
  margin-right: 8px;
  fill: #1c449b;
}

.onereg-info-icon {
  width: 20px;
  height: 20px;
  align-self: flex-start;
  min-width: 20px;
  margin-top: 5px;
  margin-left: 8px;
  padding: 0;
  fill: #1c449b;
}

h2 .onereg-info-icon {
  margin-top: 4px;
  margin-left: 0;
  vertical-align: top;
}

h3 .onereg-info-icon {
  margin-top: 2px;
  margin-left: 0;
  vertical-align: top;
}

.pos-info-box {
  margin-top: 0;
}
.pos-info-box.pos-content-box--visible {
  margin-bottom: 16px;
}

.onereg-pwtipps ul {
  margin-top: 16px;
  padding-left: 16px;
}
.onereg-pwtipps li {
  margin-bottom: 0;
}
.onereg-pwtipps a,
.onereg-pwtipps p {
  color: #fff;
}

.onereg-captcha-container {
  background-color: rgb(242.76, 242.76, 242.76);
}

.onereg-refresh {
  display: block;
}

.onereg-refresh .pos-svg-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.onereg-captcha--loading .onereg-refresh .pos-svg-icon {
  animation: rotation 1s linear 0s normal none infinite;
}

.onereg-refresh .pos-svg {
  width: 24px;
  height: 24px;
  padding: 2px;
  fill: #1c449b;
}

.onereg-refresh .pos-icon-item__label {
  color: #1c449b;
}

.onereg-refresh .pos-icon-item__label:hover {
  text-decoration: underline;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.onereg-password-container:not(.pos-input--error):hover .pos-input--password {
  border-color: #1c449b;
}

.onereg-text--normal {
  font-weight: normal;
}

.onereg-password__minlength-message .pos-form-message__wrapper {
  align-items: flex-start;
}

.pos-input-radio--error .pos-input-radio__input:focus + .pos-input-radio__border,
.pos-input-checkbox--error .pos-input-checkbox__input:focus + .pos-input-checkbox__border {
  border-color: #d40000;
}

.hidden {
  display: none;
}

onereg-additional-info {
  min-width: 100%;
}
onereg-additional-info .onereg-plz-field {
  width: 125px;
}
onereg-additional-info .land-line-phone-input {
  gap: 8px;
}

.agb-box {
  margin-bottom: 24px;
  padding: 16px;
  background: rgb(242.76, 242.76, 242.76);
}
.agb-box > *:last-child {
  margin-bottom: 0;
}

.agb-box__title--no-margin {
  margin-bottom: 0;
}

.agb-box__title-container {
  display: flex;
  flex-direction: row;
}

.agb-box__icon {
  width: 124px;
  max-width: 124px;
  height: 48px;
  max-height: 48px;
  padding-left: 8px;
}
.agb-box__icon .pos-svg {
  overflow: auto;
}

.onereg-advanced-suggestions {
  display: block;
  width: 100%;
  max-width: 100%;
}

.onereg-advanced-suggestions__skeleton-text {
  color: rgb(81.6, 81.6, 81.6);
  margin: 24px 0;
}

.onereg-advanced-suggestions__skeleton-group {
  margin: 24px 0;
}

.onereg-advanced-suggestions__skeleton-group:first-child {
  margin: 0;
}

.email-alias-advanced__input {
  padding: 16px;
  margin: 0 -16px;
  background: rgb(242.76, 242.76, 242.76);
}
@media screen and (min-width: 768px) {
  .email-alias-advanced__input {
    padding: 0;
    margin: 0;
    background: #fff;
  }
}

.email-alias-advanced__content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding-bottom: 20px;
}
@media screen and (min-width: 768px) {
  .email-alias-advanced__content {
    padding-bottom: 48px;
  }
}

.email-alias-advanced__success-message {
  margin-top: -8px;
}

.email-alias-advanced-input__alias-box {
  margin-bottom: 8px;
  gap: 8px;
}
.email-alias-advanced-input__alias-box .pos-input input:disabled,
.email-alias-advanced-input__alias-box .pos-input select:disabled {
  opacity: 1;
}

.email-alias-advanced-input__input-title {
  margin-bottom: 8px;
}

.email-alias-advanced-input__check {
  width: 100%;
  height: 40px;
  margin-top: 8px;
  border-radius: 4px;
}

.email-alias-advanced-check__text {
  width: 100%;
}

.alias-advanced__check-spinner {
  margin: auto;
}

.alias-advanced__check-spinner .pos-spinner__bar {
  background-color: #fff;
}

.email-alias-advanced-input__alias-input input::-webkit-contacts-auto-fill-button,
.email-alias-advanced-input__alias-input input::-webkit-credentials-auto-fill-button {
  display: none !important;
  position: absolute;
  right: 0;
  visibility: hidden;
  pointer-events: none;
}

.email-alias-advanced-input--no-domain-selector {
  gap: 0;
}
.email-alias-advanced-input--no-domain-selector .email-alias-advanced-input__alias-text-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
}

.email-alias-advanced-input__postfix {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  background-color: white;
  padding-right: 8px;
  border: 1px solid rgb(193.8, 193.8, 193.8);
  border-left: 0;
  line-height: 42px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.email-alias-advanced-input__postfix--focus {
  border-color: #1c449b;
}

.email-alias-advanced-input__postfix--error {
  border-color: #d40000;
}

.email-alias-advanced-input__postfix--disabled {
  border-color: rgb(193.8, 193.8, 193.8);
  background-color: rgb(242.76, 242.76, 242.76);
  color: #999999;
}

.email-alias-input .email-alias-input__alias-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.email-alias-input .email-alias-input__domain-input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding-right: 36px;
  border-left-width: 0;
}
.email-alias-input .email-alias-input__domain-input:focus,
.email-alias-input .email-alias-input__domain-input:hover {
  border-left-width: 1px;
}
.email-alias-input .email-alias-input__check {
  display: flex;
  align-self: center;
  justify-content: center;
}

.email-alias-check-select {
  margin-right: 8px;
}

.email-alias-input--no-domain-selector .email-alias-input__alias-input {
  border-right: 0;
}

.email-alias-input__postfix {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  height: 32px;
  padding-right: 8px;
  border: 1px solid rgb(193.8, 193.8, 193.8);
  border-left: 0;
  line-height: 30px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.email-alias-input__postfix--focus {
  border-color: #1c449b;
}

.email-alias-input__postfix--error {
  border-color: #d40000;
}

.email-alias-input__postfix--disabled {
  border-color: rgb(193.8, 193.8, 193.8);
  background-color: rgb(242.76, 242.76, 242.76);
  color: #999999;
}

@media screen and (max-width: 767px) {
  .email-alias-input {
    flex-wrap: wrap;
  }
  .email-alias-input .email-alias-input__check {
    width: 100%;
    margin-top: 16px;
    height: 40px;
  }
  .email-alias-check-select {
    margin-right: 0;
  }
  .email-alias-check__text {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .email-alias-input__fieldset {
    height: 100%;
  }
}
.onereg-app {
  display: flex;
  flex-direction: column;
  min-width: 290px;
}

.onereg-app.ismultistep {
  height: 100%;
}

.onereg-registration-app {
  margin: 32px 16px 200px;
}

.onereg-app.ismultistep .onereg-registration-app {
  flex: 1 0 auto;
}

onereg-app .pos-header {
  display: flex;
  justify-content: center;
}

onereg-app .pos-brand-title {
  font-size: 20px;
}
@media screen and (min-width: 768px) {
  onereg-app .pos-brand-title {
    font-size: 32px;
  }
}

onereg-app .pos-header__content {
  width: 100%;
}
@media screen and (min-width: 1200px) {
  onereg-app .pos-header__content {
    justify-content: flex-start;
    width: 944px;
  }
}
@media screen and (min-width: 768px) and (max-width: 1199px) {
  onereg-app .pos-header__content {
    justify-content: flex-start;
    width: 464px;
  }
}

@media screen and (max-width: 767px) {
  .onereg-app:not(.ismultistep--always) .onereg-registration-app {
    margin: 32px 0 0;
  }
}
@media screen and (min-width: 768px) {
  .onereg-app:not(.ismultistep--always) .pos-header {
    height: 72px;
  }
}

.onereg-app.ismultistep--always .onereg-registration-app {
  margin: 0;
}
@media screen and (min-width: 768px) {
  .onereg-app.ismultistep--always .onereg-registration-app {
    margin-top: 32px;
  }
}

.captchafox {
  --primary-color: #1c449b;
  --success-color: #5cb82a;
  --error-color: #d40000;
}

.form__panel--terms-and-conditions .captchafox {
  margin-top: 8px;
  margin-bottom: 16px;
}

/* stylelint-disable selector-max-id */
#onereg-captcha-fox [role=button] {
  max-width: 100%;
}

.onereg-euds {
  margin-bottom: space(3);
}

.onereg-euds__icon {
  width: 24px;
  height: 24px;
  margin-right: space(1);
}

.onereg-footer {
  display: block;
  flex: none;
  border-top: 1px solid rgb(193.8, 193.8, 193.8);
  background-color: rgb(242.76, 242.76, 242.76);
}

.onereg-footer__list {
  margin: 0;
  padding: 12px 0;
  list-style: none;
}

.onereg-footer__list-item {
  margin: 0 8px;
  white-space: nowrap;
}
.onereg-footer__list-item::before {
  display: none;
}

.onereg-footer__link {
  display: block;
  color: #333;
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
}
.onereg-footer__link:active, .onereg-footer__link:focus, .onereg-footer__link:hover {
  color: #333;
}

.onereg-free-level-two-panel__container {
  margin-top: -8px;
}

.onereg-free-level-two__info {
  margin-left: 36px;
}

onereg-initial-info {
  min-width: 100%;
}
onereg-initial-info .toggle-wrapper {
  padding: 0;
}
onereg-initial-info .toggle-wrapper .pos-caret {
  margin-left: 8px;
  margin-right: 0;
}
onereg-initial-info .pos-input-dob {
  display: flex;
  align-content: stretch;
  gap: 8px;
}
onereg-initial-info .pos-input-dob .pos-input + span {
  display: none;
}
onereg-initial-info .pos-input-dob .pos-input input {
  margin: 0;
}
onereg-initial-info .pos-input-dob .pos-input .pos-dob--dd {
  width: 80px;
}
onereg-initial-info .pos-input-dob .pos-input .pos-dob--mm {
  width: 123px;
}
onereg-initial-info .pos-input-dob .pos-input .pos-dob--yyyy {
  width: 100%;
}
onereg-initial-info .pos-input-dob .pos-input:last-of-type {
  flex: 1;
}

.onereg-initial-info__preselected-email {
  border-radius: 4px;
  border: 1px dashed #1c449b;
  display: flex;
  padding: 8px 16px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  width: -moz-fit-content;
  width: fit-content;
  margin-bottom: 16px;
  height: 34px;
  font-weight: 700;
  font-family: Verdana, sans-serif;
  font-size: 12px;
  color: rgb(81.6, 81.6, 81.6);
}

.onereg-initial-info__wrapper {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

@media screen and (max-width: 767px) {
  .onereg-initial-info__fieldset {
    height: 100%;
  }
}
.onereg-mail-domain-suggestions {
  margin-bottom: 8px;
  height: 100%;
}

.onereg-mail-domain-suggestions__cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  word-break: break-all;
  border: solid 1px #5cb82a;
  background-color: #fff;
  border-radius: 4px;
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.onereg-mail-domain-suggestions__email {
  font-size: 12px;
  font-weight: 700;
  color: rgb(81.6, 81.6, 81.6);
  line-height: 18px;
}

.onereg-mail-domain-suggestions__price {
  font-size: 10px;
  font-weight: 700;
  color: #1c449b;
  line-height: 14px;
  margin-top: 4px;
}

.onereg-mail-domain-suggestions__icon {
  height: 24px;
  width: 24px;
  color: #5cb82a;
  fill: #5cb82a;
}

.onereg-mail-domain-suggestions__domain-choice {
  margin: 0 8px;
  padding: 12px;
  border: 1px solid rgb(193.8, 193.8, 193.8);
  border-radius: 0 0 8px 8px;
  border-top: none;
}
.onereg-mail-domain-suggestions__domain-choice .pos-input-checkbox__label .pos-input-checkbox__labeltext {
  color: #1c449b;
  font-weight: 700;
  margin-bottom: 8px;
}

.onereg-mail-domain-suggestions__title {
  margin-bottom: 8px;
  color: #333;
}

.onereg-mail-domain-suggestions__info {
  margin: 0;
  font-size: 10px;
  line-height: 14px;
  color: #333;
}

.onereg-mail-domain-suggestions__info--highlight {
  color: #1c449b;
}

.mdh-free-mail-selection__wrapper {
  height: 100%;
}

.mdh-free-mail-selection__alias-container {
  padding: 16px 8px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  border-radius: 0 0 4px 4px;
  background-color: rgb(242.76, 242.76, 242.76);
}

.mdh-free-mail-selection__alias-mdh {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 8px;
  gap: 4px;
  border-radius: 4px 4px 0 0;
  border: 1px solid #1c449b;
  background-color: rgb(241.38, 243.78, 249);
  font-family: Verdana, sans-serif;
  margin-top: 16px;
}

.mdh-free-mail-selection__alias-mdh__domain {
  font-size: 12px;
  font-style: normal;
  font-weight: 700;
  line-height: 18px;
}

.mdh-free-mail-selection__alias-mdh__text {
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px;
  color: #333;
}

.mdh-free-mail-selection__alias-container-title {
  margin-bottom: 8px;
  font-family: Verdana, sans-serif;
  font-size: 12px;
  font-style: normal;
  font-weight: 700;
  line-height: 18px;
  color: #333;
}

.mdh-free-mail-selection__legal-note-container {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  margin-top: 8px;
}

.mdh-free-mail-selection__legal-note {
  font-family: Verdana, sans-serif;
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px;
  color: rgb(81.6, 81.6, 81.6);
}

.mdh-free-mail-selection__legal-note-icon {
  width: 20px;
  height: 20px;
  fill: #a3a1a1;
  margin-top: 0;
  margin-left: 0;
}

.onereg-pay-info-box__close-button, .onereg-mdh-info-box__close-button {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 8px;
  right: 16px;
  padding: 2px;
  cursor: pointer;
  z-index: 1;
  fill: #999999;
}
.onereg-pay-info-box__close-button .pos-svg, .onereg-mdh-info-box__close-button .pos-svg {
  width: inherit;
  overflow: visible;
}

.onereg-mdh-info-box {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
  border-radius: 4px;
  background-color: rgb(241.38, 243.78, 249);
}

.onereg-mdh-info-box__domain-name {
  margin: 0 16px 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgb(214.14, 221.34, 237);
  text-align: center;
  word-break: break-all;
}

.onereg-mdh-info-box__bottom-container {
  display: flex;
  flex-direction: column;
  background-color: rgb(241.38, 243.78, 249);
}
@media screen and (min-width: 768px) {
  .onereg-mdh-info-box__bottom-container {
    flex-direction: row;
  }
}

.onereg-mdh-info-box__contentbox {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 16px 16px;
}
.onereg-mdh-info-box__contentbox:last-child {
  flex: 1;
}
@media screen and (min-width: 768px) {
  .onereg-mdh-info-box__contentbox {
    flex: 1;
  }
}

.onereg-mdh-info-box__icon .pos-svg {
  width: 52px;
  height: 52px;
  fill: #1c449b;
}

.onereg-mdh-info-box__icon-title {
  font-size: 20px;
  line-height: 28px;
  font-weight: var(--font-weight-header);
  font-family: var(--font-family-header);
  color: rgb(81.6, 81.6, 81.6);
  max-width: 100%;
  padding-top: 20px;
  text-align: center;
}

.onereg-mdh-info-box__icon-subtitle {
  margin-top: 8px;
  font-size: 16px;
  line-height: 24px;
}

.onereg-mdh-info-box__list {
  margin: 0 0 8px;
  padding-left: 35px;
  list-style: none;
}
@media screen and (min-width: 768px) {
  .onereg-mdh-info-box__list {
    padding-left: 5px;
  }
}

.onereg-mdh-info-box__list-item {
  position: relative;
  max-width: 200px;
  margin-bottom: 8px;
}
.onereg-mdh-info-box__list-item::before {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 6px;
  margin: 0 8px 0 -16px;
  border-radius: 50%;
  background-color: rgb(45.9, 45.9, 45.9);
  line-height: 24px;
  content: "";
}

.onereg-mdh-login-info {
  display: block;
}

.mdh-info-box__mail-domain-container {
  flex-direction: column;
  align-items: flex-start;
  padding: 16px;
  gap: 8px;
  background-color: rgb(242.76, 242.76, 242.76);
  border-radius: 0 0 4px 4px;
}

.mdh-info-box__mail-domain-text,
.mdh-info-box__mail-domain-email {
  color: #000;
  text-align: center;
  font-size: 14px;
  line-height: 22px;
}

.mdh-info-box__info-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-top: 4px;
}

.mdh-info-box__mail-domain-email {
  font-weight: 700;
}

.mdh-info-box__info-icon {
  width: 24px;
  height: 24px;
  fill: #a3a1a1;
  margin-top: 0;
  margin-left: 0;
}

.mdh-info-box__info-note {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #333;
}

onereg-mdh-package {
  min-width: 100%;
}

.mdh-package__wrapper {
  height: 100%;
}

.mdh-package__box {
  background-color: rgb(242.76, 242.76, 242.76);
  border-radius: 4px;
}

.mdh-package__content {
  border: 1px solid rgb(218.28, 218.28, 218.28);
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
}
.mdh-package__content h2.mdh-package__title {
  text-align: center;
  margin: 8px 0 16px 0;
  font-size: 20px;
  line-height: 28px;
}
.mdh-package__content hr {
  border-top: 1px solid rgb(218.28, 218.28, 218.28);
  margin-top: 0;
  margin-bottom: 8px;
}

.mdh-package__start {
  font-size: 14px;
  line-height: 22px;
  font-weight: 700;
  color: #5cb82a;
  text-align: center;
}

.mdh-package__text {
  margin-top: 4px;
  margin-bottom: 16px;
  font-size: 12px;
  line-height: 18px;
  color: rgb(81.6, 81.6, 81.6);
  text-align: center;
}

.mdh-package__features {
  padding: 0;
  margin: 0;
}
.mdh-package__features li {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  gap: 8px;
}
.mdh-package__features li:last-child {
  margin-bottom: 0;
}
.mdh-package__features li::before {
  display: none;
}
.mdh-package__features pos-svg-icon {
  width: 24px;
  height: 24px;
  fill: #1c449b;
}
.mdh-package__features span {
  display: inline-flex;
  font-size: 14px;
  line-height: 22px;
}

.mdh-package__free-level {
  text-align: center;
  margin-top: 4px;
  padding: 6px 0;
}
.mdh-package__free-level span {
  display: inline-block;
  padding: 3px 0;
  font-size: 14px;
  line-height: 22px;
  color: #1c449b;
  cursor: pointer;
  vertical-align: top;
}

.onereg-list__mdh-call-to-action,
.onereg-list__alias-mdh-tip {
  word-break: normal;
}

.onereg-list__mdh-call-to-action {
  display: block;
}

onereg-mdh-terms-and-conditions {
  display: block;
}

.pos-input-checkbox__label {
  padding: 0;
}

.pos-input-checkbox {
  margin: 0;
}

.onereg-mdh-terms__info {
  margin-left: 36px;
}

.code-send-view__form {
  display: flex;
  flex-direction: column;
  max-width: 464px;
  row-gap: 16px;
}

.code-send-view__back-button {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  margin-left: 12px;
}

.code-send-view__back-button-text {
  margin-left: 16px;
  color: #1c449b;
  font-size: 12px;
}

.code-send-view__back-button-icon {
  width: 8px;
  height: 12px;
  fill: #1c449b;
}

.code-send-view__title {
  margin: 0;
  font-weight: 700;
}

.code-send-view__description {
  margin: 0;
}

.code-send-view__phone-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
  padding: 16px 0;
  background-color: rgb(242.76, 242.76, 242.76);
}

.code-send-view__phone-label {
  margin: 0 0 8px;
  color: rgb(81.6, 81.6, 81.6);
}

.code-send-view__mobile-phone {
  margin: 0;
}

.code-send-view__button {
  display: block;
  width: 100%;
}

[pos-button].code-send-view__button {
  padding-top: 9px;
  padding-bottom: 9px;
  line-height: 22px;
}

.code-verification-view__form {
  display: flex;
  flex-direction: column;
  width: 464px;
  row-gap: 16px;
}

.code-verification-view__title {
  margin-bottom: 0;
  font-weight: 700;
}

.code-verification-view__description {
  display: inline-block;
  margin-bottom: 0;
}

.code-verification-view__mobile-phone {
  color: #333;
}

.code-verification-view__code-fields {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
  margin-bottom: 8px;
  padding: 16px 0;
  background-color: rgb(242.76, 242.76, 242.76);
}

.code-verification-view__code-fields-label {
  margin: 0 0 8px;
  color: rgb(81.6, 81.6, 81.6);
}

.code-verification-view__button {
  display: block;
  width: 100%;
}

[pos-button].code-verification-view__button {
  padding-top: 9px;
  padding-bottom: 9px;
  line-height: 22px;
}

.code-verification-view__pending-message {
  margin-top: 8px;
}

.mtan__container {
  padding-top: 88px;
}

@media screen and (min-width: 1200px) {
  .mtan__container--content {
    min-height: 620px;
  }
}
@media screen and (max-width: 767px) {
  .mtan__container {
    padding: 24px 16px 0;
  }
}
onereg-app:has(.mtan__container) {
  height: 100%;
}

.onereg-registration-app:has(.mtan__container) {
  flex: 1;
  margin: 0;
}

.onereg-teaser {
  top: 56px;
  right: 0;
  left: auto;
  width: 304px;
  background-color: transparent;
}
.onereg-teaser.onereg-teaser--mtan {
  top: 0;
}

.form__loading-container {
  position: relative;
}

.form__loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.form__panels {
  display: flex;
  flex-direction: column;
  max-width: 464px;
}

.multistep .form__panels {
  flex-direction: row;
  max-width: 100%;
}

.form__panels section {
  margin-bottom: 16px;
}

.multistep .form__panels section {
  min-width: 100%;
  margin-bottom: 0;
}

.onereg-responsive-block-host {
  display: flex;
  justify-content: center;
}

.onereg-responsive-block {
  width: 100%;
}

.onereg-form {
  max-width: 464px;
}

.onereg-pwmanager-fix-field {
  display: none;
}

.phone-input select {
  min-width: 100px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.phone-input input {
  border-left-width: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.phone-input input:hover,
.phone-input input:focus {
  padding-left: 7px;
  border-left-width: 1px;
}

.form__panel--alt-email {
  display: none;
}

.form__panel--advanced h2 {
  font-size: 24px;
  line-height: 32px;
}
.form__panel--advanced h2 .onereg-info-icon {
  margin-top: 6px;
}
.form__panel--advanced .onereg-step-content {
  padding-top: 12px;
}
.form__panel--advanced .pos-info-box {
  color: rgb(81.6, 81.6, 81.6);
  background-color: rgb(241.38, 243.78, 249);
}
.form__panel--advanced .pos-info-box .pos-info-box__inner-wrapper {
  padding: 0;
  font-size: 10px;
  line-height: 14px;
}
.form__panel--advanced .pos-info-box .pos-info-box__inner-wrapper + .pos-svg-icon {
  display: none;
}
.form__panel--advanced .pos-info-box.pos-content-box--visible {
  margin-bottom: 8px;
}
.form__panel--advanced .pos-input-radio {
  margin-left: 0;
}
.form__panel--advanced .pos-input-radio__border {
  margin: 2px;
}
.form__panel--advanced .pos-input-radio__label {
  padding: 0;
  align-items: center;
}
.form__panel--advanced .pos-input-radio__labeltext {
  line-height: 20px;
}
.form__panel--advanced .pos-label {
  font-weight: 700;
}
.form__panel--advanced .pos-input input {
  color: #333;
}
.form__panel--advanced .pos-form-wrapper,
.form__panel--advanced .pos-form-message__wrapper {
  margin-bottom: 8px;
}
.form__panel--advanced .pos-form-message--negative-top-margin .pos-form-message__wrapper {
  margin-top: 0;
}

@media screen and (min-width: 1200px) {
  .onereg-responsive-block {
    width: 944px;
  }
}
@media screen and (max-width: 767px) {
  .form__loading-container {
    height: 100%;
  }
  .form__panels {
    flex-direction: row;
    max-width: 100%;
    height: 100%;
  }
  .form__panels section {
    min-width: 100%;
    height: 100%;
  }
  .form-body {
    max-width: 100%;
    overflow-x: hidden;
  }
  .form-body.multistep {
    height: 100%;
    padding: 0 8px;
  }
  .onereg-responsive-block-host {
    height: 100%;
  }
}
@media screen and (min-width: 768px) and (max-width: 1199px) {
  .onereg-app:not(.ismultistep--always) .onereg-responsive-block {
    width: 464px;
  }
}
@media screen and (min-width: 768px) {
  .onereg-app:not(.ismultistep--always) .form-body:not(.multistep) > .form__panels {
    transform: translate3d(0, 0, 0) !important;
  }
}
@media screen and (min-width: 1200px) {
  .onereg-app:not(.ismultistep--always) .form-body.multistep {
    width: 464px;
  }
}

.onereg-app.ismultistep--always .form__loading-container {
  height: 100%;
}
.onereg-app.ismultistep--always .form__panels {
  flex-direction: row;
  max-width: 100%;
  height: 100%;
}
.onereg-app.ismultistep--always .form__panels section {
  min-width: 100%;
  height: 100%;
}
.onereg-app.ismultistep--always .form-body {
  max-width: 100%;
  overflow-x: hidden;
  height: 100%;
  padding: 0 16px;
}
.onereg-app.ismultistep--always .onereg-responsive-block-host {
  height: 100%;
}
@media screen and (min-width: 768px) and (max-width: 1199px) {
  .onereg-app.ismultistep--always .onereg-responsive-block {
    width: 464px;
  }
}
@media screen and (min-width: 768px) {
  .onereg-app.ismultistep--always .form-body {
    width: 464px;
    padding: 0 0 16px;
    max-width: 100%;
  }
}
@media screen and (min-width: 1200px) {
  .onereg-app.ismultistep--always .onereg-teaser {
    top: 0;
  }
}

@media screen and (max-width: 767px) {
  .onereg-password-advanced__fieldset {
    height: 100%;
  }
}
.onereg-password-advanced__fieldset .onereg-progress-bar__wrapper {
  margin-bottom: 8px;
}
.onereg-password-advanced__fieldset .pos-input-toggle.pos-input-toggle--active,
.onereg-password-advanced__fieldset .pos-input-toggle:hover {
  fill: #1c449b;
}

.onereg-password-advanced__minlength-message {
  margin-bottom: 8px;
  font-size: 10px;
  line-height: 14px;
  color: #999999;
}

.onereg-password-advanced__confirm-password {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 0;
  width: 0;
  overflow: hidden;
}

.password-recovery-advanced__fieldset--mtan-enabled h3 {
  margin-top: 12px;
}
.password-recovery-advanced__fieldset:not(.password-recovery-advanced__fieldset--mtan-enabled) .recovery-phone-input {
  margin-top: 12px;
}

@media screen and (max-width: 767px) {
  .password-recovery-advanced__fieldset {
    height: 100%;
  }
}
.form__panel--password-recovery-advanced {
  margin-left: -8px;
}

.password-recovery-advanced__button-wrapper {
  display: flex;
  margin-top: auto;
}

.password-recovery-advanced__button {
  display: block;
  width: 100%;
}

[pos-button].password-recovery-advanced__button {
  padding-top: 9px;
  padding-bottom: 9px;
  line-height: 22px;
}

.cta__button {
  margin-top: auto;
}

.password-recovery-advanced__code-fields.hidden {
  display: none;
}

[pos-button].resend-mtan-button {
  margin-top: 16px;
  padding: 3px 12px;
}
[pos-button].resend-mtan-button .pos-svg-icon {
  width: 18px;
  height: 18px;
  margin-left: -2px;
  margin-right: 10px;
}

pos-form-message + .resend-mtan-button {
  margin-top: 8px;
}

.mtan-button__countdown {
  margin-left: 6px;
}

.recovery-phone-input {
  position: relative;
  margin-bottom: 8px;
  gap: 8px;
}
.recovery-phone-input .pos-input input:disabled,
.recovery-phone-input .pos-input select:disabled {
  opacity: 1;
}

.edit-phone-number-button {
  position: absolute;
  right: 6px;
  top: 6px;
}

.password-recovery-advanced__code-fields {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  margin-top: 8px;
  padding: 16px 0;
  background-color: rgb(242.76, 242.76, 242.76);
}
.password-recovery-advanced__code-fields pos-form-message:last-child .pos-form-message__wrapper {
  margin-bottom: 0;
}

.password-recovery-advanced__code-fields-label {
  margin: 0 0 8px;
  color: rgb(81.6, 81.6, 81.6);
}

.recovery-phone-input *:disabled:is(:-webkit-autofill, :-webkit-autofill) {
  -webkit-text-fill-color: #999999;
  -webkit-box-shadow: 0 0 0 1000px rgb(242.76, 242.76, 242.76) inset;
}

.recovery-phone-input *:disabled:is(:-webkit-autofill, :autofill) {
  -webkit-text-fill-color: #999999;
  -webkit-box-shadow: 0 0 0 1000px rgb(242.76, 242.76, 242.76) inset;
}

@media screen and (max-width: 767px) {
  .password-recovery__fieldset {
    height: 100%;
  }
}
.form__panel--password-recovery .pos-input-checkbox {
  margin-left: -8px;
}

.password-recovery__email-form-row:not(.password-recovery__email-form-row--hidden) {
  display: block;
}

.password-recovery__email-form-row--hidden {
  display: none;
}

.onereg-progress-bar__wrapper {
  margin-bottom: 27px;
}

@media screen and (max-width: 767px) {
  .onereg-password__fieldset {
    height: 100%;
  }
}
.onereg-password__minlength-message .pos-form-message__wrapper .pos-svg-icon {
  display: none;
}
.onereg-password__minlength-message .pos-form-message__wrapper .pos-form-message-text {
  padding-left: 0;
}

.onereg-pay-info-box {
  position: relative;
  width: 100%;
  border-radius: 4px;
}
.onereg-pay-info-box--teaser {
  margin-bottom: 16px;
  background-color: rgb(241.38, 243.78, 249);
}

.onereg-pay-info-box__section {
  display: flex;
  position: relative;
  flex-direction: column;
  align-content: center;
  align-items: center;
  margin: 0 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgb(218.28, 218.28, 218.28);
  text-align: center;
}
.onereg-pay-info-box__section:last-child {
  border-bottom-color: transparent;
}
.onereg-pay-info-box__section--horizontal {
  flex-direction: row;
  text-align: left;
}
@media screen and (max-width: 767px) {
  .onereg-pay-info-box__section--horizontal {
    flex-direction: column;
  }
}
@media screen and (max-width: 767px) {
  .onereg-pay-info-box__section {
    padding: 8px 0;
    text-align: center;
  }
}

.onereg-pay-info-box__icon {
  display: flex;
  align-self: start;
  margin-right: 24px;
  padding: 5px 0 0;
  justify-self: start;
}
@media screen and (max-width: 767px) {
  .onereg-pay-info-box__icon {
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 0 0 32px;
    padding-top: 20px;
  }
}
.onereg-pay-info-box__icon .pos-svg {
  width: 92px;
  height: 100px;
  margin: 0 16px;
  fill: #1c449b;
}

.onereg-pay-info-box__leading-description {
  font-size: 16px;
  line-height: 24px;
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #333;
  margin: 0 0 8px;
  font-family: var(--font-family-header);
  font-weight: 400;
}

.onereg-pay-info-box__leading-product-name {
  font-size: 20px;
  line-height: 28px;
  font-weight: var(--font-weight-header);
  font-family: var(--font-family-header);
  color: rgb(81.6, 81.6, 81.6);
  margin: 0 0 8px;
}

.onereg-pay-info-box__leading-storage-space {
  margin-bottom: 16px;
  font-size: 12px;
}
@media screen and (max-width: 767px) {
  .onereg-pay-info-box__leading-storage-space {
    margin-bottom: 8px;
  }
}

.onereg-pay-info-box__test-period-title {
  margin-bottom: 8px;
  color: #5cb82a;
}

.onereg-pay-info-box__test-period-description {
  margin-bottom: 0;
  font-size: 12px;
}

.onereg-pay-info-box__list {
  margin: 0 0 8px;
  padding: 0;
  font-size: 12px;
  list-style: none;
}

.onereg-pay-info-box__list-item {
  margin-bottom: 8px;
}
.onereg-pay-info-box__list-item::before {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 8px;
  border-radius: 50%;
  background-color: rgb(45.9, 45.9, 45.9);
  content: "";
}

.onereg-pay-info-box__all-features-link {
  font-size: 12px;
}
@media screen and (max-width: 767px) {
  .onereg-pay-info-box__all-features-link {
    margin-bottom: 8px;
  }
}

onereg-pay-package {
  min-width: 100%;
}

.pay-package__wrapper {
  height: 100%;
}

.pay-package__content {
  border: 1px solid rgb(218.28, 218.28, 218.28);
  border-radius: 4px;
  padding: 16px;
}
.pay-package__content h2.pay-package__title {
  text-align: center;
  margin: 8px 0 16px 0;
  font-size: 20px;
  line-height: 28px;
}
.pay-package__content hr {
  border-top: 1px solid rgb(218.28, 218.28, 218.28);
  margin-top: 0;
  margin-bottom: 8px;
}

.pay-package__free-start {
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 22px;
  font-weight: 700;
  color: #5cb82a;
  text-align: center;
}

.pay-package__text {
  margin-bottom: 16px;
  font-size: 12px;
  line-height: 18px;
  color: rgb(81.6, 81.6, 81.6);
  text-align: center;
}

.pay-package__features {
  padding: 0;
  margin: 0;
}
.pay-package__features li {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  gap: 8px;
}
.pay-package__features li:last-child {
  margin-bottom: 0;
}
.pay-package__features li::before {
  display: none;
}
.pay-package__features pos-svg-icon {
  width: 24px;
  height: 24px;
  fill: #1c449b;
}
.pay-package__features span {
  display: inline-flex;
  font-size: 14px;
  line-height: 22px;
}

.pay-package__free-level {
  text-align: center;
  margin-top: 4px;
  padding: 6px 0;
}
.pay-package__free-level span {
  display: inline-block;
  padding: 3px 0;
  font-size: 14px;
  line-height: 22px;
  color: #1c449b;
  cursor: pointer;
  vertical-align: top;
}

.onereg-payment-advanced__checkbox {
  margin: 0;
  padding-top: 4px;
  display: block;
}
.onereg-payment-advanced__checkbox .pos-input-checkbox__border {
  flex-shrink: 0;
}
.onereg-payment-advanced__checkbox .pos-input-checkbox__label {
  padding-left: 0;
  padding-right: 0;
}
.onereg-payment-advanced__checkbox .onereg-payment-advanced__checkbox-wrapper {
  margin-top: 4px;
}
.onereg-payment-advanced__checkbox .pos-input-checkbox__labeltext {
  line-height: unset;
}
.onereg-payment-advanced__checkbox .onereg-payment__checkbox__labeltext {
  font-size: 14px;
  line-height: 20px;
}

.onereg-payment {
  margin-top: 16px;
}

.onereg-payment__header {
  margin-top: 32px;
}

.onereg-payment__checkbox .pos-input-checkbox__border {
  flex-shrink: 0;
}

.pos-input-checkbox__labeltext {
  line-height: unset;
}

.onereg-payment__checkbox__labeltext {
  font-size: 12px;
}

.onereg-payment__consent {
  margin-top: 32px;
}

.onereg-payment__consent-title {
  margin-right: 8px;
}

.onereg-payment-panel__body {
  margin-top: 16px;
}

onereg-personal-info {
  min-width: 100%;
}
onereg-personal-info .onereg-plz-field {
  width: 65px;
}

@media screen and (min-width: 768px) {
  .form-header-steps {
    display: none;
  }
}
.onereg-progress-meter {
  width: 100%;
}

@media screen and (max-width: 767px) {
  .onereg-progress-meter {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}
.onereg-progress-meter__grow-container {
  position: relative;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.onereg-progress-meter__container {
  display: none;
  justify-content: flex-start;
  width: 100%;
  border-radius: 4px;
  background-color: #fff;
  overflow: hidden;
}

@media screen and (max-width: 767px) {
  .onereg-progress-meter__container {
    display: flex;
  }
}
.multistep .onereg-progress-meter__container {
  display: flex;
}

.onereg-progress-meter__item {
  position: relative;
  flex: 1;
  height: 32px;
  margin-left: 3px;
  text-align: center;
}

.onereg-progress-meter__item.onereg-progress-meter__item--active {
  padding: 0 16px 0 24px;
}

.onereg-progress-meter__item:first-of-type {
  margin-left: -6px;
}

.onereg-progress-meter__item:last-of-type {
  margin-right: -6px;
}

.onereg-progress-meter__item::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 50%;
  transform: skew(-25deg, 0deg);
  background: rgb(241.38, 243.78, 249);
  content: "";
}

.onereg-progress-meter__item::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  transform: skew(25deg, 0deg);
  background: rgb(241.38, 243.78, 249);
  content: "";
}

.onereg-progress-meter__item.onereg-progress-meter__item--active::before,
.onereg-progress-meter__item.onereg-progress-meter__item--active::after,
.onereg-progress-meter__item.onereg-progress-meter__item--passed::before,
.onereg-progress-meter__item.onereg-progress-meter__item--passed::after {
  background-color: #1c449b;
}

.onereg-progress-meter__item.onereg-progress-meter__item--passed {
  cursor: pointer;
}

.onereg-progress-meter__item .onereg-progress-meter__text {
  position: absolute;
  top: 6px;
  right: 6px;
  left: 6px;
  color: #fff;
  line-height: 20px;
  white-space: nowrap;
  visibility: hidden;
  z-index: 2;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.onereg-progress-meter__item.onereg-progress-meter__item--active .onereg-progress-meter__text {
  visibility: visible;
}

.onereg-progress-meter__buttons {
  display: none;
}

.onereg-progress-meter__buttons-back {
  color: #333;
}

@media screen and (max-width: 767px) {
  .onereg-progress-meter__buttons {
    display: flex;
    width: 100%;
  }
}
.onereg-progress-meter__buttons-text {
  width: 100%;
}

.onereg-progress-meter__buttons-back,
.onereg-progress-meter__buttons-next {
  width: 100%;
  height: 40px;
  margin-bottom: 8px;
}

.multistep .onereg-progress-meter__buttons {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  flex-wrap: wrap;
  padding-top: 8px;
}

.onereg-progress-meter__buttons-back.onereg-progress-meter__buttons--hidden,
.onereg-progress-meter__buttons-next.onereg-progress-meter__buttons--hidden {
  display: none;
}

.onereg-progress-meter__advanced-container {
  display: none;
  height: 6px;
  margin: 0 -16px 32px;
  border-radius: 0 0 2px 2px;
  background: rgb(218.28, 218.28, 218.28);
}
.onereg-progress-meter__advanced-container__bar {
  height: 6px;
  border-radius: 0 0 2px 2px;
  background: #5cb82a;
}
@media screen and (min-width: 768px) {
  .onereg-progress-meter__advanced-container {
    border-radius: 2px;
    margin: 0 0 32px;
  }
  .onereg-progress-meter__advanced-container__bar {
    border-radius: 2px;
  }
}

.onereg-app.ismultistep--always .onereg-progress-meter__container {
  display: none;
}
.onereg-app.ismultistep--always .onereg-progress-meter__advanced-container {
  display: block;
}
.onereg-app.ismultistep--always .onereg-progress-meter__buttons {
  display: flex;
  width: 100%;
  padding-bottom: 16px;
}
.onereg-app.ismultistep--always .onereg-progress-meter {
  display: flex;
  flex-direction: column;
  height: 100%;
}
@media screen and (min-width: 768px) {
  .onereg-app.ismultistep--always .onereg-progress-meter {
    height: auto;
    min-height: 538px;
  }
}

.onereg-suggestion-item-advanced-cell {
  display: flex;
  justify-content: space-between;
  flex: 1 1 auto;
  align-items: center;
  padding: 8px;
  word-break: break-all;
  border: solid 1px rgb(193.8, 193.8, 193.8);
  background-color: #fff;
  border-radius: 4px;
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.onereg-suggestion-item-advanced-cell--highlighted {
  border: 1px solid #1c449b;
  background: rgb(214.14, 221.34, 237);
}

.onereg-suggestion-item-advanced-cell:hover {
  border: 1px solid #1c449b;
  background: rgb(242.76, 242.76, 242.76);
}

.onereg-suggestion-item-advanced-row {
  position: relative;
  display: flex;
  margin-bottom: 8px;
  cursor: pointer;
}
.onereg-suggestion-item-advanced-row:last-child {
  margin-bottom: 0;
}

@media screen and (max-width: 767px) {
  .onereg-suggestion-item-advanced-row {
    flex-wrap: wrap;
  }
}
.onereg-suggestion-item-advanced__text {
  font-size: 12px;
  font-weight: 700;
  color: rgb(81.6, 81.6, 81.6);
  line-height: 18px;
}

.onereg-suggestion-item-advanced__price {
  font-size: 10px;
  font-weight: 700;
  color: #1c449b;
  line-height: 14px;
  margin-top: 4px;
}

.onereg-suggestion-item-advanced__additional-text {
  font-size: 10px;
  color: #333;
  line-height: 14px;
  margin-top: 4px;
}

.onereg-suggestion-item-advanced__icon {
  height: 24px;
  width: 24px;
  color: #1c449b;
  fill: #1c449b;
}

.onereg-suggestion-item-advanced__icon--loading {
  margin-left: auto;
  margin-top: -2px;
  transform: scale(0.8);
}

.onereg-suggestion-item-advanced__icon--loading .pos-spinner__bar {
  background-color: #1c449b;
}

.onereg-suggestion-item-advanced__highlight {
  position: absolute;
  top: 0;
  right: 8px;
  display: flex;
  padding: 4px 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 0 0 4px 4px;
  background-color: #1c449b;
  color: #fff;
  font-size: 12px;
  line-height: 18px;
}
.onereg-suggestion-item-advanced__highlight pos-svg-icon {
  width: 20px;
  height: 20px;
  fill: #fff;
}

.onereg-suggestions-box__suggestion-row {
  position: relative;
  cursor: pointer;
}

.onereg-suggestions-box__suggestion-cell {
  flex: 1 1 auto;
}

.onereg-suggestions-box__suggestion-icon {
  width: 24px;
  height: 24px;
  margin-right: 5px;
  padding: 2px;
  fill: #1c449b;
}

.onereg-suggestions-box {
  display: block;
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
  background-color: rgb(241.38, 243.78, 249);
}

.onereg-suggestions-box__row {
  display: flex;
  position: relative;
  align-items: center;
  min-height: 22px;
  padding: 8px;
}

@media screen and (max-width: 767px) {
  .onereg-suggestions-box__row {
    flex-wrap: wrap;
  }
}
.onereg-suggestions-box__row--highlighted {
  font-weight: bold;
}

.onereg-suggestions-box__row--separated:not(:last-child)::after {
  position: absolute;
  bottom: 0;
  left: 16px;
  width: calc(100% - 32px);
  height: 1px;
  background: rgb(214.14, 221.34, 237);
  content: "";
}

.onereg-suggestions-box__cell {
  display: flex;
  align-items: center;
  padding: 8px;
  line-height: 22px;
  word-break: break-all;
}

.onereg-suggestions-box__cell--link {
  justify-content: flex-end;
}

.onereg-suggestions-box__title-cell {
  flex: 1;
}

.onereg-suggestions-box__close-cell {
  min-width: 24px;
  text-align: right;
}

.onereg-suggestions-box__close-button {
  width: 24px;
  height: 24px;
  padding: 2px;
  fill: #999999;
}

.onereg-suggestions-box__close-button .pos-svg {
  width: inherit;
  overflow: visible;
}

.onereg-suggestions-box__footer-cell {
  display: block;
}

.onereg-suggestions-box__show-more-button-cell,
.onereg-suggestions-box__footer-cell {
  flex: 1 1 auto;
}

.onereg-suggestions-box__title-cell,
.onereg-suggestions-box__footer-cell {
  word-break: normal;
}

.teaser-frame {
  width: 100%;
  height: 100%;
  border: 0;
  background-color: transparent;
}

.form__create-account-text {
  flex: 1;
  white-space: normal;
}

.terms-and-conditions-advanced__wrapper,
.terms-and-conditions-advanced__onereg-agb-box *,
.terms-and-conditions-advanced__onereg-mdh-terms-and-conditions *,
.terms-and-conditions-advanced__onereg-free-level-two > * {
  font-size: 12px;
  line-height: 18px;
}

.terms-and-conditions-advanced__wrapper {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
.terms-and-conditions-advanced__wrapper h2,
.terms-and-conditions-advanced__wrapper .onereg-mdh-login-info {
  margin-bottom: 20px;
}
.terms-and-conditions-advanced__wrapper pos-form-message,
.terms-and-conditions-advanced__wrapper .terms-and-conditions-advanced__pending-message {
  font-size: 14px;
  line-height: 20px;
}
.terms-and-conditions-advanced__wrapper .onereg-mdh-terms__container .pos-input-checkbox__labeltext {
  padding-right: 0;
}
.terms-and-conditions-advanced__wrapper .onereg-mdh-terms__container .pos-input-checkbox__labeltext p {
  margin-bottom: 8px;
}

.terms-and-conditions-advanced__wrapper .onereg-euds,
.terms-and-conditions-advanced__wrapper .agb-box {
  margin-bottom: 8px;
}

.terms-and-conditions-advanced__onereg-agb-box > * {
  padding: 10px;
}

.terms-and-conditions-advanced__onereg-free-level-two > * {
  margin-bottom: 8px;
}

.terms-and-conditions-advanced__cta-button {
  display: block;
  width: 100%;
  height: 40px;
}

[pos-button].terms-and-conditions-advanced__cta-button {
  padding-top: 7px;
  padding-bottom: 7px;
}

.terms-and-conditions-advanced__content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.form__create-account-text {
  flex: 1;
  white-space: normal;
}

.terms-and-conditions__body {
  font-size: 12px;
}

.terms-and-conditions__cta-button {
  display: block;
  width: 100%;
}

[pos-button].terms-and-conditions__cta-button {
  padding-top: 7px;
  padding-bottom: 7px;
}

@media screen and (max-width: 767px) {
  .terms-and-conditions__fieldset {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .terms-and-conditions__footer {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: flex-end;
  }
  .terms-and-conditions__cta-button {
    height: 40px;
  }
}
.terms-and-conditions__pending-message {
  margin-top: 8px;
}

.terms-and-conditions__error-message {
  margin-top: 8px;
}

.terms-and-conditions__wrapper {
  margin-top: 16px;
}

@media screen and (min-width: 1200px) {
  .terms-and-conditions__wrapper {
    margin-bottom: 16px;
  }
}
.onereg-text-skeleton {
  display: flex;
  width: 100%;
  height: 10px;
  margin: 8px 0;
  border-radius: 10px;
  background: linear-gradient(90deg, rgba(150, 150, 150, 0.1) 25%, rgba(150, 150, 150, 0.5) 50%, rgba(150, 150, 150, 0.1) 75%);
  background-size: 200% 100%;
  animation: skeleton-pulse 2s linear infinite;
}

@keyframes skeleton-pulse {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
.onereg-email-badge {
  background-color: rgb(242.76, 242.76, 242.76);
  padding: 8px 16px;
  font-size: 12px;
  color: rgb(81.6, 81.6, 81.6);
  font-weight: 700;
  font-family: Verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
  border-radius: 4px;
  width: -moz-fit-content;
  width: fit-content;
  margin-bottom: 16px;
  height: 34px;
}

.onereg-email-badge:not(:last-child) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

onereg-user-email-badge {
  display: none;
}

.onereg-app.ismultistep onereg-user-email-badge {
  display: flex;
}

.onereg-email-badge__highlight {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
  background-color: #1c449b;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
}

.onereg-email-badge__domain-icon {
  height: 24px;
  width: 24px;
  color: #fff;
  fill: currentColor;
}

.code-inputs__wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.code-inputs__label {
  display: block;
  min-height: 18px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 0;
  line-height: 18px;
  text-align: center;
  white-space: nowrap;
}

.code-inputs__input {
  width: 35px;
  height: 32px;
  margin: 0 4px;
  padding: 4px 8px;
  border: 1px solid rgb(193.8, 193.8, 193.8);
  border-radius: 4px;
  text-align: center;
  -webkit-appearance: none;
          appearance: none;
  -moz-appearance: textfield;
}
.code-inputs__input:focus {
  border-color: #1c449b;
  outline: 0;
}
.code-inputs__input::-webkit-outer-spin-button, .code-inputs__input::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
}
@supports (-webkit-touch-callout: none) {
  .code-inputs__input:focus {
    font-size: 16px;
  }
}

.clearfix::before, .clearfix::after {
  display: table;
  content: " ";
}
.clearfix::after {
  clear: both;
}

.center-block {
  display: block;
  margin-right: auto;
  margin-left: auto;
}

.pull-right {
  float: right !important;
}

.pull-left {
  float: left !important;
}

.show {
  display: block !important;
}

.invisible {
  visibility: hidden;
}

.text-hide {
  border: 0;
  background-color: transparent;
  color: transparent;
  font: 0/0 a;
  text-shadow: none;
}

.hidden {
  display: none !important;
}

.affix {
  position: fixed;
}

.u-scroll-vertical {
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}
/*# sourceMappingURL=onereg_intenseblue.css.map */
