var TcfApi=function(e){"use strict";function t(e,t,s,n){return new(s||(s=Promise))((function(r,i){function o(e){try{c(n.next(e))}catch(e){i(e)}}function a(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(o,a)}c((n=n.apply(e,t||[])).next())}))}function s(e,t){var s,n,r,i={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(s)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(i=0)),i;)try{if(s=1,n&&(r=2&a[0]?n.return:a[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,a[1])).done)return r;switch(n=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,n=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(r=i.trys,(r=r.length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){i.label=a[1];break}if(6===a[0]&&i.label<r[1]){i.label=r[1],r=a;break}if(r&&i.label<r[2]){i.label=r[2],i.ops.push(a);break}r[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],n=0}finally{s=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function n(e,t,s){if(s||2===arguments.length)for(var n,r=0,i=t.length;r<i;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))}var r,i;"function"==typeof SuppressedError&&SuppressedError,e.TcfApiCommands=void 0,(r=e.TcfApiCommands||(e.TcfApiCommands={}))[r.getTCData=0]="getTCData",r[r.ping=1]="ping",r[r.addEventListener=2]="addEventListener",r[r.removeEventListener=3]="removeEventListener",r[r.updateTCString=4]="updateTCString",r[r.getTCString=5]="getTCString",r[r.getACString=6]="getACString",r[r.getPermission=7]="getPermission",r[r.getPermissionFeatures=8]="getPermissionFeatures",r[r.getTCFVersion=9]="getTCFVersion",r[r.getTCLastUpdated=10]="getTCLastUpdated",r[r.getTCStringUtil=11]="getTCStringUtil",r[r.getAppInfo=12]="getAppInfo",r[r.getConsentState=13]="getConsentState",e.PermissionFeatures=void 0,(i=e.PermissionFeatures||(e.PermissionFeatures={}))[i.publisher=0]="publisher",i[i.purpose=1]="purpose",i[i.vendor=2]="vendor",i[i.special=3]="special",i[i.brainTracking=4]="brainTracking",i[i.uimservTracking=5]="uimservTracking",i[i.agofTracking=6]="agofTracking",i[i.tgp=7]="tgp",i[i.oewaTracking=8]="oewaTracking",i[i.googleAnalyticsTracking=9]="googleAnalyticsTracking",i[i.googleAnalyticsTrackingInternational=10]="googleAnalyticsTrackingInternational",i[i.editorialPersonalization=11]="editorialPersonalization",i[i.aditionAds=12]="aditionAds",i[i.siteSpectTesting=13]="siteSpectTesting",i[i.fullConsent=14]="fullConsent",i[i.publisherCustomPurpose=15]="publisherCustomPurpose",i[i.googleAc=16]="googleAc",i[i.amazonAds=17]="amazonAds",i[i.specialGeoFeature=18]="specialGeoFeature",i[i.googleAdsConversionTracking=19]="googleAdsConversionTracking",i[i.searchBasicAds=20]="searchBasicAds",i[i.searchPersonalisedAds=21]="searchPersonalisedAds",i[i.sentryErrorTracing=22]="sentryErrorTracing",i[i.internalRetargeting=23]="internalRetargeting",i[i.googleBasicAds=24]="googleBasicAds",i[i.googleAdRemarketing=25]="googleAdRemarketing",i[i.vgwortTracking=26]="vgwortTracking",i[i.opinary=27]="opinary",i[i.appmonPerformanceTracing=28]="appmonPerformanceTracing",i[i.searchBasicAdsAmazon=29]="searchBasicAdsAmazon",i[i.searchBasicAdsVerizon=30]="searchBasicAdsVerizon",i[i.searchBasicAdsGoogle=31]="searchBasicAdsGoogle",i[i.searchYahooImage=32]="searchYahooImage",i[i.firstPartyCookie=33]="firstPartyCookie",i[i.thirdPartyCookie=34]="thirdPartyCookie",i[i.facebookInternational=35]="facebookInternational",i[i.linkedinInternational=36]="linkedinInternational",i[i.instagramInternational=37]="instagramInternational",i[i.microsoftInternational=38]="microsoftInternational",i[i.civey=39]="civey",i[i.tradedoublerTracking=40]="tradedoublerTracking",i[i.shoppingCartSmartPanel=41]="shoppingCartSmartPanel",i[i.hotjar=42]="hotjar",i[i.sovendus=43]="sovendus",i[i.contentGarden=44]="contentGarden",i[i.theGlobalGroup=45]="theGlobalGroup",i[i.shopping24=46]="shopping24",i[i.heimspiel=47]="heimspiel",i[i.metaDach=48]="metaDach",i[i.tcfMicrosoftDach=49]="tcfMicrosoftDach",i[i.tcfGoogleDach=50]="tcfGoogleDach",i[i.tcfTiktokDach=51]="tcfTiktokDach",i[i.tcfTrackFree=52]="tcfTrackFree";var o="PRODUCTION",a="euconsent-v2",c="GDNA",u="uiconsent",l="TCF_API_DEBUG",p="https://dl.gmx.net/cookie-transfer/index.html",d="https://dl.gmx.at/cookie-transfer/index.html",h="https://dl.gmx.ch/cookie-transfer/index.html",g=!0,f="mail.com",m=755,C=793;/*! js-cookie v3.0.5 | MIT */
function v(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)e[n]=s[n]}return e}var y,b,S,I,E,A=function e(t,s){function n(e,n,r){if("undefined"!=typeof document){"number"==typeof(r=v({},s,r)).expires&&(r.expires=new Date(Date.now()+864e5*r.expires)),r.expires&&(r.expires=r.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var i="";for(var o in r)r[o]&&(i+="; "+o,!0!==r[o]&&(i+="="+r[o].split(";")[0]));return document.cookie=e+"="+t.write(n,e)+i}}return Object.create({set:n,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var s=document.cookie?document.cookie.split("; "):[],n={},r=0;r<s.length;r++){var i=s[r].split("="),o=i.slice(1).join("=");try{var a=decodeURIComponent(i[0]);if(n[a]=t.read(o,a),e===a)break}catch(e){}}return e?n[e]:n}},remove:function(e,t){n(e,"",v({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,v({},this.attributes,t))},withConverter:function(t){return e(v({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(s)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"}),w=-1!==window.location.hostname.indexOf("co.uk")?window.location.hostname.split(".").slice(-3).join("."):window.location.hostname.split(".").slice(-2).join(".");function T(){return w}function L(){return T()===f&&"string"==typeof A.get(c)&&"string"==typeof A.get(a)?A.get(a)||"":"string"==typeof A.get(c)?null:A.get(a)||""}function V(){var e=A.get("idcc")||"1";return"1"===e||"2"===e}function P(){return"2.0"}function _(){return[565,10,32,91,39,40,70,m,76,45,69,253,52,539,42,C]}function O(){return{environment:o,version:"1.55.0",tcf:{cmpId:R(),cmpVersion:2,serviceSpecific:g}}}function k(){var e=T();return e===f||"gmx.com"===e||"gmx.es"===e||"gmx.fr"===e||"gmx.co.uk"===e}function R(){return k()||"united-internet-media.de"===T()?28:167}class N extends Error{constructor(e){super(e),this.name="DecodingError"}}class D extends Error{constructor(e){super(e),this.name="EncodingError"}}class F extends Error{constructor(e){super(e),this.name="GVLError"}}class U extends Error{constructor(e,t,s=""){super(`invalid value ${t} passed for ${e} ${s}`),this.name="TCModelError"}}class x{static DICT="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";static REVERSE_DICT=new Map([["A",0],["B",1],["C",2],["D",3],["E",4],["F",5],["G",6],["H",7],["I",8],["J",9],["K",10],["L",11],["M",12],["N",13],["O",14],["P",15],["Q",16],["R",17],["S",18],["T",19],["U",20],["V",21],["W",22],["X",23],["Y",24],["Z",25],["a",26],["b",27],["c",28],["d",29],["e",30],["f",31],["g",32],["h",33],["i",34],["j",35],["k",36],["l",37],["m",38],["n",39],["o",40],["p",41],["q",42],["r",43],["s",44],["t",45],["u",46],["v",47],["w",48],["x",49],["y",50],["z",51],["0",52],["1",53],["2",54],["3",55],["4",56],["5",57],["6",58],["7",59],["8",60],["9",61],["-",62],["_",63]]);static BASIS=6;static LCM=24;static encode(e){if(!/^[0-1]+$/.test(e))throw new D("Invalid bitField");const t=e.length%this.LCM;e+=t?"0".repeat(this.LCM-t):"";let s="";for(let t=0;t<e.length;t+=this.BASIS)s+=this.DICT[parseInt(e.substr(t,this.BASIS),2)];return s}static decode(e){if(!/^[A-Za-z0-9\-_]+$/.test(e))throw new N("Invalidly encoded Base64URL string");let t="";for(let s=0;s<e.length;s++){const n=this.REVERSE_DICT.get(e[s]).toString(2);t+="0".repeat(this.BASIS-n.length)+n}return t}}class M{static langSet=new Set(["AR","BG","BS","CA","CS","CY","DA","DE","EL","EN","ES","ET","EU","FI","FR","GL","HE","HI","HR","HU","ID","IT","JA","KA","KO","LT","LV","MK","MS","MT","NL","NO","PL","PT-BR","PT-PT","RO","RU","SK","SL","SQ","SR-LATN","SR-CYRL","SV","SW","TH","TL","TR","UK","VI","ZH","ZH-HANT"]);has(e){return M.langSet.has(e)}parseLanguage(e){const t=(e=e.toUpperCase()).split("-")[0];if(e.length>=2&&2==t.length){if(M.langSet.has(e))return e;if(M.langSet.has(t))return t;const s=t+"-"+t;if(M.langSet.has(s))return s;for(const s of M.langSet)if(-1!==s.indexOf(e)||-1!==s.indexOf(t))return s}throw new Error(`unsupported language ${e}`)}forEach(e){M.langSet.forEach(e)}get size(){return M.langSet.size}}class G{static cmpId="cmpId";static cmpVersion="cmpVersion";static consentLanguage="consentLanguage";static consentScreen="consentScreen";static created="created";static supportOOB="supportOOB";static isServiceSpecific="isServiceSpecific";static lastUpdated="lastUpdated";static numCustomPurposes="numCustomPurposes";static policyVersion="policyVersion";static publisherCountryCode="publisherCountryCode";static publisherCustomConsents="publisherCustomConsents";static publisherCustomLegitimateInterests="publisherCustomLegitimateInterests";static publisherLegitimateInterests="publisherLegitimateInterests";static publisherConsents="publisherConsents";static publisherRestrictions="publisherRestrictions";static purposeConsents="purposeConsents";static purposeLegitimateInterests="purposeLegitimateInterests";static purposeOneTreatment="purposeOneTreatment";static specialFeatureOptins="specialFeatureOptins";static useNonStandardTexts="useNonStandardTexts";static vendorConsents="vendorConsents";static vendorLegitimateInterests="vendorLegitimateInterests";static vendorListVersion="vendorListVersion";static vendorsAllowed="vendorsAllowed";static vendorsDisclosed="vendorsDisclosed";static version="version"}class B{clone(){const e=new this.constructor;return Object.keys(this).forEach((t=>{const s=this.deepClone(this[t]);void 0!==s&&(e[t]=s)})),e}deepClone(e){const t=typeof e;if("number"===t||"string"===t||"boolean"===t)return e;if(null!==e&&"object"===t){if("function"==typeof e.clone)return e.clone();if(e instanceof Date)return new Date(e.getTime());if(void 0!==e[Symbol.iterator]){const t=[];for(const s of e)t.push(this.deepClone(s));return e instanceof Array?t:new e.constructor(t)}{const t={};for(const s in e)e.hasOwnProperty(s)&&(t[s]=this.deepClone(e[s]));return t}}}}!function(e){e[e.NOT_ALLOWED=0]="NOT_ALLOWED",e[e.REQUIRE_CONSENT=1]="REQUIRE_CONSENT",e[e.REQUIRE_LI=2]="REQUIRE_LI"}(y||(y={}));class H extends B{static hashSeparator="-";purposeId_;restrictionType;constructor(e,t){super(),void 0!==e&&(this.purposeId=e),void 0!==t&&(this.restrictionType=t)}static unHash(e){const t=e.split(this.hashSeparator),s=new H;if(2!==t.length)throw new U("hash",e);return s.purposeId=parseInt(t[0],10),s.restrictionType=parseInt(t[1],10),s}get hash(){if(!this.isValid())throw new Error("cannot hash invalid PurposeRestriction");return`${this.purposeId}${H.hashSeparator}${this.restrictionType}`}get purposeId(){return this.purposeId_}set purposeId(e){this.purposeId_=e}isValid(){return Number.isInteger(this.purposeId)&&this.purposeId>0&&(this.restrictionType===y.NOT_ALLOWED||this.restrictionType===y.REQUIRE_CONSENT||this.restrictionType===y.REQUIRE_LI)}isSameAs(e){return this.purposeId===e.purposeId&&this.restrictionType===e.restrictionType}}class j extends B{bitLength=0;map=new Map;gvl_;has(e){return this.map.has(e)}isOkToHave(e,t,s){let n=!0;if(this.gvl?.vendors){const r=this.gvl.vendors[s];if(r)if(e===y.NOT_ALLOWED)n=r.legIntPurposes.includes(t)||r.purposes.includes(t);else if(r.flexiblePurposes.length)switch(e){case y.REQUIRE_CONSENT:n=r.flexiblePurposes.includes(t)&&r.legIntPurposes.includes(t);break;case y.REQUIRE_LI:n=r.flexiblePurposes.includes(t)&&r.purposes.includes(t)}else n=!1;else n=!1}return n}add(e,t){if(this.isOkToHave(t.restrictionType,t.purposeId,e)){const s=t.hash;this.has(s)||(this.map.set(s,new Set),this.bitLength=0),this.map.get(s).add(e)}}restrictPurposeToLegalBasis(e){const t=Array.from(this.gvl.vendorIds),s=e.hash,n=t[t.length-1],r=[...Array(n).keys()].map((e=>e+1));if(this.has(s))for(let e=1;e<=n;e++)this.map.get(s).add(e);else this.map.set(s,new Set(r)),this.bitLength=0}getVendors(e){let t=[];if(e){const s=e.hash;this.has(s)&&(t=Array.from(this.map.get(s)))}else{const e=new Set;this.map.forEach((t=>{t.forEach((t=>{e.add(t)}))})),t=Array.from(e)}return t.sort(((e,t)=>e-t))}getRestrictionType(e,t){let s;return this.getRestrictions(e).forEach((e=>{e.purposeId===t&&(void 0===s||s>e.restrictionType)&&(s=e.restrictionType)})),s}vendorHasRestriction(e,t){let s=!1;const n=this.getRestrictions(e);for(let e=0;e<n.length&&!s;e++)s=t.isSameAs(n[e]);return s}getMaxVendorId(){let e=0;return this.map.forEach((t=>{e=Math.max(Array.from(t)[t.size-1],e)})),e}getRestrictions(e){const t=[];return this.map.forEach(((s,n)=>{e?s.has(e)&&t.push(H.unHash(n)):t.push(H.unHash(n))})),t}getPurposes(){const e=new Set;return this.map.forEach(((t,s)=>{e.add(H.unHash(s).purposeId)})),Array.from(e)}remove(e,t){const s=t.hash,n=this.map.get(s);n&&(n.delete(e),0==n.size&&(this.map.delete(s),this.bitLength=0))}set gvl(e){this.gvl_||(this.gvl_=e,this.map.forEach(((e,t)=>{const s=H.unHash(t);Array.from(e).forEach((t=>{this.isOkToHave(s.restrictionType,s.purposeId,t)||e.delete(t)}))})))}get gvl(){return this.gvl_}isEmpty(){return 0===this.map.size}get numRestrictions(){return this.map.size}}!function(e){e.COOKIE="cookie",e.WEB="web",e.APP="app"}(b||(b={})),function(e){e.CORE="core",e.VENDORS_DISCLOSED="vendorsDisclosed",e.VENDORS_ALLOWED="vendorsAllowed",e.PUBLISHER_TC="publisherTC"}(S||(S={}));class z{static ID_TO_KEY=[S.CORE,S.VENDORS_DISCLOSED,S.VENDORS_ALLOWED,S.PUBLISHER_TC];static KEY_TO_ID={[S.CORE]:0,[S.VENDORS_DISCLOSED]:1,[S.VENDORS_ALLOWED]:2,[S.PUBLISHER_TC]:3}}class Q extends B{bitLength=0;maxId_=0;set_=new Set;*[Symbol.iterator](){for(let e=1;e<=this.maxId;e++)yield[e,this.has(e)]}values(){return this.set_.values()}get maxId(){return this.maxId_}has(e){return this.set_.has(e)}unset(e){Array.isArray(e)?e.forEach((e=>this.unset(e))):"object"==typeof e?this.unset(Object.keys(e).map((e=>Number(e)))):(this.set_.delete(Number(e)),this.bitLength=0,e===this.maxId&&(this.maxId_=0,this.set_.forEach((e=>{this.maxId_=Math.max(this.maxId,e)}))))}isIntMap(e){let t="object"==typeof e;return t=t&&Object.keys(e).every((t=>{let s=Number.isInteger(parseInt(t,10));return s=s&&this.isValidNumber(e[t].id),s=s&&void 0!==e[t].name,s})),t}isValidNumber(e){return parseInt(e,10)>0}isSet(e){let t=!1;return e instanceof Set&&(t=Array.from(e).every(this.isValidNumber)),t}set(e){if(Array.isArray(e))e.forEach((e=>this.set(e)));else if(this.isSet(e))this.set(Array.from(e));else if(this.isIntMap(e))this.set(Object.keys(e).map((e=>Number(e))));else{if(!this.isValidNumber(e))throw new U("set()",e,"must be positive integer array, positive integer, Set<number>, or IntMap");this.set_.add(e),this.maxId_=Math.max(this.maxId,e),this.bitLength=0}}empty(){this.set_=new Set,this.maxId_=0}forEach(e){for(let t=1;t<=this.maxId;t++)e(this.has(t),t)}get size(){return this.set_.size}setAll(e){this.set(e)}}class ${static[G.cmpId]=12;static[G.cmpVersion]=12;static[G.consentLanguage]=12;static[G.consentScreen]=6;static[G.created]=36;static[G.isServiceSpecific]=1;static[G.lastUpdated]=36;static[G.policyVersion]=6;static[G.publisherCountryCode]=12;static[G.publisherLegitimateInterests]=24;static[G.publisherConsents]=24;static[G.purposeConsents]=24;static[G.purposeLegitimateInterests]=24;static[G.purposeOneTreatment]=1;static[G.specialFeatureOptins]=12;static[G.useNonStandardTexts]=1;static[G.vendorListVersion]=12;static[G.version]=6;static anyBoolean=1;static encodingType=1;static maxId=16;static numCustomPurposes=6;static numEntries=12;static numRestrictions=12;static purposeId=6;static restrictionType=2;static segmentType=3;static singleOrRange=1;static vendorId=16}class W{static encode(e){return String(Number(e))}static decode(e){return"1"===e}}class J{static encode(e,t){let s;if("string"==typeof e&&(e=parseInt(e,10)),s=e.toString(2),s.length>t||e<0)throw new D(`${e} too large to encode into ${t}`);return s.length<t&&(s="0".repeat(t-s.length)+s),s}static decode(e,t){if(t!==e.length)throw new N("invalid bit length");return parseInt(e,2)}}class Y{static encode(e,t){return J.encode(Math.round(e.getTime()/100),t)}static decode(e,t){if(t!==e.length)throw new N("invalid bit length");const s=new Date;return s.setTime(100*J.decode(e,t)),s}}class q{static encode(e,t){let s="";for(let n=1;n<=t;n++)s+=W.encode(e.has(n));return s}static decode(e,t){if(e.length!==t)throw new N("bitfield encoding length mismatch");const s=new Q;for(let n=1;n<=t;n++)W.decode(e[n-1])&&s.set(n);return s.bitLength=e.length,s}}class K{static encode(e,t){const s=(e=e.toUpperCase()).charCodeAt(0)-65,n=e.charCodeAt(1)-65;if(s<0||s>25||n<0||n>25)throw new D(`invalid language code: ${e}`);if(t%2==1)throw new D(`numBits must be even, ${t} is not valid`);t/=2;return J.encode(s,t)+J.encode(n,t)}static decode(e,t){let s;if(t!==e.length||e.length%2)throw new N("invalid bit length for language");{const t=65,n=e.length/2,r=J.decode(e.slice(0,n),n)+t,i=J.decode(e.slice(n),n)+t;s=String.fromCharCode(r)+String.fromCharCode(i)}return s}}class Z{static encode(e){let t=J.encode(e.numRestrictions,$.numRestrictions);if(!e.isEmpty()){const s=(t,s)=>{for(let n=t+1;n<=s;n++)if(e.gvl.vendorIds.has(n))return n;return t};e.getRestrictions().forEach((n=>{t+=J.encode(n.purposeId,$.purposeId),t+=J.encode(n.restrictionType,$.restrictionType);const r=e.getVendors(n),i=r.length;let o=0,a=0,c="";for(let e=0;e<i;e++){const t=r[e];if(0===a&&(o++,a=t),e===i-1||r[e+1]>s(t,r[i-1])){const e=!(t===a);c+=W.encode(e),c+=J.encode(a,$.vendorId),e&&(c+=J.encode(t,$.vendorId)),a=0}}t+=J.encode(o,$.numEntries),t+=c}))}return t}static decode(e){let t=0;const s=new j,n=J.decode(e.substr(t,$.numRestrictions),$.numRestrictions);t+=$.numRestrictions;for(let r=0;r<n;r++){const n=J.decode(e.substr(t,$.purposeId),$.purposeId);t+=$.purposeId;const r=J.decode(e.substr(t,$.restrictionType),$.restrictionType);t+=$.restrictionType;const i=new H(n,r),o=J.decode(e.substr(t,$.numEntries),$.numEntries);t+=$.numEntries;for(let n=0;n<o;n++){const n=W.decode(e.substr(t,$.anyBoolean));t+=$.anyBoolean;const r=J.decode(e.substr(t,$.vendorId),$.vendorId);if(t+=$.vendorId,n){const n=J.decode(e.substr(t,$.vendorId),$.vendorId);if(t+=$.vendorId,n<r)throw new N(`Invalid RangeEntry: endVendorId ${n} is less than ${r}`);for(let e=r;e<=n;e++)s.add(e,i)}else s.add(r,i)}}return s.bitLength=t,s}}!function(e){e[e.FIELD=0]="FIELD",e[e.RANGE=1]="RANGE"}(I||(I={}));class X{static encode(e){const t=[];let s,n=[],r=J.encode(e.maxId,$.maxId),i="";const o=$.maxId+$.encodingType,a=o+e.maxId,c=2*$.vendorId+$.singleOrRange+$.numEntries;let u=o+$.numEntries;return e.forEach(((r,o)=>{if(i+=W.encode(r),s=e.maxId>c&&u<a,s&&r){e.has(o+1)?0===n.length&&(n.push(o),u+=$.singleOrRange,u+=$.vendorId):(n.push(o),u+=$.vendorId,t.push(n),n=[])}})),s?(r+=String(I.RANGE),r+=this.buildRangeEncoding(t)):(r+=String(I.FIELD),r+=i),r}static decode(e,t){let s,n=0;const r=J.decode(e.substr(n,$.maxId),$.maxId);n+=$.maxId;const i=J.decode(e.charAt(n),$.encodingType);if(n+=$.encodingType,i===I.RANGE){if(s=new Q,1===t){if("1"===e.substr(n,1))throw new N("Unable to decode default consent=1");n++}const r=J.decode(e.substr(n,$.numEntries),$.numEntries);n+=$.numEntries;for(let t=0;t<r;t++){const t=W.decode(e.charAt(n));n+=$.singleOrRange;const r=J.decode(e.substr(n,$.vendorId),$.vendorId);if(n+=$.vendorId,t){const t=J.decode(e.substr(n,$.vendorId),$.vendorId);n+=$.vendorId;for(let e=r;e<=t;e++)s.set(e)}else s.set(r)}}else{const t=e.substr(n,r);n+=r,s=q.decode(t,r)}return s.bitLength=n,s}static buildRangeEncoding(e){const t=e.length;let s=J.encode(t,$.numEntries);return e.forEach((e=>{const t=1===e.length;s+=W.encode(!t),s+=J.encode(e[0],$.vendorId),t||(s+=J.encode(e[1],$.vendorId))})),s}}function ee(){return{[G.version]:J,[G.created]:Y,[G.lastUpdated]:Y,[G.cmpId]:J,[G.cmpVersion]:J,[G.consentScreen]:J,[G.consentLanguage]:K,[G.vendorListVersion]:J,[G.policyVersion]:J,[G.isServiceSpecific]:W,[G.useNonStandardTexts]:W,[G.specialFeatureOptins]:q,[G.purposeConsents]:q,[G.purposeLegitimateInterests]:q,[G.purposeOneTreatment]:W,[G.publisherCountryCode]:K,[G.vendorConsents]:X,[G.vendorLegitimateInterests]:X,[G.publisherRestrictions]:Z,segmentType:J,[G.vendorsDisclosed]:X,[G.vendorsAllowed]:X,[G.publisherConsents]:q,[G.publisherLegitimateInterests]:q,[G.numCustomPurposes]:J,[G.publisherCustomConsents]:q,[G.publisherCustomLegitimateInterests]:q}}class te{1={[S.CORE]:[G.version,G.created,G.lastUpdated,G.cmpId,G.cmpVersion,G.consentScreen,G.consentLanguage,G.vendorListVersion,G.purposeConsents,G.vendorConsents]};2={[S.CORE]:[G.version,G.created,G.lastUpdated,G.cmpId,G.cmpVersion,G.consentScreen,G.consentLanguage,G.vendorListVersion,G.policyVersion,G.isServiceSpecific,G.useNonStandardTexts,G.specialFeatureOptins,G.purposeConsents,G.purposeLegitimateInterests,G.purposeOneTreatment,G.publisherCountryCode,G.vendorConsents,G.vendorLegitimateInterests,G.publisherRestrictions],[S.PUBLISHER_TC]:[G.publisherConsents,G.publisherLegitimateInterests,G.numCustomPurposes,G.publisherCustomConsents,G.publisherCustomLegitimateInterests],[S.VENDORS_ALLOWED]:[G.vendorsAllowed],[S.VENDORS_DISCLOSED]:[G.vendorsDisclosed]}}class se{1=[S.CORE];2=[S.CORE];constructor(e,t){if(2===e.version)if(e.isServiceSpecific)this[2].push(S.PUBLISHER_TC);else{const s=!(!t||!t.isForVendors);s&&!0!==e[G.supportOOB]||this[2].push(S.VENDORS_DISCLOSED),s&&(e[G.supportOOB]&&e[G.vendorsAllowed].size>0&&this[2].push(S.VENDORS_ALLOWED),this[2].push(S.PUBLISHER_TC))}}}class ne{static fieldSequence=new te;static encode(e,t){let s;try{s=this.fieldSequence[String(e.version)][t]}catch(s){throw new D(`Unable to encode version: ${e.version}, segment: ${t}`)}let n="";t!==S.CORE&&(n=J.encode(z.KEY_TO_ID[t],$.segmentType));const r=ee();return s.forEach((s=>{const i=e[s],o=r[s];let a=$[s];void 0===a&&this.isPublisherCustom(s)&&(a=Number(e[G.numCustomPurposes]));try{n+=o.encode(i,a)}catch(e){throw new D(`Error encoding ${t}->${s}: ${e.message}`)}})),x.encode(n)}static decode(e,t,s){const n=x.decode(e);let r=0;s===S.CORE&&(t.version=J.decode(n.substr(r,$[G.version]),$[G.version])),s!==S.CORE&&(r+=$.segmentType);const i=this.fieldSequence[String(t.version)][s],o=ee();return i.forEach((e=>{const s=o[e];let i=$[e];if(void 0===i&&this.isPublisherCustom(e)&&(i=Number(t[G.numCustomPurposes])),0!==i){const o=n.substr(r,i);if(t[e]=s===X?s.decode(o,t.version):s.decode(o,i),Number.isInteger(i))r+=i;else{if(!Number.isInteger(t[e].bitLength))throw new N(e);r+=t[e].bitLength}}})),t}static isPublisherCustom(e){return 0===e.indexOf("publisherCustom")}}class re{static processor=[e=>e,(e,t)=>{e.publisherRestrictions.gvl=t,e.purposeLegitimateInterests.unset([1,3,4,5,6]);const s=new Map;return s.set("legIntPurposes",e.vendorLegitimateInterests),s.set("purposes",e.vendorConsents),s.forEach(((s,n)=>{s.forEach(((r,i)=>{if(r){const r=t.vendors[i];if(!r||r.deletedDate)s.unset(i);else if(0===r[n].length)if("legIntPurposes"===n&&0===r.purposes.length&&0===r.legIntPurposes.length&&r.specialPurposes.length>0)s.set(i);else if("legIntPurposes"===n&&r.purposes.length>0&&0===r.legIntPurposes.length&&r.specialPurposes.length>0)s.set(i);else if(e.isServiceSpecific)if(0===r.flexiblePurposes.length)s.unset(i);else{const t=e.publisherRestrictions.getRestrictions(i);let r=!1;for(let e=0,s=t.length;e<s&&!r;e++)r=t[e].restrictionType===y.REQUIRE_CONSENT&&"purposes"===n||t[e].restrictionType===y.REQUIRE_LI&&"legIntPurposes"===n;r||s.unset(i)}else s.unset(i)}}))})),e.vendorsDisclosed.set(t.vendors),e}];static process(e,t){const s=e.gvl;if(!s)throw new D("Unable to encode TCModel without a GVL");if(!s.isReady)throw new D("Unable to encode TCModel tcModel.gvl.readyPromise is not resolved");(e=e.clone()).consentLanguage=s.language.slice(0,2).toUpperCase(),t?.version>0&&t?.version<=this.processor.length?e.version=t.version:e.version=this.processor.length;const n=e.version-1;if(!this.processor[n])throw new D(`Invalid version: ${e.version}`);return this.processor[n](e,s)}}class ie{static absCall(e,t,s,n){return new Promise(((r,i)=>{const o=new XMLHttpRequest;o.withCredentials=s,o.addEventListener("load",(()=>{if(o.readyState==XMLHttpRequest.DONE)if(o.status>=200&&o.status<300){let e=o.response;if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}r(e)}else i(new Error(`HTTP Status: ${o.status} response type: ${o.responseType}`))})),o.addEventListener("error",(()=>{i(new Error("error"))})),o.addEventListener("abort",(()=>{i(new Error("aborted"))})),null===t?o.open("GET",e,!0):o.open("POST",e,!0),o.responseType="json",o.timeout=n,o.ontimeout=()=>{i(new Error("Timeout "+n+"ms "+e))},o.send(t)}))}static post(e,t,s=!1,n=0){return this.absCall(e,JSON.stringify(t),s,n)}static fetch(e,t=!1,s=0){return this.absCall(e,null,t,s)}}class oe extends B{static LANGUAGE_CACHE=new Map;static CACHE=new Map;static LATEST_CACHE_KEY=0;static DEFAULT_LANGUAGE="EN";static consentLanguages=new M;static baseUrl_;static set baseUrl(e){if(/^https?:\/\/vendorlist\.consensu\.org\//.test(e))throw new F("Invalid baseUrl!  You may not pull directly from vendorlist.consensu.org and must provide your own cache");e.length>0&&"/"!==e[e.length-1]&&(e+="/"),this.baseUrl_=e}static get baseUrl(){return this.baseUrl_}static latestFilename="vendor-list.json";static versionedFilename="archives/vendor-list-v[VERSION].json";static languageFilename="purposes-[LANG].json";readyPromise;gvlSpecificationVersion;vendorListVersion;tcfPolicyVersion;lastUpdated;purposes;specialPurposes;features;specialFeatures;isReady_=!1;vendors_;vendorIds;fullVendorList;byPurposeVendorMap;bySpecialPurposeVendorMap;byFeatureVendorMap;bySpecialFeatureVendorMap;stacks;dataCategories;lang_;cacheLang_;isLatest=!1;constructor(e,t){super();let s=oe.baseUrl,n=t?.language;if(n)try{n=oe.consentLanguages.parseLanguage(n)}catch(e){throw new F("Error during parsing the language: "+e.message)}if(this.lang_=n||oe.DEFAULT_LANGUAGE,this.cacheLang_=n||oe.DEFAULT_LANGUAGE,this.isVendorList(e))this.populate(e),this.readyPromise=Promise.resolve();else{if(!s)throw new F("must specify GVL.baseUrl before loading GVL json");if(e>0){const t=e;oe.CACHE.has(t)?(this.populate(oe.CACHE.get(t)),this.readyPromise=Promise.resolve()):(s+=oe.versionedFilename.replace("[VERSION]",String(t)),this.readyPromise=this.fetchJson(s))}else oe.CACHE.has(oe.LATEST_CACHE_KEY)?(this.populate(oe.CACHE.get(oe.LATEST_CACHE_KEY)),this.readyPromise=Promise.resolve()):(this.isLatest=!0,this.readyPromise=this.fetchJson(s+oe.latestFilename))}}static emptyLanguageCache(e){let t=!1;return null==e&&oe.LANGUAGE_CACHE.size>0?(oe.LANGUAGE_CACHE=new Map,t=!0):"string"==typeof e&&this.consentLanguages.has(e.toUpperCase())&&(oe.LANGUAGE_CACHE.delete(e.toUpperCase()),t=!0),t}static emptyCache(e){let t=!1;return Number.isInteger(e)&&e>=0?(oe.CACHE.delete(e),t=!0):void 0===e&&(oe.CACHE=new Map,t=!0),t}cacheLanguage(){oe.LANGUAGE_CACHE.has(this.cacheLang_)||oe.LANGUAGE_CACHE.set(this.cacheLang_,{purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,dataCategories:this.dataCategories})}async fetchJson(e){try{this.populate(await ie.fetch(e))}catch(e){throw new F(e.message)}}getJson(){return{gvlSpecificationVersion:this.gvlSpecificationVersion,vendorListVersion:this.vendorListVersion,tcfPolicyVersion:this.tcfPolicyVersion,lastUpdated:this.lastUpdated,purposes:this.clonePurposes(),specialPurposes:this.cloneSpecialPurposes(),features:this.cloneFeatures(),specialFeatures:this.cloneSpecialFeatures(),stacks:this.cloneStacks(),...this.dataCategories?{dataCategories:this.cloneDataCategories()}:{},vendors:this.cloneVendors()}}cloneSpecialFeatures(){const e={};for(const t of Object.keys(this.specialFeatures))e[t]=oe.cloneFeature(this.specialFeatures[t]);return e}cloneFeatures(){const e={};for(const t of Object.keys(this.features))e[t]=oe.cloneFeature(this.features[t]);return e}cloneStacks(){const e={};for(const t of Object.keys(this.stacks))e[t]=oe.cloneStack(this.stacks[t]);return e}cloneDataCategories(){const e={};for(const t of Object.keys(this.dataCategories))e[t]=oe.cloneDataCategory(this.dataCategories[t]);return e}cloneSpecialPurposes(){const e={};for(const t of Object.keys(this.specialPurposes))e[t]=oe.clonePurpose(this.specialPurposes[t]);return e}clonePurposes(){const e={};for(const t of Object.keys(this.purposes))e[t]=oe.clonePurpose(this.purposes[t]);return e}static clonePurpose(e){return{id:e.id,name:e.name,description:e.description,...e.descriptionLegal?{descriptionLegal:e.descriptionLegal}:{},...e.illustrations?{illustrations:Array.from(e.illustrations)}:{}}}static cloneFeature(e){return{id:e.id,name:e.name,description:e.description,...e.descriptionLegal?{descriptionLegal:e.descriptionLegal}:{},...e.illustrations?{illustrations:Array.from(e.illustrations)}:{}}}static cloneDataCategory(e){return{id:e.id,name:e.name,description:e.description}}static cloneStack(e){return{id:e.id,name:e.name,description:e.description,purposes:Array.from(e.purposes),specialFeatures:Array.from(e.specialFeatures)}}static cloneDataRetention(e){return{..."number"==typeof e.stdRetention?{stdRetention:e.stdRetention}:{},purposes:{...e.purposes},specialPurposes:{...e.specialPurposes}}}static cloneVendorUrls(e){return e.map((e=>({langId:e.langId,privacy:e.privacy,...e.legIntClaim?{legIntClaim:e.legIntClaim}:{}})))}static cloneVendor(e){return{id:e.id,name:e.name,purposes:Array.from(e.purposes),legIntPurposes:Array.from(e.legIntPurposes),flexiblePurposes:Array.from(e.flexiblePurposes),specialPurposes:Array.from(e.specialPurposes),features:Array.from(e.features),specialFeatures:Array.from(e.specialFeatures),...e.overflow?{overflow:{httpGetLimit:e.overflow.httpGetLimit}}:{},..."number"==typeof e.cookieMaxAgeSeconds||null===e.cookieMaxAgeSeconds?{cookieMaxAgeSeconds:e.cookieMaxAgeSeconds}:{},...void 0!==e.usesCookies?{usesCookies:e.usesCookies}:{},...e.policyUrl?{policyUrl:e.policyUrl}:{},...void 0!==e.cookieRefresh?{cookieRefresh:e.cookieRefresh}:{},...void 0!==e.usesNonCookieAccess?{usesNonCookieAccess:e.usesNonCookieAccess}:{},...e.dataRetention?{dataRetention:this.cloneDataRetention(e.dataRetention)}:{},...e.urls?{urls:this.cloneVendorUrls(e.urls)}:{},...e.dataDeclaration?{dataDeclaration:Array.from(e.dataDeclaration)}:{},...e.deviceStorageDisclosureUrl?{deviceStorageDisclosureUrl:e.deviceStorageDisclosureUrl}:{},...e.deletedDate?{deletedDate:e.deletedDate}:{}}}cloneVendors(){const e={};for(const t of Object.keys(this.fullVendorList))e[t]=oe.cloneVendor(this.fullVendorList[t]);return e}async changeLanguage(e){let t=e;try{t=oe.consentLanguages.parseLanguage(e)}catch(e){throw new F("Error during parsing the language: "+e.message)}const s=e.toUpperCase();if((t.toLowerCase()!==oe.DEFAULT_LANGUAGE.toLowerCase()||oe.LANGUAGE_CACHE.has(s))&&t!==this.lang_)if(this.lang_=t,oe.LANGUAGE_CACHE.has(s)){const e=oe.LANGUAGE_CACHE.get(s);for(const t in e)e.hasOwnProperty(t)&&(this[t]=e[t])}else{const e=oe.baseUrl+oe.languageFilename.replace("[LANG]",this.lang_.toLowerCase());try{await this.fetchJson(e),this.cacheLang_=s,this.cacheLanguage()}catch(e){throw new F("unable to load language: "+e.message)}}}get language(){return this.lang_}isVendorList(e){return void 0!==e&&void 0!==e.vendors}populate(e){this.purposes=e.purposes,this.specialPurposes=e.specialPurposes,this.features=e.features,this.specialFeatures=e.specialFeatures,this.stacks=e.stacks,this.dataCategories=e.dataCategories,this.isVendorList(e)&&(this.gvlSpecificationVersion=e.gvlSpecificationVersion,this.tcfPolicyVersion=e.tcfPolicyVersion,this.vendorListVersion=e.vendorListVersion,this.lastUpdated=e.lastUpdated,"string"==typeof this.lastUpdated&&(this.lastUpdated=new Date(this.lastUpdated)),this.vendors_=e.vendors,this.fullVendorList=e.vendors,this.mapVendors(),this.isReady_=!0,this.isLatest&&oe.CACHE.set(oe.LATEST_CACHE_KEY,this.getJson()),oe.CACHE.has(this.vendorListVersion)||oe.CACHE.set(this.vendorListVersion,this.getJson())),this.cacheLanguage()}mapVendors(e){this.byPurposeVendorMap={},this.bySpecialPurposeVendorMap={},this.byFeatureVendorMap={},this.bySpecialFeatureVendorMap={},Object.keys(this.purposes).forEach((e=>{this.byPurposeVendorMap[e]={legInt:new Set,consent:new Set,flexible:new Set}})),Object.keys(this.specialPurposes).forEach((e=>{this.bySpecialPurposeVendorMap[e]=new Set})),Object.keys(this.features).forEach((e=>{this.byFeatureVendorMap[e]=new Set})),Object.keys(this.specialFeatures).forEach((e=>{this.bySpecialFeatureVendorMap[e]=new Set})),Array.isArray(e)||(e=Object.keys(this.fullVendorList).map((e=>+e))),this.vendorIds=new Set(e),this.vendors_=e.reduce(((e,t)=>{const s=this.vendors_[String(t)];return s&&void 0===s.deletedDate&&(s.purposes.forEach((e=>{this.byPurposeVendorMap[String(e)].consent.add(t)})),s.specialPurposes.forEach((e=>{this.bySpecialPurposeVendorMap[String(e)].add(t)})),s.legIntPurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].legInt.add(t)})),s.flexiblePurposes&&s.flexiblePurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].flexible.add(t)})),s.features.forEach((e=>{this.byFeatureVendorMap[String(e)].add(t)})),s.specialFeatures.forEach((e=>{this.bySpecialFeatureVendorMap[String(e)].add(t)})),e[t]=s),e}),{})}getFilteredVendors(e,t,s,n){const r=e.charAt(0).toUpperCase()+e.slice(1);let i;const o={};return i="purpose"===e&&s?this["by"+r+"VendorMap"][String(t)][s]:this["by"+(n?"Special":"")+r+"VendorMap"][String(t)],i.forEach((e=>{o[String(e)]=this.vendors[String(e)]})),o}getVendorsWithConsentPurpose(e){return this.getFilteredVendors("purpose",e,"consent")}getVendorsWithLegIntPurpose(e){return this.getFilteredVendors("purpose",e,"legInt")}getVendorsWithFlexiblePurpose(e){return this.getFilteredVendors("purpose",e,"flexible")}getVendorsWithSpecialPurpose(e){return this.getFilteredVendors("purpose",e,void 0,!0)}getVendorsWithFeature(e){return this.getFilteredVendors("feature",e)}getVendorsWithSpecialFeature(e){return this.getFilteredVendors("feature",e,void 0,!0)}get vendors(){return this.vendors_}narrowVendorsTo(e){this.mapVendors(e)}get isReady(){return this.isReady_}clone(){const e=new oe(this.getJson());return this.lang_!==oe.DEFAULT_LANGUAGE&&e.changeLanguage(this.lang_),e}static isInstanceOf(e){return"object"==typeof e&&"function"==typeof e.narrowVendorsTo}}class ae extends B{static consentLanguages=oe.consentLanguages;isServiceSpecific_=!1;supportOOB_=!0;useNonStandardTexts_=!1;purposeOneTreatment_=!1;publisherCountryCode_="AA";version_=2;consentScreen_=0;policyVersion_=5;consentLanguage_="EN";cmpId_=0;cmpVersion_=0;vendorListVersion_=0;numCustomPurposes_=0;gvl_;created;lastUpdated;specialFeatureOptins=new Q;purposeConsents=new Q;purposeLegitimateInterests=new Q;publisherConsents=new Q;publisherLegitimateInterests=new Q;publisherCustomConsents=new Q;publisherCustomLegitimateInterests=new Q;customPurposes;vendorConsents=new Q;vendorLegitimateInterests=new Q;vendorsDisclosed=new Q;vendorsAllowed=new Q;publisherRestrictions=new j;constructor(e){super(),e&&(this.gvl=e),this.updated()}set gvl(e){oe.isInstanceOf(e)||(e=new oe(e)),this.gvl_=e,this.publisherRestrictions.gvl=e}get gvl(){return this.gvl_}set cmpId(e){if(e=Number(e),!(Number.isInteger(e)&&e>1))throw new U("cmpId",e);this.cmpId_=e}get cmpId(){return this.cmpId_}set cmpVersion(e){if(e=Number(e),!(Number.isInteger(e)&&e>-1))throw new U("cmpVersion",e);this.cmpVersion_=e}get cmpVersion(){return this.cmpVersion_}set consentScreen(e){if(e=Number(e),!(Number.isInteger(e)&&e>-1))throw new U("consentScreen",e);this.consentScreen_=e}get consentScreen(){return this.consentScreen_}set consentLanguage(e){this.consentLanguage_=e}get consentLanguage(){return this.consentLanguage_}set publisherCountryCode(e){if(!/^([A-z]){2}$/.test(e))throw new U("publisherCountryCode",e);this.publisherCountryCode_=e.toUpperCase()}get publisherCountryCode(){return this.publisherCountryCode_}set vendorListVersion(e){if((e=Number(e)|0)<0)throw new U("vendorListVersion",e);this.vendorListVersion_=e}get vendorListVersion(){return this.gvl?this.gvl.vendorListVersion:this.vendorListVersion_}set policyVersion(e){if(this.policyVersion_=parseInt(e,10),this.policyVersion_<0)throw new U("policyVersion",e)}get policyVersion(){return this.gvl?this.gvl.tcfPolicyVersion:this.policyVersion_}set version(e){this.version_=parseInt(e,10)}get version(){return this.version_}set isServiceSpecific(e){this.isServiceSpecific_=e}get isServiceSpecific(){return this.isServiceSpecific_}set useNonStandardTexts(e){this.useNonStandardTexts_=e}get useNonStandardTexts(){return this.useNonStandardTexts_}set supportOOB(e){this.supportOOB_=e}get supportOOB(){return this.supportOOB_}set purposeOneTreatment(e){this.purposeOneTreatment_=e}get purposeOneTreatment(){return this.purposeOneTreatment_}setAllVendorConsents(){this.vendorConsents.set(this.gvl.vendors)}unsetAllVendorConsents(){this.vendorConsents.empty()}setAllVendorsDisclosed(){this.vendorsDisclosed.set(this.gvl.vendors)}unsetAllVendorsDisclosed(){this.vendorsDisclosed.empty()}setAllVendorsAllowed(){this.vendorsAllowed.set(this.gvl.vendors)}unsetAllVendorsAllowed(){this.vendorsAllowed.empty()}setAllVendorLegitimateInterests(){this.vendorLegitimateInterests.set(this.gvl.vendors)}unsetAllVendorLegitimateInterests(){this.vendorLegitimateInterests.empty()}setAllPurposeConsents(){this.purposeConsents.set(this.gvl.purposes)}unsetAllPurposeConsents(){this.purposeConsents.empty()}setAllPurposeLegitimateInterests(){this.purposeLegitimateInterests.set(this.gvl.purposes)}unsetAllPurposeLegitimateInterests(){this.purposeLegitimateInterests.empty()}setAllSpecialFeatureOptins(){this.specialFeatureOptins.set(this.gvl.specialFeatures)}unsetAllSpecialFeatureOptins(){this.specialFeatureOptins.empty()}setAll(){this.setAllVendorConsents(),this.setAllPurposeLegitimateInterests(),this.setAllSpecialFeatureOptins(),this.setAllPurposeConsents(),this.setAllVendorLegitimateInterests()}unsetAll(){this.unsetAllVendorConsents(),this.unsetAllPurposeLegitimateInterests(),this.unsetAllSpecialFeatureOptins(),this.unsetAllPurposeConsents(),this.unsetAllVendorLegitimateInterests()}get numCustomPurposes(){let e=this.numCustomPurposes_;if("object"==typeof this.customPurposes){const t=Object.keys(this.customPurposes).sort(((e,t)=>Number(e)-Number(t)));e=parseInt(t.pop(),10)}return e}set numCustomPurposes(e){if(this.numCustomPurposes_=parseInt(e,10),this.numCustomPurposes_<0)throw new U("numCustomPurposes",e)}updated(){const e=new Date,t=new Date(Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()));this.created=t,this.lastUpdated=t}}class ce{static encode(e,t){let s,n="";return e=re.process(e,t),s=Array.isArray(t?.segments)?t.segments:new se(e,t)[""+e.version],s.forEach(((t,r)=>{let i="";r<s.length-1&&(i="."),n+=ne.encode(e,t)+i})),n}static decode(e,t){const s=e.split("."),n=s.length;t||(t=new ae);for(let e=0;e<n;e++){const n=s[e],r=x.decode(n.charAt(0)).substr(0,$.segmentType),i=z.ID_TO_KEY[J.decode(r,$.segmentType).toString()];ne.decode(n,t,i)}return t}}!function(e){e[e.verbose=0]="verbose",e[e.info=1]="info",e[e.warnings=2]="warnings",e[e.errors=3]="errors"}(E||(E={}));var ue,le,pe,de,he,ge={debug:function(){for(var e,t=[],s=0;s<arguments.length;s++)t[s]=arguments[s];(e=window.console).debug.apply(e,t)},info:function(){for(var e,t=[],s=0;s<arguments.length;s++)t[s]=arguments[s];(e=window.console).info.apply(e,t)},warn:function(){for(var e,t=[],s=0;s<arguments.length;s++)t[s]=arguments[s];(e=window.console).warn.apply(e,t)},error:function(){for(var e,t=[],s=0;s<arguments.length;s++)t[s]=arguments[s];(e=window.console).error.apply(e,t)}},fe=[ge];function me(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];ue<=E.verbose&&"true"===A.get(l)&&fe.filter((function(e){return"function"==typeof e.debug})).forEach((function(t){t.debug.apply(t,n(["[TCF]->"],e,!1))}))}function Ce(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];ue<=E.warnings&&"true"===A.get(l)&&fe.filter((function(e){return"function"==typeof e.warn})).forEach((function(t){t.warn.apply(t,n(["[TCF]->"],e,!1))}))}function ve(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];ue<=E.errors&&fe.filter((function(e){return"function"==typeof e.error})).forEach((function(t){t.error.apply(t,n(["[TCF]->"],e,!1))}))}function ye(e){var t;try{t=ce.decode(e)}catch(e){return ve("decodeTCString: Could not decode tcString into tcModel, tcString is invalid!"),null}return t&&t.publisherConsents&&t.purposeConsents&&t.vendorConsents&&t.specialFeatureOptins?t:(ve("decodeModel: Decoded model is invalid!"),null)}ue=E.errors,function(e){e.PING="ping",e.GET_TC_DATA="getTCData",e.GET_IN_APP_TC_DATA="getInAppTCData",e.GET_VENDOR_LIST="getVendorList",e.ADD_EVENT_LISTENER="addEventListener",e.REMOVE_EVENT_LISTENER="removeEventListener"}(le||(le={})),function(e){e.STUB="stub",e.LOADING="loading",e.LOADED="loaded",e.ERROR="error"}(pe||(pe={})),function(e){e.VISIBLE="visible",e.HIDDEN="hidden",e.DISABLED="disabled"}(de||(de={})),function(e){e.TC_LOADED="tcloaded",e.CMP_UI_SHOWN="cmpuishown",e.USER_ACTION_COMPLETE="useractioncomplete"}(he||(he={}));class be{listenerId;callback;next;param;success=!0;constructor(e,t,s,n){Object.assign(this,{callback:e,listenerId:s,param:t,next:n});try{this.respond()}catch(e){this.invokeCallback(null)}}invokeCallback(e){const t=null!==e;"function"==typeof this.next?this.callback(this.next,e,t):this.callback(e,t)}}class Se extends be{respond(){this.throwIfParamInvalid(),this.invokeCallback(new Te(this.param,this.listenerId))}throwIfParamInvalid(){if(!(void 0===this.param||Array.isArray(this.param)&&this.param.every(Number.isInteger)))throw new Error("Invalid Parameter")}}class Ie{eventQueue=new Map;queueNumber=0;add(e){return this.eventQueue.set(this.queueNumber,e),this.queueNumber++}remove(e){return this.eventQueue.delete(e)}exec(){this.eventQueue.forEach(((e,t)=>{new Se(e.callback,e.param,t,e.next)}))}clear(){this.queueNumber=0,this.eventQueue.clear()}get size(){return this.eventQueue.size}}class Ee{static apiVersion="2";static tcfPolicyVersion;static eventQueue=new Ie;static cmpStatus=pe.LOADING;static disabled=!1;static displayStatus=de.HIDDEN;static cmpId;static cmpVersion;static eventStatus;static gdprApplies;static tcModel;static tcString;static reset(){delete this.cmpId,delete this.cmpVersion,delete this.eventStatus,delete this.gdprApplies,delete this.tcModel,delete this.tcString,delete this.tcfPolicyVersion,this.cmpStatus=pe.LOADING,this.disabled=!1,this.displayStatus=de.HIDDEN,this.eventQueue.clear()}}class Ae{cmpId=Ee.cmpId;cmpVersion=Ee.cmpVersion;gdprApplies=Ee.gdprApplies;tcfPolicyVersion=Ee.tcfPolicyVersion}class we extends Ae{cmpStatus=pe.ERROR}class Te extends Ae{tcString;listenerId;eventStatus;cmpStatus;isServiceSpecific;useNonStandardTexts;publisherCC;purposeOneTreatment;outOfBand;purpose;vendor;specialFeatureOptins;publisher;constructor(e,t){if(super(),this.eventStatus=Ee.eventStatus,this.cmpStatus=Ee.cmpStatus,this.listenerId=t,Ee.gdprApplies){const t=Ee.tcModel;this.tcString=Ee.tcString,this.isServiceSpecific=t.isServiceSpecific,this.useNonStandardTexts=t.useNonStandardTexts,this.purposeOneTreatment=t.purposeOneTreatment,this.publisherCC=t.publisherCountryCode,this.outOfBand={allowedVendors:this.createVectorField(t.vendorsAllowed,e),disclosedVendors:this.createVectorField(t.vendorsDisclosed,e)},this.purpose={consents:this.createVectorField(t.purposeConsents),legitimateInterests:this.createVectorField(t.purposeLegitimateInterests)},this.vendor={consents:this.createVectorField(t.vendorConsents,e),legitimateInterests:this.createVectorField(t.vendorLegitimateInterests,e)},this.specialFeatureOptins=this.createVectorField(t.specialFeatureOptins),this.publisher={consents:this.createVectorField(t.publisherConsents),legitimateInterests:this.createVectorField(t.publisherLegitimateInterests),customPurpose:{consents:this.createVectorField(t.publisherCustomConsents),legitimateInterests:this.createVectorField(t.publisherCustomLegitimateInterests)},restrictions:this.createRestrictions(t.publisherRestrictions)}}}createRestrictions(e){const t={};if(e.numRestrictions>0){const s=e.getMaxVendorId();for(let n=1;n<=s;n++){const s=n.toString();e.getRestrictions(n).forEach((e=>{const n=e.purposeId.toString();t[n]||(t[n]={}),t[n][s]=e.restrictionType}))}}return t}createVectorField(e,t){return t?t.reduce(((t,s)=>(t[String(s)]=e.has(Number(s)),t)),{}):[...e].reduce(((e,t)=>(e[t[0].toString(10)]=t[1],e)),{})}}class Le extends Te{constructor(e){super(e),delete this.outOfBand}createVectorField(e){return[...e].reduce(((e,t)=>e+=t[1]?"1":"0"),"")}createRestrictions(e){const t={};if(e.numRestrictions>0){const s=e.getMaxVendorId();e.getRestrictions().forEach((e=>{t[e.purposeId.toString()]="_".repeat(s)}));for(let n=0;n<s;n++){const s=n+1;e.getRestrictions(s).forEach((e=>{const s=e.restrictionType.toString(),r=e.purposeId.toString(),i=t[r].substr(0,n),o=t[r].substr(n+1);t[r]=i+s+o}))}}return t}}class Ve extends Ae{cmpLoaded=!0;cmpStatus=Ee.cmpStatus;displayStatus=Ee.displayStatus;apiVersion=String(Ee.apiVersion);gvlVersion;constructor(){super(),Ee.tcModel&&Ee.tcModel.vendorListVersion&&(this.gvlVersion=+Ee.tcModel.vendorListVersion)}}class Pe extends be{respond(){this.invokeCallback(new Ve)}}class _e extends Se{respond(){this.throwIfParamInvalid(),this.invokeCallback(new Le(this.param))}}class Oe extends be{respond(){const e=Ee.tcModel,t=e.vendorListVersion;let s;void 0===this.param&&(this.param=t),s=this.param===t&&e.gvl?e.gvl:new oe(this.param),s.readyPromise.then((()=>{this.invokeCallback(s.getJson())}))}}class ke extends Se{respond(){this.listenerId=Ee.eventQueue.add({callback:this.callback,param:this.param,next:this.next}),super.respond()}}class Re extends be{respond(){this.invokeCallback(Ee.eventQueue.remove(this.param))}}class Ne{static[le.PING]=Pe;static[le.GET_TC_DATA]=Se;static[le.GET_IN_APP_TC_DATA]=_e;static[le.GET_VENDOR_LIST]=Oe;static[le.ADD_EVENT_LISTENER]=ke;static[le.REMOVE_EVENT_LISTENER]=Re}class De{static set_=new Set([0,2,void 0,null]);static has(e){return"string"==typeof e&&(e=Number(e)),this.set_.has(e)}}const Fe="__tcfapi";class Ue{callQueue;customCommands;constructor(e){if(e){let t=le.ADD_EVENT_LISTENER;if(e?.[t])throw new Error(`Built-In Custom Commmand for ${t} not allowed: Use ${le.GET_TC_DATA} instead`);if(t=le.REMOVE_EVENT_LISTENER,e?.[t])throw new Error(`Built-In Custom Commmand for ${t} not allowed`);e?.[le.GET_TC_DATA]&&(e[le.ADD_EVENT_LISTENER]=e[le.GET_TC_DATA],e[le.REMOVE_EVENT_LISTENER]=e[le.GET_TC_DATA]),this.customCommands=e}try{this.callQueue=window[Fe]()||[]}catch(e){this.callQueue=[]}finally{window[Fe]=this.apiCall.bind(this),this.purgeQueuedCalls()}}apiCall(e,t,s,...n){if("string"!=typeof e)s(null,!1);else if(De.has(t)){if("function"!=typeof s)throw new Error("invalid callback function");Ee.disabled?s(new we,!1):this.isCustomCommand(e)||this.isBuiltInCommand(e)?this.isCustomCommand(e)&&!this.isBuiltInCommand(e)?this.customCommands[e](s,...n):e===le.PING?this.isCustomCommand(e)?new Ne[e](this.customCommands[e],n[0],null,s):new Ne[e](s,n[0]):void 0===Ee.tcModel?this.callQueue.push([e,t,s,...n]):this.isCustomCommand(e)&&this.isBuiltInCommand(e)?new Ne[e](this.customCommands[e],n[0],null,s):new Ne[e](s,n[0]):s(null,!1)}else s(null,!1)}purgeQueuedCalls(){const e=this.callQueue;this.callQueue=[],e.forEach((e=>{window[Fe](...e)}))}isCustomCommand(e){return this.customCommands&&"function"==typeof this.customCommands[e]}isBuiltInCommand(e){return void 0!==Ne[e]}}class xe{callResponder;isServiceSpecific;numUpdates=0;constructor(e,t,s=!1,n){this.throwIfInvalidInt(e,"cmpId",2),this.throwIfInvalidInt(t,"cmpVersion",0),Ee.cmpId=e,Ee.cmpVersion=t,Ee.tcfPolicyVersion=5,this.isServiceSpecific=!!s,this.callResponder=new Ue(n)}throwIfInvalidInt(e,t,s){if(!("number"==typeof e&&Number.isInteger(e)&&e>=s))throw new Error(`Invalid ${t}: ${e}`)}update(e,t=!1){if(Ee.disabled)throw new Error("CmpApi Disabled");Ee.cmpStatus=pe.LOADED,t?(Ee.displayStatus=de.VISIBLE,Ee.eventStatus=he.CMP_UI_SHOWN):void 0===Ee.tcModel?(Ee.displayStatus=de.DISABLED,Ee.eventStatus=he.TC_LOADED):(Ee.displayStatus=de.HIDDEN,Ee.eventStatus=he.USER_ACTION_COMPLETE),Ee.gdprApplies=null!==e,Ee.gdprApplies?(""===e?(Ee.tcModel=new ae,Ee.tcModel.cmpId=Ee.cmpId,Ee.tcModel.cmpVersion=Ee.cmpVersion):Ee.tcModel=ce.decode(e),Ee.tcModel.isServiceSpecific=this.isServiceSpecific,Ee.tcfPolicyVersion=Number(Ee.tcModel.policyVersion),Ee.tcString=e):Ee.tcModel=null,0===this.numUpdates?this.callResponder.purgeQueuedCalls():Ee.eventQueue.exec(),this.numUpdates++}disable(){Ee.disabled=!0,Ee.cmpStatus=pe.ERROR}}var Me="[Permission Feature] ->";function Ge(t,s,n){if(void 0===n&&(n=L()),null===n)return"agofTracking"===t?(me(Me,"getPermissionFeature: agofTracking should be always false!"),!1):(me(Me,"getPermissionFeature: GDPR does not apply, applying consent by default!"),!0);if(""===n)return Ce(Me,"getPermissionFeature: Empty tcString, applying privacy by default!"),!1;if(!Object.prototype.hasOwnProperty.call(e.PermissionFeatures,t))throw new Error("getPermissionFeature: Requested permission".concat(s?"":"Feature"," '").concat(t,"'").concat(s?" with pId '".concat(s,"'"):""," is not available!"));var r;if(Ee.tcModel&&Ee.tcString===n)me(Me,"getPermissionFeature: Using existing tcModel"),r=Ee.tcModel;else{if(!("string"==typeof n&&n.length>0))return Ce(Me,"getPermissionFeature: No tcString available or not typeof string, applying privacy by default!",n),!1;me(Me,"getPermissionFeature: Creating new tcModel"),r=ye(n)}return r?("number"==typeof s&&(s=[s]),{brainTracking:[1,8,10].every((function(e){return r.publisherConsents.has(e)}))&&V(),uimservTracking:[1,3,5,7,8,9,10].every((function(e){return r.publisherConsents.has(e)}))&&V(),agofTracking:[1,7,8,9,25].every((function(e){return r.publisherConsents.has(e)}))&&[1,7,8,9,25].every((function(e){return r.purposeConsents.has(e)}))&&r.vendorConsents.has(730),oewaTracking:[1,8].every((function(e){return r.publisherConsents.has(e)}))&&[1,8].every((function(e){return r.purposeConsents.has(e)}))&&r.vendorConsents.has(730),appmonPerformanceTracing:[1,8].every((function(e){return r.publisherConsents.has(e)})),googleAnalyticsTracking:r.publisherConsents.has(1)&&r.purposeConsents.has(3)&&r.vendorConsents.has(m),googleAnalyticsTrackingInternational:[1,7,8,10].every((function(e){return r.publisherConsents.has(e)}))&&[1,7,8,10].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(3),aditionAds:[1,2,3,4,7].every((function(e){return r.purposeConsents.has(e)}))&&V(),siteSpectTesting:[1,8,10].every((function(e){return r.publisherConsents.has(e)}))&&V(),googleAc:[1,2,3,4,7].every((function(e){return r.purposeConsents.has(e)}))&&r.specialFeatureOptins.has(2)&&r.publisherCustomConsents.has(1),amazonAds:[1,2,3,4,7].every((function(e){return r.purposeConsents.has(e)}))&&[1,2,3,4,7].every((function(e){return r.publisherConsents.has(e)}))&&r.specialFeatureOptins.has(2)&&r.vendorConsents.has(C),googleAdsConversionTracking:[1,3,7,8].every((function(e){return r.publisherConsents.has(e)}))&&r.specialFeatureOptins.has(2)&&r.vendorConsents.has(m),searchBasicAds:[1,2,7].every((function(e){return r.publisherConsents.has(e)}))&&r.vendorConsents.has(C),searchPersonalisedAds:[1,2,3,4,7].every((function(e){return r.publisherConsents.has(e)}))&&r.vendorConsents.has(C),sentryErrorTracing:r.publisherCustomConsents.has(1),internalRetargeting:[1,2,3,4,7,10].every((function(e){return r.publisherConsents.has(e)}))&&V(),googleBasicAds:r.publisherConsents.has(1)&&r.purposeConsents.has(1)&&r.vendorConsents.has(m),googleAdRemarketing:[1,2,3,4,5,7].every((function(e){return r.publisherConsents.has(e)}))&&r.publisherCustomConsents.has(1)&&r.vendorConsents.has(m),searchBasicAdsAmazon:[1,2,7,9,10].every((function(e){return r.purposeConsents.has(e)}))&&r.vendorConsents.has(C),searchBasicAdsVerizon:[1,2,7,9,10].every((function(e){return r.purposeConsents.has(e)}))&&r.vendorConsents.has(25),searchBasicAdsGoogle:[1,2,7,9,10].every((function(e){return r.purposeConsents.has(e)}))&&r.vendorConsents.has(m),searchYahooImage:r.purposeConsents.has(1)&&r.vendorConsents.has(25),firstPartyCookie:r.publisherConsents.has(1),thirdPartyCookie:r.publisherConsents.has(1)&&r.purposeConsents.has(1),vgwortTracking:[1,7,8,9,10].every((function(e){return r.purposeConsents.has(e)}))&&r.vendorConsents.has(345)&&r.publisherCustomConsents.has(4)&&V(),civey:[1].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(4),tradedoublerTracking:[1,2,3,4,5,6,7,9,10].every((function(e){return r.purposeConsents.has(e)}))&&r.vendorConsents.has(24)&&r.vendorConsents.has(213)&&r.publisherCustomConsents.has(4)&&V(),shoppingCartSmartPanel:[1,2,3,4,7].every((function(e){return r.publisherConsents.has(e)}))&&[1,2,3,4,7].every((function(e){return r.purposeConsents.has(e)}))&&r.vendorConsents.has(34),heimspiel:[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.publisherConsents.has(e)}))&&[1,2,7,8].every((function(e){return r.purposeConsents.has(e)}))&&r.vendorConsents.has(865),hotjar:[1,8,9,10].every((function(e){return r.purposeConsents.has(e)}))&&r.specialFeatureOptins.has(2)&&r.publisherCustomConsents.has(4),contentGarden:[1,2,7,8].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(4),theGlobalGroup:r.publisherCustomConsents.has(4),shopping24:[7].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(4),metaDach:[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.publisherConsents.has(e)}))&&[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(4),tcfMicrosoftDach:[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.publisherConsents.has(e)}))&&[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(4),tcfGoogleDach:[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.publisherConsents.has(e)}))&&[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(4),tcfTiktokDach:[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.publisherConsents.has(e)}))&&[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(4),tcfTrackFree:r.publisherCustomConsents.has(9),tgp:[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.publisherConsents.has(e)}))&&V(),editorialPersonalization:[1,5,6,8,10,11].every((function(e){return r.publisherConsents.has(e)}))&&r.vendorConsents.has(C)&&V(),specialGeoFeature:[5,6,11].every((function(e){return r.publisherConsents.has(e)})),opinary:[1,2,5,6,7,8,9,10,11].every((function(e){return r.purposeConsents.has(e)}))&&r.specialFeatureOptins.has(2)&&r.vendorConsents.has(488),facebookInternational:[1,2,3,4,5,6,7,8,11].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(5),linkedinInternational:[1,2,3,4,5,6,7,8,11].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(6),instagramInternational:[1,2,3,4,5,6,7,8,11].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(7),microsoftInternational:[1,2,3,4,5,7,11].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(8),sovendus:[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.purposeConsents.has(e)}))&&r.publisherCustomConsents.has(4),fullConsent:[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.publisherConsents.has(e)}))&&[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return r.purposeConsents.has(e)}))&&r.specialFeatureOptins.has(2)&&r.publisherCustomConsents.has(1)&&_().every((function(e){return r.vendorConsents.has(e)})),publisher:!!(s&&s.length>0)&&s.every((function(e){return r.publisherConsents.has(e)})),publisherCustomPurpose:!!(s&&s.length>0)&&s.every((function(e){return r.publisherCustomConsents.has(e)})),purpose:!!(s&&s.length>0)&&s.every((function(e){return r.purposeConsents.has(e)})),vendor:!!(s&&s.length>0)&&s.every((function(e){return r.vendorConsents.has(e)})),special:!!(s&&s.length>0)&&s.every((function(e){return r.specialFeatureOptins.has(e)}))}[t]):(ve(Me,"getPermissionFeature: No valid tcModel, applying privacy by default!"),!1)}function Be(t){return void 0===t&&(t=L()),Object.keys(e.PermissionFeatures).filter((function(e){return isNaN(parseInt(e))})).map((function(e){var s,n=null;return"publisher"===e||"purpose"===e?n=[1,2,3,4,5,6,7,8,9,10,11]:"special"===e?n=[2]:"publisherCustomPurpose"===e&&(n=[1,2]),(s={})[e]=Ge(e,n,t),s}))}function He(e){var t;void 0===e&&(e=L());try{t=k()?T()===f&&function(e){var t;if(Ee.tcModel&&Ee.tcString===e)t=Ee.tcModel.publisherCountryCode;else try{t=ce.decode(e).publisherCountryCode}catch(e){t=void 0}return"US"===t}(e)?JSON.parse("[43,46,55,61,70,83,89,93,108,117,122,124,135,143,144,147,149,159,192,196,211,228,230,239,259,266,286,291,311,318,320,322,323,327,367,371,385,394,407,415,424,430,436,445,486,491,494,495,522,523,540,550,559,560,568,574,576,584,587,591,737,802,803,820,821,839,864,899,904,922,931,938,959,979,981,985,1003,1027,1031,1040,1046,1051,1053,1067,1092,1095,1097,1099,1107,1135,1143,1149,1152,1162,1166,1186,1188,1205,1215,1226,1227,1230,1252,1268,1270,1276,1284,1290,1301,1307,1312,1345,1356,1375,1403,1415,1416,1421,1423,1440,1449,1455,1495,1512,1516,1525,1540,1548,1555,1558,1570,1577,1579,1583,1584,1603,1616,1638,1651,1653,1659,1667,1677,1678,1682,1697,1699,1703,1712,1716,1721,1725,1732,1745,1750,1765,1782,1786,1800,1810,1825,1827,1832,1838,1840,1842,1843,1845,1859,1866,1870,1878,1880,1889,1899,1917,1929,1942,1944,1962,1963,1964,1967,1968,1969,1978,1985,1987,2003,2008,2027,2035,2039,2047,2052,2056,2064,2068,2072,2074,2088,2090,2103,2107,2109,2115,2124,2130,2133,2135,2137,2140,2147,2156,2166,2177,2186,2205,2213,2216,2219,2220,2222,2225,2234,2253,2279,2282,2292,2309,2312,2316,2322,2325,2328,2331,2335,2336,2343,2354,2358,2359,2370,2376,2377,2387,2400,2403,2405,2407,2411,2414,2416,2418,2425,2440,2447,2461,2465,2468,2472,2477,2481,2484,2486,2488,2493,2498,2501,2510,2517,2526,2527,2532,2535,2542,2552,2563,2564,2567,2568,2569,2571,2572,2575,2577,2583,2584,2596,2604,2605,2608,2609,2610,2612,2614,2621,2628,2629,2633,2636,2642,2643,2645,2646,2650,2651,2652,2656,2657,2658,2660,2661,2669,2670,2677,2681,2684,2687,2690,2695,2698,2713,2714,2729,2739,2767,2768,2770,2772,2784,2787,2791,2792,2798,2801,2805,2812,2813,2816,2817,2821,2822,2827,2830,2831,2834,2838,2839,2844,2846,2849,2850,2852,2854,2860,2862,2863,2865,2867,2869,2873,2874,2875,2876,2878,2880,2881,2882,2883,2884,2886,2887,2888,2889,2891,2893,2894,2895,2897,2898,2900,2901,2908,2909,2916,2917,2918,2919,2920,2922,2923,2927,2929,2930,2931,2940,2941,2947,2949,2950,2956,2958,2961,2963,2964,2965,2966,2968,2973,2975,2979,2980,2981,2983,2985,2986,2987,2994,2995,2997,2999,3000,3002,3003,3005,3008,3009,3010,3012,3016,3017,3018,3019,3028,3034,3038,3043,3052,3053,3055,3058,3059,3063,3066,3068,3070,3073,3074,3075,3076,3077,3089,3090,3093,3094,3095,3097,3099,3100,3106,3109,3112,3117,3119,3126,3127,3128,3130,3135,3136,3145,3150,3151,3154,3155,3163,3167,3172,3173,3182,3183,3184,3185,3187,3188,3189,3190,3194,3196,3209,3210,3211,3214,3215,3217,3219,3222,3223,3225,3226,3227,3228,3230,3231,3234,3235,3236,3237,3238,3240,3244,3245,3250,3251,3253,3257,3260,3270,3272,3281,3288,3290,3292,3293,3296,3299,3300,3306,3307,3309,3314,3315,3316,3318,3324,3328,3330,3331,3531,3731,3831,4131,4531,4631,4731,4831,5231,6931,7235,7831,7931,8931,9731,10231,10631,10831,11031,11531,12831,13632,13731,14034,14133,14237,14332,15731,16831,16931,21233,23031,25131,25731,25931,26031,26831,27731,27831,28031,28731,28831,29631,32531,33631,34231,34631,36831,39131,39531,40632,41531,43631,43731,43831]"):JSON.parse("[70,89,143,196,311,1097,1188,1579,2072,2343,2567,2605,2869,3119]"):JSON.parse("[70,89,143,196,981,1097,1579,2072,2343,2376,2567]")}catch(e){t=[],console.error(e)}var s=t.join(".");return Ge("googleAc",null,e)?"".concat(2,"~").concat(s):"".concat(2,"~dv.").concat(s)}function je(){return{TCString:ce,GVL:oe}}function ze(e){var t;void 0===e&&(e=L()),e||Ce("[IAB Utils]->","getTCLastUpdated: No tcString available or not typeof string!");try{t=ce.decode(e)}catch(e){ve("[IAB Utils]->","getPermissionFeature: Could not decode tcString into tcModel, tcString is invalid!")}return e&&t&&t.lastUpdated?t.lastUpdated:void 0}var Qe="__tcfapiLocator";function $e(e){var t="string"==typeof e.data,s={};try{s=t?JSON.parse(e.data):e.data}catch(e){}var n=s.__tcfapiCall;n&&window.__tcfapi(n.command,n.version,(function(s,r){var i={__tcfapiReturn:{returnValue:s,success:r,callId:n.callId,command:n.command}};e&&e.source&&e.source.postMessage&&e.source.postMessage(t?JSON.stringify(i):i,"*")}),n.parameter)}function We(e){var t=e.document;if(!!!e.frames[Qe])if("interactive"===document.readyState||"complete"===document.readyState){var s=t.createElement("iframe");s.style.display="none",s.name=Qe,s.setAttribute("aria-hidden","true"),t.body.appendChild(s)}else document.addEventListener("readystatechange",(function(){We(e)}))}function Je(){for(var e,t=window;t;){try{if(t.frames[Qe]){e=t;break}}catch(e){}if(t===window.top)break;t=t.parent}e||(We(window),window.addEventListener("message",$e,!1))}var Ye="no-consent";var qe=-1!==["gmx.net","gmx.at","gmx.ch"].indexOf(T()),Ke={},Ze=function(e,n,r,i){return new Promise((function(o,a){return t(void 0,void 0,void 0,(function(){var t,c,u;return s(this,(function(s){switch(s.label){case 0:return t=function(e){try{e.remove()}catch(t){e.parentNode&&e.parentNode.removeChild(e)}},c="".concat(e,"?cName=").concat(n,"&cValue=").concat(r,"&cExpire=").concat(i),(u=document.createElement("iframe")).src=c,u.width="0",u.height="0",u.style.display="none",u.style.visibility="hidden",u.onload=function(){t(u),o()},u.onerror=function(){t(u),a("Failed to transfer cookie: Error loading the iframe.")},window.setTimeout((function(){a("Failed to transfer cookie: Iframe load timed out.")}),3e3),"loading"!==document.readyState?[3,2]:[4,new Promise((function(e){window.addEventListener("DOMContentLoaded",(function(){e()}))}))];case 1:s.sent(),s.label=2;case 2:return document.body.appendChild(u),[2]}}))}))}))},Xe=function(){return t(void 0,void 0,void 0,(function(){var e;return s(this,(function(n){switch(n.label){case 0:if("string"!=typeof A.get(a)&&"string"!=typeof A.get(c))return Ce("setUiConsentCookie: No ".concat(a," or\n      ").concat(c," cookies, cannot determine consent!")),[2];try{e=JSON.stringify((l={},Be().forEach((function(e){var t=Object.keys(e)[0],s=e[t];["publisher","purpose","special","publisherCustomPurpose","vendor"].includes(t)||s&&(Object.prototype.hasOwnProperty.call(l,"permissionFeature")||(l.permissionFeature=[]),l.permissionFeature.push(t))})),l.permissionFeature&&-1!==l.permissionFeature.indexOf("fullConsent")?(l.permissionFeature=["fullConsent"],l):([1,2,3,4,5,6,7,8,9,10,11].forEach((function(e){Ge("publisher",e)&&(Object.prototype.hasOwnProperty.call(l,"publisher")||(l.publisher=[]),l.publisher.push(e))})),[1,2,3,4,5,6,7,8,9,10,11].forEach((function(e){Ge("purpose",e)&&(Object.prototype.hasOwnProperty.call(l,"purpose")||(l.purpose=[]),l.purpose.push(e))})),[1,2,3,4,5,6,7,8,9].forEach((function(e){Ge("publisherCustomPurpose",e)&&(Object.prototype.hasOwnProperty.call(l,"publisherCustomPurpose")||(l.publisherCustomPurpose=[]),l.publisherCustomPurpose.push(e))})),[1,2].forEach((function(e){Ge("special",e)&&(Object.prototype.hasOwnProperty.call(l,"specialFeature")||(l.specialFeature=[]),l.specialFeature.push(e))})),l)))}catch(t){e="{}",ve("setUiConsentCookie","Could not parse 'uiConsentCookieData', applying privacy by default.",t)}if(e===A.get(u))return me("setUiConsentCookie: Data did not change"),[2];A.set(u,e,{domain:T(),expires:390,secure:"https:"===window.location.protocol,sameSite:"https:"===window.location.protocol?"none":void 0}),r=u,o=390,(i=e)&&"string"==typeof i&&!isNaN(o)&&qe&&(Ke[r]={cookieValue:i,cookieExpire:o},me("CookieDataTransferService: Pushed new entry into cookie data transfer queue:",Ke)),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,t(void 0,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return Ke&&0!==Object.keys(Ke).length?(me("CookieDataTransferService: Starting process of cookie data transfer queue..."),[4,Promise.all(Object.keys(Ke).map((function(e){return t(void 0,void 0,void 0,(function(){return s(this,(function(n){switch(n.label){case 0:return[4,(r=e,i=Ke[e].cookieValue,o=Ke[e].cookieExpire,t(void 0,void 0,void 0,(function(){var e;return s(this,(function(t){switch(t.label){case 0:return"gmx.net"!==(e=T())?[3,2]:[4,Promise.all([Ze(d,r,i,o),Ze(h,r,i,o)]).catch((function(e){return Promise.reject(e)}))];case 1:return t.sent(),[3,4];case 2:return"gmx.at"!==e&&"gmx.ch"!==e?[3,4]:[4,Ze(p,r,i,o).catch((function(e){return Promise.reject(e)}))];case 3:t.sent(),t.label=4;case 4:return[2]}}))})))];case 1:return n.sent(),me("CookieDataTransferService: Transferring cookie data for cookie '".concat(e,"': "),Ke[e]),[2]}var r,i,o}))}))}))).catch((function(e){ve("CookieDataTransferService failed",e)}))]):[2,Promise.resolve()];case 1:return e.sent(),me("CookieDataTransferService: Successfully finished processing of cookie data transfer queue."),[2]}}))}))];case 2:return n.sent(),[3,4];case 3:return ve("setUiConsentCookie","Cookie transfer failed",n.sent()),[3,4];case 4:return[2]}var r,i,o,l}))}))},et=function(){function e(e){void 0===e&&(e=!0),this.moduleName="[API]->",e&&this.init().catch((function(e){ve("Failed to initialize TCF API",e)}))}return e.prototype.init=function(){return t(this,void 0,void 0,(function(){var e,t=this;return s(this,(function(s){switch(s.label){case 0:this.cmpApi||(this.cmpApi=new xe(R(),2,g,{getTCData:function(e,s,n){try{s&&"object"==typeof s&&(s.addtlConsent=He(),Ee.tcModel&&Ee.tcModel.vendorListVersion&&(s.vendorListVersion=Ee.tcModel.vendorListVersion)),e(s,n)}catch(e){ve(t.moduleName,"getTCData: Failed to append Google ACString to TCModel.")}},updateTCString:function(e){t.updateTCString(),e(Ee.tcModel)},getTCString:function(e){e(L())},getACString:function(e,t){e(He(t=t||void 0))},getPermission:function(e,t,s,n){e(Ge(t,s,n))},getTCFVersion:function(e){e("2.0")},getTCLastUpdated:function(e,t){e(ze(t=t||void 0))},getTCStringUtil:function(e){e(je())},getAppInfo:function(e){e(O())},getPermissionFeatures:function(e,t){e(Be(t))},getConsentState:function(e,t){var s,n;Ge("fullConsent",void 0,t)?e("fullConsent"):(void 0===(s=t)&&(s=L()),Ee.tcModel&&Ee.tcString===s?(me(Ye,"hasNoConsent: Using existing tcModel"),n=Ee.tcModel):"string"==typeof s&&s.length>0&&(n=ye(s)),(n?[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return!n.publisherConsents.has(e)}))&&[1,2,3,4,5,6,7,8,9,10,11].every((function(e){return!n.purposeConsents.has(e)}))&&[1,2].every((function(e){return!n.specialFeatureOptins.has(e)}))&&[1,2].every((function(e){return!n.publisherCustomConsents.has(e)}))&&_().every((function(e){return!n.vendorConsents.has(e)})):(ve(Ye,"hasNoConsent: No valid tcModel, applying privacy by default!"),1))?e("noConsent"):e("partialConsent"))}})),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,this.updateTCString()];case 2:return s.sent(),me(this.moduleName,"init: Successfully initialized"),[3,4];case 3:throw ve("Failed to initialize TCF API",e=s.sent()),e;case 4:return[2]}}))}))},e.prototype.updateTCString=function(){return t(this,void 0,void 0,(function(){var e,t,n;return s(this,(function(s){switch(s.label){case 0:if(e=Ee.tcString,(t=L())===e)return[3,5];this.cmpApi.update(t),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,Xe()];case 2:return s.sent(),[3,4];case 3:return n=s.sent(),Ce(this.moduleName,"init: Failed to set uiconsent cookie",n),[3,4];case 4:return me(this.moduleName,"updateTCString:",["Previous tcString:",e],["Got tcString from 'euconsent-v2' cookie here:",t],["Got tcModel from decoded tcString here:",Ee.tcModel],"Successfully updated TCF API state, commands will respond with latest TCString / TCModel data from now"),[3,6];case 5:me(this.moduleName,"updateTCString:",["Previous tcString:",e],["Got tcString from 'euconsent-v2' cookie here:",t],"TCString did not change, no update required"),s.label=6;case 6:return[2]}}))}))},e}(),tt=new et(!1);return function(){t(this,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,tt.init()];case 1:return e.sent(),Je(),[3,3];case 2:return ve("Failed to initialize TCF API",e.sent()),[3,3];case 3:return[2]}}))}))}(),e.TcfApi=et,e.getACString=He,e.getAppInfo=O,e.getIdcc=V,e.getPermissionFeature=Ge,e.getPermissionFeatures=Be,e.getTCFVersion=P,e.getTCLastUpdated=ze,e.getTCString=L,e.getTCStringUtil=je,e.getVendorArray=_,e.tcfApi=tt,e}({});