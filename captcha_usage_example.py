"""
Example usage of the updated CaptchaSolver with Selenium WebDriver.
This shows how to integrate the captcha solver with your GMX creator.
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from Captcha_files.captcha_solver import CaptchaSolver
import config

def example_usage():
    """Example of how to use the updated CaptchaSolver with Selenium."""
    
    # Setup Chrome driver (similar to your gmx.py setup)
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    # Initialize driver
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Initialize captcha solver
        captcha_solver = CaptchaSolver()
        
        # Navigate to a page with captcha
        driver.get("https://example.com/page-with-captcha")
        
        # Method 1: Automatic detection and solving
        print("Method 1: Automatic captcha detection and solving")
        solution = captcha_solver.solve_page_captcha(
            driver=driver,
            user_agent=driver.execute_script("return navigator.userAgent;")
        )
        
        if solution:
            print(f"Captcha solved automatically! Token: {solution[:20]}...")
        else:
            print("No captcha found or failed to solve")
        
        # Method 2: Manual captcha solving (if you know the details)
        print("\nMethod 2: Manual captcha solving")
        website_url = driver.current_url
        website_key = "your-site-key-here"  # Extract this from the page
        
        solution = captcha_solver.solve_captcha_fox(
            website_url=website_url,
            website_key=website_key,
            user_agent=driver.execute_script("return navigator.userAgent;")
        )
        
        if solution:
            print(f"Captcha solved manually! Token: {solution[:20]}...")
            # Apply the solution manually if needed
            success = captcha_solver._apply_captcha_solution(driver, solution)
            print(f"Solution applied: {success}")
        else:
            print("Failed to solve captcha manually")
        
        # Method 3: Just detect captcha without solving
        print("\nMethod 3: Captcha detection only")
        captcha_info = captcha_solver.detect_captcha(driver)
        
        if captcha_info:
            print(f"Captcha detected:")
            print(f"  Type: {captcha_info['type']}")
            print(f"  Website Key: {captcha_info.get('website_key', 'Not found')}")
            print(f"  Website URL: {captcha_info['website_url']}")
        else:
            print("No captcha detected")
            
    finally:
        driver.quit()

def integration_with_gmx():
    """Example of how to integrate with your existing GMX creator code."""

    # This is how you would use it in your Worker class methods
    def solve_gmx_captcha(self):
        """Method to add to your Worker class for captcha solving."""

        # Initialize captcha solver
        captcha_solver = CaptchaSolver()

        # Debug: Log all captcha elements found
        captcha_solver.debug_captcha_elements(self.browser)

        # Get user agent from browser
        user_agent = None
        try:
            user_agent = self.browser.execute_script("return navigator.userAgent;")
        except:
            pass

        # Solve any captcha on the current page
        solution = captcha_solver.solve_page_captcha(
            driver=self.browser,
            user_agent=user_agent
        )

        if solution:
            self.logger.info(f"Captcha solved successfully: {solution[:20]}...")
            return True
        else:
            self.logger.warning("No captcha found or failed to solve")
            return False

    def handle_gmx_registration_captcha(self):
        """Specific method for GMX registration captcha handling."""

        captcha_solver = CaptchaSolver()

        # Check if we're on a GMX registration page with captcha
        try:
            # Look for the specific GMX captcha elements
            captcha_elements = self.browser.find_elements("css selector", "onereg-captcha")
            if not captcha_elements:
                self.logger.info("No GMX captcha found on page")
                return True

            self.logger.info("GMX CaptchaFox detected, attempting to solve...")

            # Use the debug method to see what we're working with
            captcha_solver.debug_captcha_elements(self.browser)

            # Solve the captcha
            solution = captcha_solver.solve_page_captcha(
                driver=self.browser,
                user_agent=self.browser.execute_script("return navigator.userAgent;")
            )

            if solution:
                self.logger.info("GMX captcha solved successfully!")
                # Wait a moment for the solution to be processed
                time.sleep(2)
                return True
            else:
                self.logger.error("Failed to solve GMX captcha")
                return False

        except Exception as e:
            self.logger.error(f"Error handling GMX captcha: {e}")
            return False

    print("Integration examples created:")
    print("1. solve_gmx_captcha() - General captcha solving")
    print("2. handle_gmx_registration_captcha() - GMX-specific handling")

if __name__ == "__main__":
    print("CaptchaSolver Usage Examples")
    print("=" * 40)
    
    # Run the example (commented out to avoid actually opening browser)
    # example_usage()
    
    # Show integration example
    integration_with_gmx()
    
    print("\nKey features of the updated CaptchaSolver:")
    print("✓ Uses requests instead of playwright")
    print("✓ Compatible with Selenium WebDriver")
    print("✓ Automatic GMX CaptchaFox detection")
    print("✓ No iframe handling needed (direct page integration)")
    print("✓ Supports GMX-specific selectors:")
    print("  - onereg-captcha, onereg-captcha-fox")
    print("  - div[id^='cf-widget-'], .captchafox")
    print("  - textarea[name='cf-captcha-response']")
    print("✓ Debug method for troubleshooting")
    print("✓ Simple, clear, and short code")
    print("✓ Proper error handling and logging")
