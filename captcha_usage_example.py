"""
Example usage of the updated CaptchaSolver with Selenium WebDriver.
This shows how to integrate the captcha solver with your GMX creator.
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from Captcha_files.captcha_solver import CaptchaSolver
import config

def example_usage():
    """Example of how to use the updated CaptchaSolver with Selenium."""
    
    # Setup Chrome driver (similar to your gmx.py setup)
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    # Initialize driver
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Initialize captcha solver
        captcha_solver = CaptchaSolver()
        
        # Navigate to a page with captcha
        driver.get("https://example.com/page-with-captcha")
        
        # Method 1: Automatic detection and solving
        print("Method 1: Automatic captcha detection and solving")
        solution = captcha_solver.solve_page_captcha(
            driver=driver,
            user_agent=driver.execute_script("return navigator.userAgent;")
        )
        
        if solution:
            print(f"Captcha solved automatically! Token: {solution[:20]}...")
        else:
            print("No captcha found or failed to solve")
        
        # Method 2: Manual captcha solving (if you know the details)
        print("\nMethod 2: Manual captcha solving")
        website_url = driver.current_url
        website_key = "your-site-key-here"  # Extract this from the page
        
        solution = captcha_solver.solve_captcha_fox(
            website_url=website_url,
            website_key=website_key,
            user_agent=driver.execute_script("return navigator.userAgent;")
        )
        
        if solution:
            print(f"Captcha solved manually! Token: {solution[:20]}...")
            # Apply the solution manually if needed
            success = captcha_solver._apply_captcha_solution(driver, solution)
            print(f"Solution applied: {success}")
        else:
            print("Failed to solve captcha manually")
        
        # Method 3: Just detect captcha without solving
        print("\nMethod 3: Captcha detection only")
        captcha_info = captcha_solver.detect_captcha(driver)
        
        if captcha_info:
            print(f"Captcha detected:")
            print(f"  Type: {captcha_info['type']}")
            print(f"  Website Key: {captcha_info.get('website_key', 'Not found')}")
            print(f"  Website URL: {captcha_info['website_url']}")
        else:
            print("No captcha detected")
            
    finally:
        driver.quit()

def integration_with_gmx():
    """Example of how to integrate with your existing GMX creator code."""
    
    # This is how you would use it in your Worker class methods
    def solve_gmx_captcha(self):
        """Method to add to your Worker class for captcha solving."""
        
        # Initialize captcha solver
        captcha_solver = CaptchaSolver()
        
        # Get user agent from browser
        user_agent = None
        try:
            user_agent = self.browser.execute_script("return navigator.userAgent;")
        except:
            pass
        
        # Solve any captcha on the current page
        solution = captcha_solver.solve_page_captcha(
            driver=self.browser,
            user_agent=user_agent
        )
        
        if solution:
            self.logger.info(f"Captcha solved successfully: {solution[:20]}...")
            return True
        else:
            self.logger.warning("No captcha found or failed to solve")
            return False
    
    print("Integration example created - add solve_gmx_captcha method to your Worker class")

if __name__ == "__main__":
    print("CaptchaSolver Usage Examples")
    print("=" * 40)
    
    # Run the example (commented out to avoid actually opening browser)
    # example_usage()
    
    # Show integration example
    integration_with_gmx()
    
    print("\nKey features of the updated CaptchaSolver:")
    print("✓ Uses requests instead of playwright")
    print("✓ Compatible with Selenium WebDriver")
    print("✓ Automatic captcha detection")
    print("✓ Supports CaptchaFox captchas")
    print("✓ Simple, clear, and short code")
    print("✓ Proper error handling and logging")
