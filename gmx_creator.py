"""
GMX Email Creator - Main application module.

This script automates the creation of GMX.de email accounts.
"""
import asyncio
import argparse
import os
import time
import random
# import subprocess  # Uncomment if needed
from loguru import logger

import config
from browser_manager import BrowserManager
from Captcha_files.captcha_solver import <PERSON><PERSON><PERSON><PERSON><PERSON>
from data_generator import DataGenerator
from sms_manager import SmsActivateAPI
import utils

# Import HMA VPN modules
try:
    from hma.turn_on import turn_on_vpn
    from hma.turn_off import turn_off_vpn
    from hma.change_ip import change_ip
    HMA_AVAILABLE = True
except ImportError:
    logger.warning("HMA VPN modules not available. VPN features will be disabled.")
    HMA_AVAILABLE = False

class GmxCreator:
    """Main class for creating GMX email accounts."""

    def __init__(self, headless=None, browser_type=None, use_vpn=True, use_temp_profile=None, use_installed_browser=None):
        """Initialize the GMX Creator.

        Args:
            headless: Whether to run in headless mode (default from config)
            browser_type: Type of browser to use (default from config)
            use_vpn: Whether to use HMA VPN (default: True)
            use_temp_profile: Whether to use temporary browser profile (default from config)
            use_installed_browser: Whether to use installed browser (default from config)
        """
        # Set up logger
        utils.setup_logger()
        logger.info("Initializing GMX Creator")

        # Create .env template if it doesn't exist
        utils.create_dot_env_template()

        # Use config values if not specified
        if use_temp_profile is None:
            use_temp_profile = config.USE_TEMP_PROFILE

        if use_installed_browser is None:
            use_installed_browser = config.USE_INSTALLED_BROWSER

        # If using installed browser, disable temp profile
        if use_installed_browser:
            use_temp_profile = False

        # Initialize components
        self.browser_manager = BrowserManager(
            browser_type=browser_type,
            headless=headless,
            use_temp_profile=use_temp_profile,
            use_installed_browser=use_installed_browser
        )
        self.captcha_solver = CaptchaSolver()
        self.data_generator = DataGenerator()
        self.sms_manager = SmsActivateAPI()

        # VPN settings
        self.use_vpn = use_vpn and HMA_AVAILABLE
        self.vpn_connected = False
        self.ip_changes = 0

        # Statistics
        self.accounts_created = 0
        self.accounts_failed = 0
        self.start_time = None
        self.phone_numbers_used = 0

        if self.use_vpn:
            logger.info("HMA VPN integration enabled")
        else:
            if use_vpn and not HMA_AVAILABLE:
                logger.warning("HMA VPN requested but modules not available")
            logger.info("HMA VPN integration disabled")

        if use_temp_profile:
            logger.info("Using temporary browser profile")
        else:
            logger.info("Using persistent browser profile")

    async def create_account(self):
        """Create a single GMX account.

        Returns:
            tuple: (success, email, password) or (False, None, None) on failure
        """
        try:
            # Generate user data
            user_data = self.data_generator.generate_user_data()
            logger.info(f"Generated user data for: {user_data['username']}")

            # Start browser if not already started
            if not self.browser_manager.page:
                await self.browser_manager.start()

            # Navigate to GMX tariff page and select FreeMail
            success = await self._navigate_to_registration()
            if not success:
                logger.error("Failed to navigate to GMX registration page")
                return False, None, None

            # Accept cookies if present
            await self._handle_cookie_consent()

            # Handle tracking consent if present
            await self._handle_tracking_consent()

            # Complete the multi-step registration process
            success = await self._complete_registration_process(user_data)
            if not success:
                logger.error("Failed to complete registration")
                return False, None, None

            # Save account
            utils.save_account(user_data['email'], user_data['password'])

            logger.success(f"Successfully created account: {user_data['email']}")
            return True, user_data['email'], user_data['password']

        except Exception as e:
            logger.error(f"Error creating account: {e}")
            return False, None, None

    def _connect_vpn(self):
        """Connect to HMA VPN.

        Returns:
            bool: True if connected successfully, False otherwise
        """
        if not self.use_vpn:
            logger.info("VPN integration disabled, skipping connection")
            return True

        try:
            logger.info("Connecting to HMA VPN (Germany)...")
            result = turn_on_vpn()

            if result:
                logger.info("Successfully connected to HMA VPN")
                self.vpn_connected = True
                return True
            else:
                logger.error("Failed to connect to HMA VPN")
                return False

        except Exception as e:
            logger.error(f"Error connecting to VPN: {e}")
            return False

    def _disconnect_vpn(self):
        """Disconnect from HMA VPN.

        Returns:
            bool: True if disconnected successfully, False otherwise
        """
        if not self.use_vpn or not self.vpn_connected:
            return True

        try:
            logger.info("Disconnecting from HMA VPN...")
            result = turn_off_vpn()

            if result:
                logger.info("Successfully disconnected from HMA VPN")
                self.vpn_connected = False
                return True
            else:
                logger.error("Failed to disconnect from HMA VPN")
                return False

        except Exception as e:
            logger.error(f"Error disconnecting from VPN: {e}")
            return False

    def _change_ip(self):
        """Change IP address using HMA VPN.

        Returns:
            bool: True if IP changed successfully, False otherwise
        """
        if not self.use_vpn or not self.vpn_connected:
            return True

        try:
            logger.info("Changing IP address...")
            result = change_ip()

            if result:
                self.ip_changes += 1
                logger.info(f"Successfully changed IP address (Change #{self.ip_changes})")
                return True
            else:
                logger.error("Failed to change IP address")
                return False

        except Exception as e:
            logger.error(f"Error changing IP address: {e}")
            return False

    async def _navigate_to_registration(self):
        """Navigate to the GMX registration page and select FreeMail plan.

        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            # Connect to VPN if not already connected
            if self.use_vpn and not self.vpn_connected:
                if not self._connect_vpn():
                    logger.error("Failed to connect to VPN, continuing without VPN")

            # Try direct navigation to the registration page first
            logger.info("Trying direct navigation to registration page")
            if await self.browser_manager.navigate("https://registrierung.gmx.net/"):
                # Handle cookie consent dialog if present
                await self._handle_cookie_consent()

                # Handle tracking consent dialog if present
                await self._handle_tracking_consent()

                # Check if we're on the registration page
                if await self._is_on_registration_page():
                    logger.info("Successfully navigated directly to registration page")
                    return True
                else:
                    logger.warning("Direct navigation didn't reach registration page, trying via tariff page")
            else:
                logger.warning("Failed to load registration page directly, trying via tariff page")

            # First navigate to the tariff comparison page
            success = await self.browser_manager.navigate(config.GMX_TARIFF_URL)
            if not success:
                logger.error("Failed to navigate to GMX tariff page")
                return False

            # Accept cookies if present
            await self._handle_cookie_consent()

            # Handle tracking consent if present
            await self._handle_tracking_consent()

            # Add a delay to ensure the page is fully loaded
            await asyncio.sleep(3)

            # Take a screenshot for debugging
            screenshot_path = "debug_tariff_page.png"
            await self.browser_manager.page.screenshot(path=screenshot_path)
            logger.info(f"Saved screenshot to {screenshot_path}")

            # Get the page HTML for debugging
            page_content = await self.browser_manager.page.content()
            with open("debug_page_content.html", "w", encoding="utf-8") as f:
                f.write(page_content)
            logger.info("Saved page content to debug_page_content.html")

            # Check for iframes that might contain the registration link
            iframe_count = await self.browser_manager.page.locator('iframe').count()
            logger.info(f"Found {iframe_count} iframes on the page")

            if iframe_count > 0:
                logger.info("Checking iframes for registration links...")
                for i in range(iframe_count):
                    try:
                        iframe = self.browser_manager.page.frame_locator(f'iframe:nth-child({i+1})')

                        # Check if this iframe contains registration links
                        reg_links = await iframe.locator('a[href*="registrierung.gmx.net"]').count()
                        if reg_links > 0:
                            logger.info(f"Found registration links in iframe {i+1}")

                            # Try to get the iframe content
                            try:
                                iframe_content = await self.browser_manager.page.evaluate(f"""
                                    () => {{
                                        const iframe = document.querySelectorAll('iframe')[{i}];
                                        return iframe.contentDocument.documentElement.outerHTML;
                                    }}
                                """)
                                with open(f"debug_iframe_{i+1}_content.html", "w", encoding="utf-8") as f:
                                    f.write(iframe_content)
                                logger.info(f"Saved iframe {i+1} content to debug_iframe_{i+1}_content.html")
                            except Exception as e:
                                logger.warning(f"Could not get iframe {i+1} content: {e}")
                    except Exception as e:
                        logger.warning(f"Error checking iframe {i+1}: {e}")

            # Click on the "Jetzt registrieren" button for FreeMail
            register_button_selectors = [
                'a[href*="registrierung.gmx.net"][role="button"]',
                'a:has-text("Jetzt registrieren")',
                '.button:has-text("Jetzt registrieren")',
                'a.button:has-text("Jetzt registrieren")',
                'a[data-click-tracking="mail-tariff-comparison-freemail-register-now"]',
                'a[data-click-tracking*="freemail-register"]',
                'a.button.button--primary',
                'a.button--primary',
                'a[href*="registrierung.gmx.net"]'
            ]

            # Log all buttons found on the page for debugging
            logger.info("Searching for registration buttons...")
            for selector in register_button_selectors:
                count = await self.browser_manager.page.locator(selector).count()
                logger.info(f"Selector '{selector}': found {count} elements")

                if count > 0:
                    # Log the text content of each element
                    elements = await self.browser_manager.page.locator(selector).all()
                    for i, element in enumerate(elements):
                        try:
                            text = await element.text_content()
                            href = await element.get_attribute("href") or "no href"
                            logger.info(f"Element {i+1}: text='{text}', href='{href}'")
                        except Exception as e:
                            logger.warning(f"Error getting element info: {e}")

            clicked = False
            for selector in register_button_selectors:
                elements = await self.browser_manager.page.locator(selector).all()
                if len(elements) > 0:
                    # Try to click the first element that contains "Jetzt registrieren" text
                    for element in elements:
                        try:
                            text = await element.text_content()
                            if "Jetzt registrieren" in text or "registrieren" in text.lower():
                                logger.info(f"Clicking registration button with text: '{text}'")

                                # Get the href attribute
                                href = await element.get_attribute("href")
                                if href and "registrierung.gmx.net" in href:
                                    # Try navigating directly to the href instead of clicking
                                    logger.info(f"Navigating directly to: {href}")
                                    if await self.browser_manager.navigate(href):
                                        clicked = True
                                        break

                                # If direct navigation didn't work or no href, try clicking
                                if not clicked:
                                    await element.click()
                                    clicked = True
                                    break
                        except Exception as e:
                            logger.warning(f"Error clicking element: {e}")
                            continue

                if clicked:
                    break

            if not clicked:
                # Try a more aggressive approach - look for any button or link with registration-related text
                logger.warning("Could not find registration button with specific selectors, trying generic approach")

                generic_selectors = [
                    'a:has-text("registrieren")',
                    'button:has-text("registrieren")',
                    'a:has-text("Registrieren")',
                    'button:has-text("Registrieren")',
                    'a:has-text("anmelden")',
                    'button:has-text("anmelden")',
                    'a:has-text("Anmelden")',
                    'button:has-text("Anmelden")',
                    'a:has-text("kostenlos")',
                    'a:has-text("Kostenlos")'
                ]

                for selector in generic_selectors:
                    elements = await self.browser_manager.page.locator(selector).all()
                    if len(elements) > 0:
                        for element in elements:
                            try:
                                text = await element.text_content()
                                logger.info(f"Found generic button/link: '{text}'")

                                # Get the href attribute
                                href = await element.get_attribute("href")
                                if href and "registrierung.gmx.net" in href:
                                    # Try navigating directly to the href instead of clicking
                                    logger.info(f"Navigating directly to: {href}")
                                    if await self.browser_manager.navigate(href):
                                        clicked = True
                                        break

                                # If direct navigation didn't work or no href, try clicking
                                if not clicked:
                                    await element.click()
                                    clicked = True
                                    break
                            except Exception as e:
                                logger.warning(f"Error clicking generic element: {e}")
                                continue

                    if clicked:
                        break

            if not clicked:
                # Try direct navigation to the registration page as a last resort
                logger.warning("Could not find any registration button, trying direct navigation again")
                if await self.browser_manager.navigate("https://registrierung.gmx.net/"):
                    clicked = True
                else:
                    logger.error("Could not find any registration button or navigate directly")
                    return False

            # Take another screenshot after clicking
            await asyncio.sleep(3)
            screenshot_path = "debug_after_click.png"
            await self.browser_manager.page.screenshot(path=screenshot_path)
            logger.info(f"Saved post-click screenshot to {screenshot_path}")

            # Check for IP restriction error
            if await self._check_ip_restriction():
                if self.use_vpn:
                    logger.warning("IP is restricted despite using VPN. Trying to change IP...")
                    if self._change_ip():
                        # Try again after IP change
                        logger.info("Retrying navigation after IP change")
                        return await self._navigate_to_registration()
                    else:
                        logger.error("Failed to change IP")
                else:
                    logger.error("IP is restricted. Need a German IP address.")
                return False

            # Check if we're on the registration page (Step 1)
            if await self._is_on_registration_page():
                logger.info("Successfully navigated to registration page")

                # Wait a bit longer for the page to fully load
                await asyncio.sleep(3)

                return True
            else:
                logger.error("Failed to reach registration page")
                return False

        except Exception as e:
            logger.error(f"Error navigating to registration: {e}")
            return False

    async def _check_ip_restriction(self):
        """Check if we're on the IP restriction error page.

        Returns:
            bool: True if IP is restricted, False otherwise
        """
        page = self.browser_manager.page

        # Take a screenshot for debugging
        screenshot_path = "debug_ip_restriction_check.png"
        await page.screenshot(path=screenshot_path)
        logger.info(f"Saved IP restriction check screenshot to {screenshot_path}")

        # Get the current URL
        current_url = page.url
        logger.info(f"Current URL during IP check: {current_url}")

        # Get the page content for more thorough checking
        page_content = await page.content()

        # Check for common IP restriction phrases in the HTML
        ip_restriction_phrases = [
            "Registrierung leider nicht möglich",
            "nicht für den deutschsprachigen Raum zugelassenen IP-Adresse",
            "Sie haben versucht, sich mit einer nicht für den deutschsprachigen Raum zugelassenen IP-Adresse",
            "IP-Adresse aus dem deutschsprachigen Raum",
            "Zugriff verweigert",
            "Access denied",
            "Zugang gesperrt",
            "Bitte versuchen Sie es mit einer IP-Adresse aus Deutschland",
            "nur mit einer deutschen IP-Adresse",
            "nur für Nutzer aus Deutschland"
        ]

        for phrase in ip_restriction_phrases:
            if phrase in page_content:
                logger.warning(f"IP restriction detected: '{phrase}' found in page content")
                return True

        # Check for error page indicators using locators
        error_indicators = [
            'text="Registrierung leider nicht möglich"',
            'text="nicht für den deutschsprachigen Raum zugelassenen IP-Adresse"',
            'text="Sie haben versucht, sich mit einer nicht für den deutschsprachigen Raum zugelassenen IP-Adresse"',
            'text="IP-Adresse aus dem deutschsprachigen Raum"',
            'text="Zugriff verweigert"',
            'text="Access denied"',
            'text="Zugang gesperrt"',
            'text="Bitte versuchen Sie es mit einer IP-Adresse aus Deutschland"',
            'text="nur mit einer deutschen IP-Adresse"',
            'text="nur für Nutzer aus Deutschland"'
        ]

        for indicator in error_indicators:
            count = await page.locator(indicator).count()
            if count > 0:
                logger.warning(f"IP restriction detected: '{indicator}' element found")
                return True

        # Save the page content for debugging if needed
        with open("debug_ip_check_page.html", "w", encoding="utf-8") as f:
            f.write(page_content)
        logger.info("Saved IP check page content to debug_ip_check_page.html")

        logger.info("No IP restriction detected")
        return False

    async def _is_on_registration_page(self):
        """Check if we're on the registration page.

        Returns:
            bool: True if on registration page, False otherwise
        """
        page = self.browser_manager.page

        # Take a screenshot for debugging
        screenshot_path = "debug_registration_check.png"
        await page.screenshot(path=screenshot_path)
        logger.info(f"Saved registration check screenshot to {screenshot_path}")

        # Get the current URL
        current_url = page.url
        logger.info(f"Current URL: {current_url}")

        # Check if URL contains registration domain
        if "registrierung.gmx.net" in current_url:
            logger.info("On registration page based on URL")
            return True

        # Check for registration page indicators
        indicators = [
            'text="Starte jetzt!"',
            'text="Erstelle Deinen Account"',
            '[data-test="progress-meter-item"]',
            '.onereg-progress-meter__item',
            'text="Persönliche Daten"',
            'text="E-Mail-Adresse"',
            'text="Passwort"',
            'text="Anschrift"',
            'text="Handynummer"',
            'text="Bestätigung"',
            'input[name="firstName"]',
            'input[name="lastName"]',
            'input[name="birthDate.day"]',
            'form[action*="registrierung"]'
        ]

        for indicator in indicators:
            count = await page.locator(indicator).count()
            logger.info(f"Registration indicator '{indicator}': found {count} elements")
            if count > 0:
                logger.info(f"On registration page based on indicator: {indicator}")
                return True

        # Get the page HTML for debugging
        page_content = await page.content()
        with open("debug_registration_page.html", "w", encoding="utf-8") as f:
            f.write(page_content)
        logger.info("Saved registration page content to debug_registration_page.html")

        logger.warning("Not on registration page - no indicators found")
        return False

    async def _complete_registration_process(self, user_data):
        """Complete the multi-step GMX registration process.

        Args:
            user_data: User data dictionary

        Returns:
            bool: True if registration completed successfully, False otherwise
        """
        try:
            # Step 1: Personal Information
            success = await self._fill_step1_personal_info(user_data)
            if not success:
                return False

            # Step 2: Email Address Selection
            success, email = await self._fill_step2_email_selection(user_data)
            if not success:
                return False

            # Update email in user_data
            user_data['email'] = email

            # Step 3: Password Creation
            success = await self._fill_step3_password(user_data)
            if not success:
                return False

            # Step 4: Address Information
            success = await self._fill_step4_address(user_data)
            if not success:
                return False

            # Step 5: Phone Verification
            success = await self._fill_step5_phone_verification()
            if not success:
                return False

            # Step 6: Final Confirmation
            success = await self._fill_step6_confirmation()
            if not success:
                return False

            # Check for success indicators
            return await self._check_registration_success()

        except Exception as e:
            logger.error(f"Error completing registration process: {e}")
            return False

    async def _handle_cookie_consent(self):
        """Handle cookie consent dialog if present.

        Returns:
            bool: True if handled successfully or not present, False otherwise
        """
        try:
            # Look for common cookie consent selectors
            cookie_selectors = [
                "button[data-cookie-accept-all]",
                "#onetrust-accept-btn-handler",
                ".cookie-banner__button",
                "button:has-text('Akzeptieren')",
                "button:has-text('Accept all')"
            ]

            for selector in cookie_selectors:
                if await self.browser_manager.page.locator(selector).count() > 0:
                    logger.info(f"Accepting cookies with selector: {selector}")
                    await self.browser_manager.page.click(selector)
                    await self.browser_manager.page.wait_for_timeout(1000)
                    return True

            logger.info("No cookie consent dialog found")
            return True

        except Exception as e:
            logger.warning(f"Error handling cookie consent: {e}")
            return False

    async def _handle_tracking_consent(self):
        """Handle tracking consent dialog if present.

        Returns:
            bool: True if handled successfully or not present, False otherwise
        """
        try:
            # Check if we're on the tracking consent page
            consent_selectors = [
                "#save-all-pur",
                "button:has-text('Akzeptieren und weiter')",
                "button[data-id='save-all-pur']"
            ]

            for selector in consent_selectors:
                if await self.browser_manager.page.locator(selector).count() > 0:
                    logger.info(f"Accepting tracking consent with selector: {selector}")
                    await self.browser_manager.page.click(selector)
                    await self.browser_manager.page.wait_for_timeout(2000)
                    return True

            # Check for "Abfrage nochmals anzeigen" (Show query again) button
            reminder_selectors = [
                "#reminder",
                "button:has-text('Abfrage nochmals anzeigen')",
                "button[data-id='reminder']"
            ]

            for selector in reminder_selectors:
                if await self.browser_manager.page.locator(selector).count() > 0:
                    logger.info(f"Clicking reminder button with selector: {selector}")
                    await self.browser_manager.page.click(selector)
                    await self.browser_manager.page.wait_for_timeout(2000)

                    # Now try to find and click the accept button again
                    for accept_selector in consent_selectors:
                        if await self.browser_manager.page.locator(accept_selector).count() > 0:
                            logger.info(f"Accepting tracking consent with selector: {accept_selector}")
                            await self.browser_manager.page.click(accept_selector)
                            await self.browser_manager.page.wait_for_timeout(2000)
                            return True

            logger.info("No tracking consent dialog found")
            return True

        except Exception as e:
            logger.warning(f"Error handling tracking consent: {e}")
            return False

    async def _fill_step1_personal_info(self, user_data):
        """Fill Step 1: Personal Information.

        Args:
            user_data: User data dictionary

        Returns:
            bool: True if step completed successfully, False otherwise
        """
        try:
            page = self.browser_manager.page
            logger.info("Filling Step 1: Personal Information")

            # Wait longer for the page to fully load
            logger.info("Waiting for page to fully load...")
            await asyncio.sleep(5)

            # Take a screenshot before filling
            screenshot_path = "debug_step1_before.png"
            await page.screenshot(path=screenshot_path)
            logger.info(f"Saved Step 1 screenshot to {screenshot_path}")

            # Get the page HTML for debugging
            page_content = await page.content()
            with open("debug_step1_content.html", "w", encoding="utf-8") as f:
                f.write(page_content)
            logger.info("Saved Step 1 page content to debug_step1_content.html")

            # Check for iframes
            iframe_count = await page.locator('iframe').count()
            logger.info(f"Found {iframe_count} iframes on the page")

            # If there are iframes, try to find the registration form in each one
            if iframe_count > 0:
                logger.info("Checking iframes for registration form...")
                for i in range(iframe_count):
                    iframe = page.frame_locator(f'iframe:nth-child({i+1})')

                    # Check if this iframe contains the registration form
                    form_in_iframe = await iframe.locator('form').count() > 0
                    if form_in_iframe:
                        logger.info(f"Found registration form in iframe {i+1}")

                        # Take a screenshot of the iframe content
                        try:
                            iframe_element = await page.locator(f'iframe:nth-child({i+1})').element_handle()
                            await iframe_element.screenshot(path=f"debug_iframe_{i+1}.png")
                            logger.info(f"Saved iframe {i+1} screenshot to debug_iframe_{i+1}.png")
                        except Exception as e:
                            logger.warning(f"Could not take screenshot of iframe {i+1}: {e}")

                        # Try to get the iframe content
                        try:
                            iframe_content = await page.evaluate(f"""
                                () => {{
                                    const iframe = document.querySelectorAll('iframe')[{i}];
                                    return iframe.contentDocument.documentElement.outerHTML;
                                }}
                            """)
                            with open(f"debug_iframe_{i+1}_content.html", "w", encoding="utf-8") as f:
                                f.write(iframe_content)
                            logger.info(f"Saved iframe {i+1} content to debug_iframe_{i+1}_content.html")
                        except Exception as e:
                            logger.warning(f"Could not get iframe {i+1} content: {e}")

            # Try to find any input fields on the page
            input_count = await page.locator('input').count()
            logger.info(f"Found {input_count} input fields on the page")

            if input_count > 0:
                # Log details about each input field
                for i in range(input_count):
                    try:
                        input_type = await page.locator(f'input:nth-child({i+1})').get_attribute('type') or 'unknown'
                        input_name = await page.locator(f'input:nth-child({i+1})').get_attribute('name') or 'unknown'
                        input_id = await page.locator(f'input:nth-child({i+1})').get_attribute('id') or 'unknown'
                        input_placeholder = await page.locator(f'input:nth-child({i+1})').get_attribute('placeholder') or 'unknown'
                        logger.info(f"Input {i+1}: type={input_type}, name={input_name}, id={input_id}, placeholder={input_placeholder}")
                    except Exception as e:
                        logger.warning(f"Could not get details for input {i+1}: {e}")

            # Try different selectors for first name input
            first_name_selectors = [
                '[data-test="first-name-input"]',
                'input[name="firstName"]',
                'input[id="firstName"]',
                'input[placeholder*="Vorname"]',
                'input[aria-label*="Vorname"]',
                'input[type="text"]:nth-child(1)',
                'input:first-child',
                'input.form-control:first-child',
                'input[type="text"]'
            ]

            # Try to find the first name input
            first_name_input = None
            for selector in first_name_selectors:
                count = await page.locator(selector).count()
                logger.info(f"First name selector '{selector}': found {count} elements")
                if count > 0:
                    first_name_input = selector
                    break

            if not first_name_input:
                # Try to find any button that might proceed to the actual form
                logger.warning("Could not find first name input field, looking for buttons to proceed...")

                button_selectors = [
                    'button',
                    'a.button',
                    'a[role="button"]',
                    'a:has-text("Weiter")',
                    'a:has-text("Start")',
                    'a:has-text("Begin")',
                    'a:has-text("Beginnen")',
                    'button:has-text("Weiter")',
                    'button:has-text("Start")',
                    'button:has-text("Begin")',
                    'button:has-text("Beginnen")'
                ]

                for selector in button_selectors:
                    count = await page.locator(selector).count()
                    logger.info(f"Button selector '{selector}': found {count} elements")
                    if count > 0:
                        # Try clicking the first button
                        try:
                            await page.click(selector)
                            logger.info(f"Clicked button with selector: {selector}")

                            # Wait for page to update
                            await asyncio.sleep(3)

                            # Take another screenshot
                            screenshot_path = "debug_step1_after_button.png"
                            await page.screenshot(path=screenshot_path)
                            logger.info(f"Saved screenshot after clicking button to {screenshot_path}")

                            # Try again to find the first name input
                            for selector in first_name_selectors:
                                count = await page.locator(selector).count()
                                logger.info(f"First name selector '{selector}' (after button click): found {count} elements")
                                if count > 0:
                                    first_name_input = selector
                                    break

                            if first_name_input:
                                break
                        except Exception as e:
                            logger.warning(f"Error clicking button: {e}")

                if not first_name_input:
                    logger.error("Could not find first name input field even after trying to proceed")
                    return False

            # Try different selectors for last name input
            last_name_selectors = [
                '[data-test="last-name-input"]',
                'input[name="lastName"]',
                'input[id="lastName"]',
                'input[placeholder*="Nachname"]',
                'input[aria-label*="Nachname"]'
            ]

            # Try to find the last name input
            last_name_input = None
            for selector in last_name_selectors:
                count = await page.locator(selector).count()
                logger.info(f"Last name selector '{selector}': found {count} elements")
                if count > 0:
                    last_name_input = selector
                    break

            if not last_name_input:
                logger.error("Could not find last name input field")
                return False

            # Try different selectors for birth date inputs
            day_selectors = [
                '[data-test="day"]',
                'input[name="birthDate.day"]',
                'input[id="day"]',
                'input[placeholder*="Tag"]',
                'input[aria-label*="Tag"]'
            ]

            month_selectors = [
                '[data-test="month"]',
                'input[name="birthDate.month"]',
                'input[id="month"]',
                'input[placeholder*="Monat"]',
                'input[aria-label*="Monat"]'
            ]

            year_selectors = [
                '[data-test="year"]',
                'input[name="birthDate.year"]',
                'input[id="year"]',
                'input[placeholder*="Jahr"]',
                'input[aria-label*="Jahr"]'
            ]

            # Try to find the birth date inputs
            day_input = None
            for selector in day_selectors:
                count = await page.locator(selector).count()
                logger.info(f"Day selector '{selector}': found {count} elements")
                if count > 0:
                    day_input = selector
                    break

            month_input = None
            for selector in month_selectors:
                count = await page.locator(selector).count()
                logger.info(f"Month selector '{selector}': found {count} elements")
                if count > 0:
                    month_input = selector
                    break

            year_input = None
            for selector in year_selectors:
                count = await page.locator(selector).count()
                logger.info(f"Year selector '{selector}': found {count} elements")
                if count > 0:
                    year_input = selector
                    break

            # Fill first name
            if first_name_input:
                await utils.fill_input_humanlike(page, first_name_input, user_data['first_name'])
                await utils.random_async_delay(0.5, 1.5)
            else:
                logger.error("Could not find first name input field")
                return False

            # Fill last name
            if last_name_input:
                await utils.fill_input_humanlike(page, last_name_input, user_data['last_name'])
                await utils.random_async_delay(0.5, 1.5)
            else:
                logger.error("Could not find last name input field")
                return False

            # Fill birth date (day, month, year)
            birth_parts = user_data['birth_date'].split('.')

            if day_input and month_input and year_input:
                await utils.fill_input_humanlike(page, day_input, birth_parts[0])
                await utils.random_async_delay(0.3, 0.8)
                await utils.fill_input_humanlike(page, month_input, birth_parts[1])
                await utils.random_async_delay(0.3, 0.8)
                await utils.fill_input_humanlike(page, year_input, birth_parts[2])
                await utils.random_async_delay(0.5, 1.5)
            else:
                logger.error("Could not find all birth date input fields")
                return False

            # Take a screenshot after filling
            screenshot_path = "debug_step1_after_fill.png"
            await page.screenshot(path=screenshot_path)
            logger.info(f"Saved Step 1 after fill screenshot to {screenshot_path}")

            # Try different selectors for Next button
            next_button_selectors = [
                '[data-test="progress-meter-next"]',
                'button[type="submit"]',
                'button:has-text("Weiter")',
                'button:has-text("Next")',
                'button.button--primary',
                'button.button--next'
            ]

            # Try to find the Next button
            next_button = None
            for selector in next_button_selectors:
                count = await page.locator(selector).count()
                logger.info(f"Next button selector '{selector}': found {count} elements")
                if count > 0:
                    next_button = selector
                    break

            if not next_button:
                logger.error("Could not find Next button")
                return False

            # Wait for Next button to be enabled
            try:
                await page.wait_for_selector(f'{next_button}:not([disabled])', timeout=5000)
            except Exception as e:
                logger.warning(f"Next button may not be enabled: {e}")
                # Continue anyway, as the button might still be clickable

            # Click Next button
            try:
                await page.click(next_button)
                logger.info(f"Clicked Next button with selector: {next_button}")
            except Exception as e:
                logger.error(f"Error clicking Next button: {e}")
                return False

            # Wait for next step to load
            await utils.random_async_delay(2, 3)

            # Take a screenshot after clicking Next
            screenshot_path = "debug_step1_after_next.png"
            await page.screenshot(path=screenshot_path)
            logger.info(f"Saved Step 1 after Next screenshot to {screenshot_path}")

            # Check if we moved to step 2
            if await self._is_on_step(2):
                logger.info("Successfully completed Step 1")
                return True
            else:
                logger.error("Failed to proceed to Step 2")
                return False

        except Exception as e:
            logger.error(f"Error in Step 1 (Personal Information): {e}")

            # Take a screenshot on error
            try:
                screenshot_path = "debug_step1_error.png"
                await page.screenshot(path=screenshot_path)
                logger.info(f"Saved error screenshot to {screenshot_path}")
            except:
                pass

            return False

    async def _fill_step2_email_selection(self, user_data):
        """Fill Step 2: Email Address Selection.

        Args:
            user_data: User data dictionary

        Returns:
            tuple: (success, email) or (False, None) on failure
        """
        try:
            page = self.browser_manager.page
            logger.info("Filling Step 2: Email Address Selection")

            # Wait for email selection to be visible
            await page.wait_for_selector('.email-alias-advanced__content', state="visible", timeout=10000)

            # Try to select a free email suggestion first
            free_email = None

            # Look for suggestions with "kostenlos" text
            suggestion_selectors = [
                '.onereg-suggestion-item-advanced-row:has-text("kostenlos")',
                '.onereg-suggestion-item-advanced__text:has-text("gmx.de")'
            ]

            for selector in suggestion_selectors:
                suggestions = await page.locator(selector).all()
                if len(suggestions) > 0:
                    # Get the email text from the first suggestion
                    suggestion_element = suggestions[0]
                    email_element = await suggestion_element.locator('.onereg-suggestion-item-advanced__text').first
                    if email_element:
                        free_email = await email_element.text_content()
                        # Click on the suggestion
                        await suggestion_element.click()
                        await utils.random_async_delay(1, 2)
                        break

            # If no suggestion was clicked, enter custom email
            if not free_email:
                logger.info("No free email suggestion found, entering custom email")

                # Wait for email input to be visible
                await page.wait_for_selector('[data-test="check-email-availability-email-input"]', state="visible", timeout=5000)

                # Fill email input
                await utils.fill_input_humanlike(
                    page,
                    '[data-test="check-email-availability-email-input"]',
                    user_data['username']
                )
                await utils.random_async_delay(0.5, 1)

                # Select domain (gmx.de)
                await page.select_option(
                    '[data-test="check-email-availability-email-domain-input"]',
                    'gmx.de'
                )
                await utils.random_async_delay(0.5, 1)

                # Wait for check button to be enabled
                await page.wait_for_selector('[data-test="check-email-availability-check-button"]:not([disabled])', timeout=5000)

                # Click check button
                await page.click('[data-test="check-email-availability-check-button"]')
                await utils.random_async_delay(2, 3)

                # Check if email is available
                error_selector = '[data-test="check-email-availability-failure-message-alias"]'
                if await page.locator(error_selector).count() > 0 and await page.locator(f"{error_selector}:has-text('nicht verfügbar')").count() > 0:
                    logger.error("Email is not available")
                    return False, None

                # Construct the email
                free_email = f"{user_data['username']}@gmx.de"

            # Wait for Next button to be enabled
            next_button = await page.locator('[data-test="progress-meter-next"]').first
            if next_button:
                # Check if button is enabled
                is_disabled = await next_button.get_attribute('disabled') is not None
                if not is_disabled:
                    # Click Next button
                    await next_button.click()
                    await utils.random_async_delay(1, 2)

                    # Check if we moved to step 3
                    if await self._is_on_step(3):
                        logger.info(f"Successfully completed Step 2, selected email: {free_email}")
                        return True, free_email

            logger.error("Failed to proceed to Step 3")
            return False, None

        except Exception as e:
            logger.error(f"Error in Step 2 (Email Selection): {e}")
            return False, None

    async def _fill_step3_password(self, user_data):
        """Fill Step 3: Password Creation and Additional Information.

        Args:
            user_data: User data dictionary

        Returns:
            bool: True if step completed successfully, False otherwise
        """
        try:
            page = self.browser_manager.page
            logger.info("Filling Step 3: Password Creation and Additional Information")

            # Check if we're on the password input page or additional info page
            password_selectors = [
                '[data-test="choose-password-input"]',
                '[data-test="password-input"]',
                'input[id="password"]'
            ]

            password_input_visible = False
            for selector in password_selectors:
                if await page.locator(selector).count() > 0:
                    password_input_visible = True
                    password_selector = selector
                    break

            if password_input_visible:
                # Fill password
                await utils.fill_input_humanlike(page, password_selector, user_data['password'])
                await utils.random_async_delay(0.5, 1.5)

                # Fill confirm password if present
                confirm_password_selectors = [
                    '[data-test="choose-password-confirm-input"]',
                    'input[id="confirm-password"]'
                ]

                for selector in confirm_password_selectors:
                    if await page.locator(selector).count() > 0:
                        await utils.fill_input_humanlike(page, selector, user_data['password'])
                        await utils.random_async_delay(0.5, 1.5)
                        break

                # Wait for Next button to be enabled
                await page.wait_for_selector('[data-test="progress-meter-next"]:not([disabled])', timeout=5000)

                # Click Next button
                await page.click('[data-test="progress-meter-next"]')
                await utils.random_async_delay(1, 2)

            # Check if we're on the additional info page (gender and address)
            gender_radio_visible = await page.locator('[data-test="gender-radio-group"]').count() > 0

            if gender_radio_visible:
                logger.info("Filling additional information (gender and address)")

                # Randomly select a gender (female, male, or neutral)
                gender_values = ["FEMALE", "MALE", "UNKNOWN"]
                selected_gender = random.choice(gender_values)
                logger.info(f"Randomly selected gender: {selected_gender}")

                await page.locator(f'[data-test="gender-radio-group"] input[value="{selected_gender}"]').click()
                await utils.random_async_delay(0.5, 1)

                # Fill postal code
                await utils.fill_input_humanlike(page, '[data-test="postal-code-input"]', user_data['postal_code'])
                await utils.random_async_delay(0.5, 1)

                # Fill city
                await utils.fill_input_humanlike(page, '[data-test="town-input"]', user_data['city'])
                await utils.random_async_delay(0.5, 1)

                # Fill street and house number
                await utils.fill_input_humanlike(page, '[data-test="street-and-number-input"]', user_data['street'])
                await utils.random_async_delay(0.5, 1)

                # Select country (Germany)
                await page.select_option('[data-test="country-input"]', 'DE')
                await utils.random_async_delay(0.5, 1)

                # Wait for Next button to be enabled
                await page.wait_for_selector('[data-test="progress-meter-next"]:not([disabled])', timeout=5000)

                # Click Next button
                await page.click('[data-test="progress-meter-next"]')
                await utils.random_async_delay(1, 2)

            # Check if we moved to step 4
            if await self._is_on_step(4):
                logger.info("Successfully completed Step 3")
                return True
            else:
                logger.error("Failed to proceed to Step 4")
                return False

        except Exception as e:
            logger.error(f"Error in Step 3 (Password Creation and Additional Information): {e}")
            return False

    async def _fill_step4_address(self, user_data):
        """Fill Step 4: Address Information.

        Args:
            user_data: User data dictionary

        Returns:
            bool: True if step completed successfully, False otherwise
        """
        try:
            page = self.browser_manager.page
            logger.info("Filling Step 4: Address Information")

            # Wait for address inputs to be visible
            await page.wait_for_selector('[data-test="street-input"]', state="visible", timeout=10000)

            # Fill street
            await utils.fill_input_humanlike(page, '[data-test="street-input"]', user_data['street'])
            await utils.random_async_delay(0.5, 1.5)

            # Fill postal code
            await utils.fill_input_humanlike(page, '[data-test="zip-input"]', user_data['postal_code'])
            await utils.random_async_delay(0.5, 1.5)

            # Fill city
            await utils.fill_input_humanlike(page, '[data-test="city-input"]', user_data['city'])
            await utils.random_async_delay(0.5, 1.5)

            # Select country (Germany)
            country_selector = '[data-test="country-select"]'
            if await page.locator(country_selector).count() > 0:
                await page.select_option(country_selector, 'DE')
                await utils.random_async_delay(0.5, 1.5)

            # Wait for Next button to be enabled
            await page.wait_for_selector('[data-test="progress-meter-next"]:not([disabled])', timeout=5000)

            # Click Next button
            await page.click('[data-test="progress-meter-next"]')
            await utils.random_async_delay(1, 2)

            # Check if we moved to step 5
            if await self._is_on_step(5):
                logger.info("Successfully completed Step 4")
                return True
            else:
                logger.error("Failed to proceed to Step 5")
                return False

        except Exception as e:
            logger.error(f"Error in Step 4 (Address Information): {e}")
            return False

    async def _fill_step5_phone_verification(self):
        """Fill Step 5: Phone Verification.

        Returns:
            bool: True if step completed successfully, False otherwise
        """
        try:
            page = self.browser_manager.page
            logger.info("Filling Step 5: Phone Verification")

            # Wait for phone input to be visible
            phone_input_selectors = [
                '[data-test="mobile-phone-input"]',
                'input[type="tel"]',
                'input[placeholder*="Mobilfunknummer"]'
            ]

            phone_input_visible = False
            for selector in phone_input_selectors:
                if await page.locator(selector).count() > 0:
                    phone_input_visible = True
                    phone_selector = selector
                    break

            if not phone_input_visible:
                logger.error("Phone input field not found")
                return False

            # Get a phone number from SMS-Activate
            activation_id, phone_number = self.sms_manager.get_next_phone_number(service=config.SMS_SERVICE_CODE)

            if not activation_id or not phone_number:
                logger.error("Failed to get phone number for verification")
                return False

            self.phone_numbers_used += 1

            # Format the phone number as required by GMX (remove country code if needed)
            formatted_phone = phone_number
            if phone_number.startswith('+'):
                formatted_phone = phone_number[1:]  # Remove + sign
            elif phone_number.startswith('49'):
                formatted_phone = phone_number[2:]  # Remove country code

            # Select country code if available
            country_code_selectors = [
                '[data-test="mobile-phone-prefix-input"]',
                'select[autocomplete="tel-country-code"]'
            ]

            for selector in country_code_selectors:
                if await page.locator(selector).count() > 0:
                    # Select Germany (+49)
                    await page.select_option(selector, value="2: Object")
                    await utils.random_async_delay(0.5, 1)
                    break

            # Fill the phone number field
            await utils.fill_input_humanlike(page, phone_selector, formatted_phone)
            await utils.random_async_delay(0.5, 1.5)

            # Click the SMS code request button
            sms_request_button_selectors = [
                '[data-test="code-send-view__button"]',
                'button:has-text("SMS-Code anfordern")'
            ]

            sms_button_clicked = False
            for selector in sms_request_button_selectors:
                if await page.locator(selector).count() > 0 and await page.locator(f"{selector}:not([disabled])").count() > 0:
                    await page.click(selector)
                    sms_button_clicked = True
                    await utils.random_async_delay(1, 2)
                    break

            if not sms_button_clicked:
                logger.error("SMS code request button not found or not enabled")
                return False

            # Set the activation status to ready
            self.sms_manager.set_status(activation_id, "ready")

            # Wait for SMS code input fields to be visible
            await utils.random_async_delay(2, 3)

            # Check for code input fields
            code_input_selectors = [
                '[data-testid="code-input-1"]',
                '.code-inputs__input',
                'input[inputmode="numeric"]'
            ]

            code_inputs_visible = False
            for selector in code_input_selectors:
                if await page.locator(selector).count() > 0:
                    code_inputs_visible = True
                    code_input_selector = selector
                    break

            if not code_inputs_visible:
                logger.error("SMS code input fields not found")
                self.sms_manager.set_status(activation_id, "cancel")
                return False

            # Wait for SMS code
            sms_code = self.sms_manager.wait_for_code(
                activation_id,
                timeout=config.SMS_TIMEOUT,
                interval=config.SMS_CHECK_INTERVAL
            )

            if not sms_code:
                logger.error("Failed to receive SMS code")
                self.sms_manager.set_status(activation_id, "cancel")
                return False

            logger.info(f"Received SMS code: {sms_code}")

            # Enter the SMS code (one digit per input field)
            code_inputs = await page.locator(code_input_selector).all()

            if len(code_inputs) >= len(sms_code):
                for i, digit in enumerate(sms_code):
                    if i < len(code_inputs):
                        await code_inputs[i].fill(digit)
                        await utils.random_async_delay(0.2, 0.5)
            else:
                logger.error(f"Not enough code input fields: {len(code_inputs)} fields for {len(sms_code)} digits")
                return False

            # Wait for Next button to be enabled
            await utils.random_async_delay(1, 2)

            # Check if Next button is available and enabled
            next_button_selectors = [
                '[data-test="progress-meter-next"]:not([disabled])',
                'button:has-text("Weiter"):not([disabled])'
            ]

            next_button_visible = False
            for selector in next_button_selectors:
                if await page.locator(selector).count() > 0:
                    next_button_visible = True
                    next_button_selector = selector
                    break

            if next_button_visible:
                await page.click(next_button_selector)
                await utils.random_async_delay(1, 2)

            # Mark the activation as used
            self.sms_manager.set_status(activation_id, "used")

            # Check if we moved to step 6
            if await self._is_on_step(6):
                logger.info("Successfully completed Step 5")
                return True
            else:
                logger.error("Failed to proceed to Step 6")
                return False

        except Exception as e:
            logger.error(f"Error in Step 5 (Phone Verification): {e}")
            return False

    async def _fill_step6_confirmation(self):
        """Fill Step 6: Final Confirmation.

        Returns:
            bool: True if step completed successfully, False otherwise
        """
        try:
            page = self.browser_manager.page
            logger.info("Filling Step 6: Final Confirmation")

            # Check for terms and conditions checkboxes
            checkbox_selectors = [
                '[data-test="terms-checkbox"]',
                'input[type="checkbox"]'
            ]

            for selector in checkbox_selectors:
                checkboxes = await page.locator(selector).all()
                for checkbox in checkboxes:
                    # Check if not already checked
                    is_checked = await checkbox.is_checked()
                    if not is_checked:
                        await checkbox.check()
                        await utils.random_async_delay(0.3, 0.8)

            # Wait for Submit button to be enabled
            submit_button_selectors = [
                '[data-test="submit-button"]',
                'button[type="submit"]',
                'button:has-text("Registrierung abschließen")',
                'button:has-text("Fertig")'
            ]

            submit_button_visible = False
            for selector in submit_button_selectors:
                if await page.locator(selector).count() > 0 and await page.locator(f"{selector}:not([disabled])").count() > 0:
                    submit_button_visible = True
                    submit_button_selector = selector
                    break

            if not submit_button_visible:
                logger.error("Submit button not found or not enabled")
                return False

            # Click Submit button
            await page.click(submit_button_selector)

            # Wait for registration to complete
            await utils.random_async_delay(3, 5)

            logger.info("Successfully completed Step 6")
            return True

        except Exception as e:
            logger.error(f"Error in Step 6 (Final Confirmation): {e}")
            return False

    async def _check_registration_success(self):
        """Check if registration was successful.

        Returns:
            bool: True if registration was successful, False otherwise
        """
        try:
            page = self.browser_manager.page

            # Check for success indicators
            success_indicators = [
                'text="Herzlich willkommen"',
                'text="Willkommen bei GMX"',
                'text="Ihr Postfach wurde erfolgreich erstellt"',
                'text="Dein Postfach wurde erfolgreich erstellt"',
                'text="Registrierung erfolgreich"'
            ]

            for indicator in success_indicators:
                if await page.locator(indicator).count() > 0:
                    logger.info("Registration successful")
                    return True

            # Check for error messages
            error_indicators = [
                '.error-message',
                '.form-error',
                'text="Diese E-Mail-Adresse ist bereits vergeben"',
                'text="Ein Fehler ist aufgetreten"'
            ]

            for indicator in error_indicators:
                if await page.locator(indicator).count() > 0:
                    error_text = await page.locator(indicator).text_content()
                    logger.error(f"Registration error: {error_text}")
                    return False

            # Take a screenshot for manual verification
            await page.screenshot(path="registration_result.png")
            logger.warning("Could not determine registration status, saved screenshot for verification")

            # If we can't determine the status, assume it was successful
            return True

        except Exception as e:
            logger.error(f"Error checking registration success: {e}")
            return False

    async def _is_on_step(self, step_number):
        """Check if we're on a specific step of the registration process.

        Args:
            step_number: Step number to check (1-6)

        Returns:
            bool: True if on the specified step, False otherwise
        """
        try:
            page = self.browser_manager.page

            # Take a screenshot for debugging
            screenshot_path = f"debug_step{step_number}_check.png"
            await page.screenshot(path=screenshot_path)
            logger.info(f"Saved step {step_number} check screenshot to {screenshot_path}")

            # Get the current URL
            current_url = page.url
            logger.info(f"Current URL during step {step_number} check: {current_url}")

            # Get the page content for more thorough checking
            page_content = await page.content()

            # Save the page content for debugging
            with open(f"debug_step{step_number}_check.html", "w", encoding="utf-8") as f:
                f.write(page_content)
            logger.info(f"Saved step {step_number} check page content to debug_step{step_number}_check.html")

            # Check for step indicator
            step_indicators = [
                f'[data-test="progress-meter-item"]:nth-child({step_number}).onereg-progress-meter__item--active',
                f'.onereg-progress-meter__item:nth-child({step_number}).onereg-progress-meter__item--active',
                f'text="{step_number} von {config.GMX_STEPS_COUNT}"',
                f'text="Schritt {step_number}"',
                f'text="Step {step_number}"'
            ]

            # Check for step-specific content
            if step_number == 1:
                # Personal information step
                step_indicators.extend([
                    'input[name="firstName"]',
                    'input[name="lastName"]',
                    'input[name="birthDate.day"]',
                    'text="Persönliche Daten"',
                    'text="Personal Information"'
                ])
            elif step_number == 2:
                # Email selection step
                step_indicators.extend([
                    'input[name="email"]',
                    'text="E-Mail-Adresse"',
                    'text="Email Address"',
                    'text="Wähle Deine E-Mail-Adresse"',
                    'text="Choose your email address"'
                ])
            elif step_number == 3:
                # Password step
                step_indicators.extend([
                    'input[name="password"]',
                    'input[type="password"]',
                    'text="Passwort"',
                    'text="Password"',
                    'text="Wähle Dein Passwort"',
                    'text="Choose your password"'
                ])
            elif step_number == 4:
                # Address step
                step_indicators.extend([
                    'input[name="street"]',
                    'input[name="zipCode"]',
                    'input[name="city"]',
                    'text="Anschrift"',
                    'text="Address"'
                ])
            elif step_number == 5:
                # Phone verification step
                step_indicators.extend([
                    'input[type="tel"]',
                    'text="Handynummer"',
                    'text="Mobile Phone"',
                    'text="SMS-Code"',
                    'text="Verification Code"'
                ])
            elif step_number == 6:
                # Confirmation step
                step_indicators.extend([
                    'input[type="checkbox"]',
                    'text="Bestätigung"',
                    'text="Confirmation"',
                    'text="Registrierung abschließen"',
                    'text="Complete Registration"'
                ])

            for indicator in step_indicators:
                count = await page.locator(indicator).count()
                logger.info(f"Step {step_number} indicator '{indicator}': found {count} elements")
                if count > 0:
                    logger.info(f"On step {step_number} based on indicator: {indicator}")
                    return True

            logger.warning(f"Not on step {step_number} - no indicators found")
            return False

        except Exception as e:
            logger.error(f"Error checking step: {e}")
            return False



    async def run(self, count=1):
        """Run the GMX Creator to create multiple accounts.

        Args:
            count: Number of accounts to create (0 for unlimited)

        Returns:
            tuple: (successful_count, failed_count)
        """
        self.start_time = time.time()
        self.accounts_created = 0
        self.accounts_failed = 0
        self.phone_numbers_used = 0
        self.ip_changes = 0

        # Check SMS-Activate balance
        balance = self.sms_manager.get_balance()
        if balance is not None:
            logger.info(f"SMS-Activate balance: ${balance:.4f}")

            # Check if we have enough balance for the requested number of accounts
            if count > 0:
                estimated_cost = 0.31 * count  # Approximate cost per number
                if balance < estimated_cost:
                    logger.warning(f"Low balance warning: ${balance:.2f} may not be enough for {count} accounts (est. ${estimated_cost:.2f})")

        if count == 0:
            logger.info("Starting GMX Creator to create unlimited accounts")
        else:
            logger.info(f"Starting GMX Creator to create {count} accounts")

        try:
            # Connect to VPN if enabled
            if self.use_vpn and not self.vpn_connected:
                if not self._connect_vpn():
                    logger.warning("Failed to connect to VPN, continuing without VPN")
                    self.use_vpn = False

            # Start browser
            await self.browser_manager.start()

            # Initialize counter
            i = 0

            # Run indefinitely if count is 0, otherwise run for the specified count
            while count == 0 or i < count:
                # Increment counter
                i += 1

                # Display different message for unlimited mode
                if count == 0:
                    logger.info(f"Creating account #{i} (unlimited mode)")
                else:
                    logger.info(f"Creating account {i}/{count}")

                # Change IP after every 3 accounts if VPN is enabled
                if self.use_vpn and i > 1 and (i - 1) % 3 == 0:
                    logger.info("Changing IP address for next batch of accounts")
                    if not self._change_ip():
                        logger.warning("Failed to change IP, continuing with current IP")

                success, email, _ = await self.create_account()  # _ is used to ignore the password

                if success:
                    self.accounts_created += 1
                    logger.success(f"Account created successfully: {email}")
                else:
                    self.accounts_failed += 1
                    logger.error(f"Failed to create account #{i}")

                    # If account creation failed and we're using VPN, try changing IP
                    if not success and self.use_vpn:
                        logger.info("Account creation failed, changing IP address")
                        if not self._change_ip():
                            logger.warning("Failed to change IP, continuing with current IP")

                # Add random delay between account creations
                # For unlimited mode, always add delay
                # For limited mode, only add delay if not the last account
                if count == 0 or i < count:
                    delay = random.uniform(5, 15)
                    logger.info(f"Waiting {delay:.2f} seconds before next account")
                    await asyncio.sleep(delay)

            # Print statistics
            elapsed_time = time.time() - self.start_time
            logger.info(f"GMX Creator completed in {elapsed_time:.2f} seconds")
            logger.info(f"Accounts created: {self.accounts_created}")
            logger.info(f"Accounts failed: {self.accounts_failed}")
            logger.info(f"Phone numbers used: {self.phone_numbers_used}")

            if self.use_vpn:
                logger.info(f"IP address changes: {self.ip_changes}")

            if balance is not None:
                # Check balance again to see how much was spent
                new_balance = self.sms_manager.get_balance()
                if new_balance is not None:
                    spent = balance - new_balance
                    logger.info(f"SMS-Activate balance spent: ${spent:.4f}")
                    logger.info(f"Remaining balance: ${new_balance:.4f}")

            return self.accounts_created, self.accounts_failed

        finally:
            # Cancel any active phone number activations
            canceled = self.sms_manager.cancel_all_activations()
            if canceled > 0:
                logger.info(f"Canceled {canceled} active phone number activations")

            # Close browser
            await self.browser_manager.close()

            # Disconnect from VPN if connected
            if self.use_vpn and self.vpn_connected:
                if not self._disconnect_vpn():
                    logger.warning("Failed to disconnect from VPN")

async def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(description="GMX Email Creator")
    parser.add_argument("-c", "--count", type=int, default=1, help="Number of accounts to create (0 for unlimited)")
    parser.add_argument("--unlimited", action="store_true", help="Create unlimited accounts (same as --count 0)")
    parser.add_argument("--headless", action="store_true", help="Run in headless mode")
    parser.add_argument("--browser", choices=["chromium", "firefox", "webkit"], default="chromium", help="Browser to use")
    parser.add_argument("--sms-api-key", help="SMS-Activate API key (overrides .env setting)")
    parser.add_argument("--2captcha-api-key", dest="twocaptcha_api_key", help="2captcha API key (overrides .env setting)")
    parser.add_argument("--max-phones", type=int, default=3, help="Maximum number of phone numbers to keep in rotation")
    parser.add_argument("--no-phone-rotation", action="store_true", help="Disable phone number rotation")
    parser.add_argument("--no-vpn", action="store_true", help="Disable HMA VPN integration")
    parser.add_argument("--no-stealth", action="store_true", help="Disable stealth mode")
    parser.add_argument("--no-temp-profile", action="store_true", help="Disable temporary browser profile")
    parser.add_argument("--use-installed-browser", action="store_true", help="Use installed Chrome browser instead of bundled one")
    parser.add_argument("--max-reload-attempts", type=int, help="Maximum number of page reload attempts for blank pages")
    args = parser.parse_args()

    # Override config settings from command line if provided
    if args.sms_api_key:
        os.environ["SMS_ACTIVATE_API_KEY"] = args.sms_api_key

    if args.twocaptcha_api_key:
        os.environ["TWOCAPTCHA_API_KEY"] = args.twocaptcha_api_key

    if args.max_phones:
        config.SMS_MAX_PHONES = args.max_phones

    if args.no_phone_rotation:
        config.SMS_PHONE_ROTATION = False

    if args.no_stealth:
        config.STEALTH_ENABLED = False

    # Set max reload attempts if specified
    if args.max_reload_attempts is not None:
        config.MAX_RELOAD_ATTEMPTS = args.max_reload_attempts

    # Check for required API keys
    if not config.SMS_ACTIVATE_API_KEY:
        print("Error: SMS-Activate API key is required. Set it in .env file or use --sms-api-key.")
        return

    if not config.TWOCAPTCHA_API_KEY:
        print("Warning: 2captcha API key is not set. Captcha solving may fail.")

    # Check for HMA VPN availability
    use_vpn = not args.no_vpn
    if use_vpn and not HMA_AVAILABLE:
        print("Warning: HMA VPN integration requested but not available. Continuing without VPN.")
        use_vpn = False

    # Set temporary profile setting
    use_temp_profile = not args.no_temp_profile

    # Set installed browser setting
    use_installed_browser = args.use_installed_browser

    # If using installed browser, disable temp profile
    if use_installed_browser:
        use_temp_profile = False
        print("Using installed Chrome browser")
    elif use_temp_profile:
        print("Using temporary browser profile (will be deleted after completion)")
    else:
        print("Using persistent browser profile")

    # Set count to 0 if unlimited flag is set
    if args.unlimited:
        args.count = 0
        print("Unlimited mode enabled: Will create accounts until manually stopped")

    creator = GmxCreator(
        headless=args.headless,
        browser_type=args.browser,
        use_vpn=use_vpn,
        use_temp_profile=use_temp_profile,
        use_installed_browser=use_installed_browser
    )
    await creator.run(count=args.count)

if __name__ == "__main__":
    asyncio.run(main())
