from fivesim_integration import FiveSimManager
from time import sleep
from random import uniform







fivesim_manager = FiveSimManager()


max_phone_attempts = 2

for attempt in range(max_phone_attempts):
    print(f"Phone verification attempt {attempt + 1}/{max_phone_attempts}")

    # Get phone number from 5sim
    print("Requesting phone number from 5sim...")
    phone_number = fivesim_manager.get_phone_number_for_gmx()

    if not phone_number:
        print(f"Failed to get phone number from 5sim (attempt {attempt + 1})")
        if attempt < max_phone_attempts - 1:
            print("Retrying with new phone number...")
            sleep(uniform(5.0, 10.0)) 
            continue
        else:
            print(None)
    else:
        print(phone_number)
        break