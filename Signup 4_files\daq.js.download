//tealium universal tag - utag.loader ut4.0.202508060530, Copyright 2025 Tealium.com Inc. All Rights Reserved.
var utag_condload=false;window.__tealium_twc_switch=false;try{try{window.utag_cfg_ovrd={noview:true,split_cookie:false};sdx=document.domain.toString();sdy=sdx.split(".");sdz=(/\.co\.|\.com\.|\.org\.|\.edu\.|\.net\.|\.asn\.|\...\.jp$/.test(sdx))?3:2;window.utag_cfg_ovrd.domain=sdy.splice(sdy.length-sdz,sdz).join(".");}catch(e){console.log(e)}}catch(e){console.log(e);}
if(typeof utag=="undefined"&&!utag_condload){var utag={id:"mam.beige",o:{},sender:{},send:{},rpt:{ts:{a:new Date()}},dbi:[],db_log:[],loader:{q:[],lc:0,f:{},p:0,ol:0,wq:[],lq:[],bq:{},bk:{},rf:0,ri:0,rp:0,rq:[],ready_q:[],sendq:{"pending":0},run_ready_q:function(){for(var i=0;i<utag.loader.ready_q.length;i++){utag.DB("READY_Q:"+i);try{utag.loader.ready_q[i]()}catch(e){utag.DB(e)};}},lh:function(a,b,c){a=""+location.hostname;b=a.split(".");c=(/\.co\.|\.com\.|\.org\.|\.edu\.|\.net\.|\.asn\.|\...\.jp$/.test(a))?3:2;return b.splice(b.length-c,c).join(".");},WQ:function(a,b,c,d,g){utag.DB('WQ:'+utag.loader.wq.length);try{if(utag.udoname&&utag.udoname.indexOf(".")<0){utag.ut.merge(utag.data,window[utag.udoname],0);}
if(utag.cfg.load_rules_at_wait){utag.handler.LR(utag.data);}}catch(e){utag.DB(e)};d=0;g=[];for(a=0;a<utag.loader.wq.length;a++){b=utag.loader.wq[a];b.load=utag.loader.cfg[b.id].load;if(b.load==4){this.f[b.id]=0;utag.loader.LOAD(b.id)}else if(b.load>0){g.push(b);d++;}else{this.f[b.id]=1;}}
for(a=0;a<g.length;a++){utag.loader.AS(g[a]);}
if(d==0){utag.loader.END();}},AS:function(a,b,c,d){utag.send[a.id]=a;if(typeof a.src=='undefined'||!utag.ut.hasOwn(a,'src')){a.src=utag.cfg.path+((typeof a.name!='undefined')?a.name:'ut'+'ag.'+a.id+'.js')}
a.src+=(a.src.indexOf('?')>0?'&':'?')+'utv='+(a.v?utag.cfg.template+a.v:utag.cfg.v);utag.rpt['l_'+a.id]=a.src;b=document;this.f[a.id]=0;if(a.load==2){utag.DB("Attach sync: "+a.src);a.uid=a.id;b.write('<script id="utag_'+a.id+'" src="'+a.src+'"></scr'+'ipt>')
if(typeof a.cb!='undefined')a.cb();}else if(a.load==1||a.load==3){if(b.createElement){c='utag_mam.beige_'+a.id;if(!b.getElementById(c)){d={src:a.src,id:c,uid:a.id,loc:a.loc}
if(a.load==3){d.type="iframe"};if(typeof a.cb!='undefined')d.cb=a.cb;utag.ut.loader(d);}}}},GV:function(a,b,c){b={};for(c in a){if(a.hasOwnProperty(c)&&typeof a[c]!="function")b[c]=a[c];}
return b},OU:function(tid,tcat,a,b,c,d,f,g){g={};utag.loader.RDcp(g);try{if(typeof g['cp.OPTOUTMULTI']!='undefined'){c=utag.loader.cfg;a=utag.ut.decode(g['cp.OPTOUTMULTI']).split('|');for(d=0;d<a.length;d++){b=a[d].split(':');if(b[1]*1!==0){if(b[0].indexOf('c')==0){for(f in utag.loader.GV(c)){if(c[f].tcat==b[0].substring(1))c[f].load=0;if(c[f].tid==tid&&c[f].tcat==b[0].substring(1))return true;}
if(tcat==b[0].substring(1))return true;}else if(b[0]*1==0){utag.cfg.nocookie=true}else{for(f in utag.loader.GV(c)){if(c[f].tid==b[0])c[f].load=0}
if(tid==b[0])return true;}}}}}catch(e){utag.DB(e)}
return false;},RDdom:function(o){var d=document||{},l=location||{};o["dom.referrer"]=d.referrer;o["dom.title"]=""+d.title;o["dom.domain"]=""+l.hostname;o["dom.query_string"]=(""+l.search).substring(1);o["dom.hash"]=(""+l.hash).substring(1);o["dom.url"]=""+d.URL;o["dom.pathname"]=""+l.pathname;o["dom.viewport_height"]=window.innerHeight||(d.documentElement?d.documentElement.clientHeight:960);o["dom.viewport_width"]=window.innerWidth||(d.documentElement?d.documentElement.clientWidth:960);},RDcp:function(o,b,c,d){b=utag.loader.RC();for(d in b){if(d.match(/utag_(.*)/)){for(c in utag.loader.GV(b[d])){o["cp.utag_"+RegExp.$1+"_"+c]=b[d][c];}}}
for(c in utag.loader.GV((utag.cl&&!utag.cl['_all_'])?utag.cl:b)){if(c.indexOf("utag_")<0&&typeof b[c]!="undefined")o["cp."+c]=b[c];}},hasSplitUtagMainCookie:function(){return document.cookie.match(/([\s\S]*)utag_main_([\s\S]*)=([\s\S]*)/g);},hasUtagMainCookie:function(){return document.cookie.includes("utag_main=");},convertingToSplitCookies:function(){return utag.cfg.split_cookie&&utag.loader.hasUtagMainCookie();},revertingSplitCookies:function(){return!utag.cfg.split_cookie&&utag.loader.hasSplitUtagMainCookie();},readIndividualCookies:function(){if(!document.cookie||document.cookie===""){return{};}
var cookies=document.cookie.split("; ");return cookies.reduce(function(result,cookie){var kv=cookie.split("=");if(kv[0].startsWith("utag_")){var cookieName=kv[0].split("_")[1];var cookieNameWithTag="utag_"+cookieName;if(!result[cookieNameWithTag]){result[cookieNameWithTag]={};}
var nameTrimmed=kv[0].replace(cookieNameWithTag+"_","");result[cookieNameWithTag][nameTrimmed]=String(kv[1]).replace(/%3B/g,';')}
return result;},{});},RDqp:function(o,a,b,c){a=location.search+(location.hash+'').replace("#","&");if(utag.cfg.lowerqp){a=a.toLowerCase()};if(a.length>1){b=a.substring(1).split('&');for(a=0;a<b.length;a++){c=b[a].split("=");if(c.length>1){o["qp."+c[0]]=utag.ut.decode(c[1])}}}},RDmeta:function(o,a,b,h){a=document.getElementsByTagName("meta");for(b=0;b<a.length;b++){try{h=a[b].name||a[b].getAttribute("property")||"";}catch(e){h="";utag.DB(e)};if(utag.cfg.lowermeta){h=h.toLowerCase()};if(h!=""){o["meta."+h]=a[b].content}}},RDva:function(o){var readAttr=function(o,l){var a="",b;a=localStorage.getItem(l);if(!a||a=="{}")return;b=utag.ut.flatten({va:JSON.parse(a)});utag.ut.merge(o,b,1);}
try{readAttr(o,"tealium_va");readAttr(o,"tealium_va_"+o["ut.account"]+"_"+o["ut.profile"]);}catch(e){utag.DB(e)}},RDut:function(o,a){var t={};var d=new Date();var m=(utag.ut.typeOf(d.toISOString)=="function");o["ut.domain"]=utag.cfg.domain;o["ut.version"]=utag.cfg.v;t["tealium_event"]=o["ut.event"]=a||"view";t["tealium_visitor_id"]=o["ut.visitor_id"]=o["cp.utag_main_v_id"];t["tealium_session_id"]=o["ut.session_id"]=o["cp.utag_main_ses_id"];t["tealium_session_number"]=o["cp.utag_main__sn"];t["tealium_session_event_number"]=o["cp.utag_main__se"];try{t["tealium_datasource"]=utag.cfg.datasource;t["tealium_account"]=o["ut.account"]=utag.cfg.utid.split("/")[0];t["tealium_profile"]=o["ut.profile"]=utag.cfg.utid.split("/")[1];t["tealium_environment"]=o["ut.env"]="prod";}catch(e){utag.DB(e)}
t["tealium_random"]=Math.random().toFixed(16).substring(2);t["tealium_library_name"]="ut"+"ag.js";t["tealium_library_version"]=(utag.cfg.template+"0").substring(2);t["tealium_timestamp_epoch"]=Math.floor(d.getTime()/1000);t["tealium_timestamp_utc"]=(m?d.toISOString():"");d.setHours(d.getHours()-(d.getTimezoneOffset()/60));t["tealium_timestamp_local"]=(m?d.toISOString().replace("Z",""):"");utag.ut.merge(o,t,0);},RDses:function(o,a,c){a=(new Date()).getTime();c=(a+parseInt(utag.cfg.session_timeout))+"";if(!o["cp.utag_main_ses_id"]){o["cp.utag_main_ses_id"]=a+"";o["cp.utag_main__ss"]="1";o["cp.utag_main__se"]="1";o["cp.utag_main__sn"]=(1+parseInt(o["cp.utag_main__sn"]||0))+"";}else{o["cp.utag_main__ss"]="0";o["cp.utag_main__se"]=(1+parseInt(o["cp.utag_main__se"]||0))+"";}
o["cp.utag_main__pn"]=o["cp.utag_main__pn"]||"1";o["cp.utag_main__st"]=c;var ses_id=utag.loader.addExpSessionFlag(o["cp.utag_main_ses_id"]||a);var pn=utag.loader.addExpSessionFlag(o["cp.utag_main__pn"]);var ss=utag.loader.addExpSessionFlag(o["cp.utag_main__ss"]);var st=utag.loader.addExpSessionFlag(c);var se=utag.loader.addExpSessionFlag(o["cp.utag_main__se"]);utag.loader.SC("utag_main",{_sn:(o["cp.utag_main__sn"]||1),_se:se,_ss:ss,_st:st,ses_id:ses_id,_pn:pn});},containsExpSessionFlag:function(v){return String(v).replace(/%3B/g,';').includes(";exp-session");},addExpSessionFlag:function(v){return utag.loader.containsExpSessionFlag(v)?v:v+";exp-session";},containsExpFlag:function(v){return String(v).replace(/%3B/g,';').includes(";exp-");},addExpFlag:function(v,x){return utag.loader.containsExpFlag(v)?v:v+";exp-"+String(x);},RDpv:function(o){if(typeof utag.pagevars=="function"){utag.DB("Read page variables");utag.pagevars(o);}},RDlocalStorage:function(o){if(utag.cfg.ignoreLocalStorage){return;}
Object.keys(window.localStorage).forEach(function(localStorageKey){o["ls."+localStorageKey]=window.localStorage[localStorageKey];});},RDsessionStorage:function(o){if(utag.cfg.ignoreSessionStorage){return;}
Object.keys(window.sessionStorage).forEach(function(sessionStorageKey){o["ss."+sessionStorageKey]=window.sessionStorage[sessionStorageKey];});},convertCustomMultiCookies:function(){var cookiesToConvert={}
if(utag.loader.convertingToSplitCookies()){utag.loader.mapUtagCookies(function(parentCookie){cookiesToConvert[parentCookie.key]=cookiesToConvert[parentCookie.key]||{}
parentCookie.value.split('$').forEach(function(subCookie){var key=subCookie.split(':')[0]
var value=subCookie.split(':')[1]
cookiesToConvert[parentCookie.key][key]=(String(value).indexOf('%3Bexp-')!==-1&&String(value).indexOf('%3Bexp-session')===-1)?String(value).replace(/%3B/g,';')+'u':String(value).replace(/%3B/g,';');})})}else if(utag.loader.revertingSplitCookies()){utag.loader.mapUtagCookies(function(splitCookie){var parentCookieName=splitCookie.key.match(/^utag_[^_]*/)[0];var subCookieName=splitCookie.key.split(parentCookieName+'_')[1];cookiesToConvert[parentCookieName]=cookiesToConvert[parentCookieName]||{};cookiesToConvert[parentCookieName][subCookieName]=(String(splitCookie.value).indexOf('%3Bexp-')!==-1&&String(splitCookie.value).indexOf('%3Bexp-session'))===-1?String(splitCookie.value).replace(/%3B/g,';')+'u':String(splitCookie.value).replace(/%3B/g,';');})}
if(utag.loader.convertingToSplitCookies()){utag.loader.getUtagCookies().forEach(function(cookie){utag.loader.deleteCookie(cookie.key);});}else if(utag.loader.revertingSplitCookies()){utag.loader.deleteIndividualCookies();}
Object.keys(cookiesToConvert).forEach(function(key){utag.loader.SC(key,cookiesToConvert[key]);});},RD:function(o,a){utag.DB("utag.loader.RD");utag.DB(o);utag.loader.RDcp(o);if(utag.cfg.split_cookie){utag.loader.checkCookiesAgainstWhitelist();}
if(utag.loader.convertingToSplitCookies()||utag.loader.revertingSplitCookies()){utag.loader.convertCustomMultiCookies();}
if(!utag.loader.rd_flag){utag.loader.rd_flag=1;o["cp.utag_main__pn"]=(1+parseInt(o["cp.utag_main__pn"]||0))+"";var setVId=window.utag_cfg_ovrd&&window.utag_cfg_ovrd.always_set_v_id||false;if(setVId){o["cp.utag_main_v_id"]=o["cp.utag_main_v_id"]||utag.ut.vi((new Date()).getTime());utag.loader.SC("utag_main",{"v_id":o["cp.utag_main_v_id"]});}
utag.loader.RDses(o);}
if(a&&!utag.cfg.noview)utag.loader.RDses(o);utag.loader.RDqp(o);utag.loader.RDmeta(o);utag.loader.RDdom(o);utag.loader.RDut(o,a||"view");utag.loader.RDpv(o);utag.loader.RDva(o);utag.loader.RDlocalStorage(o);utag.loader.RDsessionStorage(o);},whitelistDefined:function(){return utag.cfg.split_cookie_allowlist&&Array.isArray(utag.cfg.split_cookie_allowlist);},cookieIsAllowed:function(key){return!utag.loader.whitelistDefined()||utag.cfg.split_cookie_allowlist.includes(key);},checkCookiesAgainstWhitelist:function(){if(!utag.loader.whitelistDefined()){return;}
utag.loader.mapUtagCookies(function(cookie){if(!utag.loader.cookieIsAllowed(cookie.key.replace("utag_main_",""))){utag.loader.deleteCookie(cookie.key);}},true);},deleteIndividualCookies:function(){utag.loader.mapUtagCookies(function(cookie){utag.loader.deleteCookie(cookie.key);});},deleteCookie:function(key){document.cookie=key+"=; path=/;domain="+utag.cfg.domain+";max-age=0;";},getUtagCookies:function(onlyUtagMain=false){var cookies=document.cookie.split("; ");var result=[];for(var i=0;i<cookies.length;i++){var cookie=cookies[i];if(cookie.startsWith(onlyUtagMain?"utag_main_":"utag_")){var kv=cookie.split("=");result.push({key:kv[0],value:kv[1]});}}
return result;},mapUtagCookies:function(mapFunction,onlyUtagMain=false){var cookies=utag.loader.getUtagCookies(onlyUtagMain);for(var i=0;i<cookies.length;i++){var cookie=cookies[i];mapFunction(cookie);}},filterArray:function(array,predicate){var y=0;for(var x=0;x<array.length;x++){if(predicate(array[x])){array[y]=array[x];y++;}}
array.length=y;},RC:function(a,x,b,c,d,e,f,g,h,i,j,k,l,m,n,o,v,ck,cv,r,s,t){o={};b=(""+document.cookie!="")?(document.cookie).split("; "):[];r=/^(.*?)=(.*)$/;s=/^(.*);exp-(.*)$/;t=(new Date()).getTime();var newMultiCookies;if(utag.loader.hasSplitUtagMainCookie()){newMultiCookies=utag.loader.readIndividualCookies();utag.loader.filterArray(b,function(cookie){return!cookie.startsWith("utag_")});}
for(c=0;c<b.length;c++){if(b[c].match(r)){ck=RegExp.$1;cv=RegExp.$2;}
e=utag.ut.decode(cv);if(typeof ck!="undefined"){if(ck.indexOf("ulog")==0||ck.indexOf("utag_")==0){e=cv.split("$");g=[];j={};for(f=0;f<e.length;f++){try{g=e[f].split(":");if(g.length>2){g[1]=g.slice(1).join(":");}
v="";if((""+g[1]).indexOf("~")==0){h=g[1].substring(1).split("|");for(i=0;i<h.length;i++)h[i]=utag.ut.decode(h[i]);v=h}else v=utag.ut.decode(g[1]);j[g[0]]=v;}catch(er){utag.DB(er)};}
o[ck]={};for(f in utag.loader.GV(j)){if(utag.ut.typeOf(j[f])=="array"){n=[];for(m=0;m<j[f].length;m++){if(j[f][m].match(s)){k=(RegExp.$2=="session")?(typeof j._st!="undefined"?j._st:t-1):parseInt(RegExp.$2);if(k>t)n[m]=(x==0)?j[f][m]:RegExp.$1;}}
j[f]=n.join("|");}else{j[f]=""+j[f];if(j[f].match(s)){k=(RegExp.$2=="session")?(typeof j._st!="undefined"?j._st:t-1):parseInt(RegExp.$2);j[f]=(k<t)?null:(x==0?j[f]:RegExp.$1);}}
if(j[f])o[ck][f]=j[f];}}else if(utag.cl[ck]||utag.cl['_all_']){o[ck]=e}}}
if(newMultiCookies){Object.keys(newMultiCookies).forEach(function(tag){o[tag]={};Object.keys(newMultiCookies[tag]).forEach(function(key){o[tag][key]=newMultiCookies[tag][key].split(';exp-')[0]})});}
return(a)?(o[a]?o[a]:{}):o;},SC:function(a,b,c,d,e,f,g,h,i,j,k,x,v){if(!a)return 0;if(a=="utag_main"&&utag.cfg.nocookie)return 0;v="";var date=new Date();var exp=new Date();var data;exp.setTime(date.getTime()+(365*24*60*60*1000));x=exp.toGMTString();if(c&&c==="da"||(utag.cfg.split_cookie&&c==='d')){x="Thu, 31 Dec 2009 00:00:00 GMT";data=utag.loader.GV(b);}else if(a.indexOf("utag_")!=0&&a.indexOf("ulog")!=0){if(typeof b!="object"){v=b}}else{if(utag.cfg.split_cookie){d=utag.loader.readIndividualCookies()[a]||{};data=utag.loader.GV(b);}else{d=utag.loader.RC(a,0);}
for(e in utag.loader.GV(b)){f=""+b[e];if(f.match(/^(.*);exp-(\d+)(\w)$/)){g=date.getTime()+parseInt(RegExp.$2)*((RegExp.$3=="h")?3600000:86400000);if(RegExp.$3=="u")g=parseInt(RegExp.$2);f=RegExp.$1+";exp-"+g;}
if(c=="i"){if(d[e]==null)d[e]=f;}else if(c=="d")delete d[e];else if(c=="a")d[e]=(d[e]!=null)?(f-0)+(d[e]-0):f;else if(c=="ap"||c=="au"){if(d[e]==null)d[e]=f;else{if(d[e].indexOf("|")>0){d[e]=d[e].split("|")}
g=(utag.ut.typeOf(d[e])=="array")?d[e]:[d[e]];g.push(f);if(c=="au"){h={};k={};for(i=0;i<g.length;i++){if(g[i].match(/^(.*);exp-(.*)$/)){j=RegExp.$1;}
if(typeof k[j]=="undefined"){k[j]=1;h[g[i]]=1;}}
g=[];for(i in utag.loader.GV(h)){g.push(i);}}
d[e]=g}}else d[e]=f;}
if(utag.loader.convertingToSplitCookies()===true){delete d[a];}
data=utag.loader.GV(d);h=new Array();for(g in data){if(utag.ut.typeOf(d[g])=="array"){for(c=0;c<d[g].length;c++){d[g][c]=encodeURIComponent(d[g][c])}
h.push(g+":~"+d[g].join("|"))}else h.push((g+":").replace(/[\,\$\;\?]/g,"")+encodeURIComponent(d[g]))}
if(h.length==0){h.push("");x=""}
v=(h.join("$"));}
if(utag.cfg.split_cookie&&c!=='da'&&c!=='d'){utag.loader.prepareAndWriteCookies(a,data,x);}else if(utag.cfg.split_cookie){utag.loader.mapUtagCookies(function(cookieInfo){var cookiesToDelete=Object.keys(data||{}).map(function(key){return a+'_'+key});if((c==='da'&&cookieInfo.key.startsWith(a))||(c==='d'&&cookiesToDelete.indexOf(cookieInfo.key)!==-1)){document.cookie=cookieInfo.key+"="+v+";path=/;domain="+utag.cfg.domain+";expires="+x+(utag.cfg.secure_cookie?";secure":"");}})}else{document.cookie=a+"="+v+";path=/;domain="+utag.cfg.domain+";expires="+x+(utag.cfg.secure_cookie?";secure":"");}
return 1},prepareAndWriteCookies:function(tag,data,expiration){var defaultSessionExpirationCookies=["_pn","_ss","_st","_ses_id","_se"];var originalExpiration=expiration;if(Object.keys(data).length>0){for(var key in data){expiration=originalExpiration;if(!utag.loader.cookieIsAllowed(key)){continue;}
var value=String(data[key]);if(defaultSessionExpirationCookies.includes(key)){value=utag.loader.addExpSessionFlag(value);}
if(value.match(/exp-(\d+|session)$/)){var expValue=RegExp.$1;if(expValue==="session"&&!!utag.cfg.session_timeout){value=utag.loader.addExpSessionFlag(value);expiration=new Date();expiration.setTime(expiration.getTime()+parseInt(utag.cfg.session_timeout));expiration=expiration.toGMTString();}else{var expInt=parseInt(expValue);if(!!expInt){value=utag.loader.addExpFlag(value,expInt);expiration=new Date(expInt);expiration=expiration.toGMTString();}}}
utag.loader.writeCookie(tag+"_"+key,value,expiration);}
utag.loader.deleteCookie(tag);}},writeCookie:function(key,value,expiration){if(value.includes(";")){value=value.replace(/;/g,encodeURIComponent(";"));}
document.cookie=key+"="+value+";path=/;domain="+utag.cfg.domain+";expires="+expiration+(utag.cfg.secure_cookie?";secure":"");},LOAD:function(a,b,c,d){if(!utag.loader.cfg){return}
if(this.ol==0){if(utag.loader.cfg[a].block&&utag.loader.cfg[a].cbf){this.f[a]=1;delete utag.loader.bq[a];}
for(b in utag.loader.GV(utag.loader.bq)){if(utag.loader.cfg[a].load==4&&utag.loader.cfg[a].wait==0){utag.loader.bk[a]=1;utag.DB("blocked: "+a);}
utag.DB("blocking: "+b);return;}
utag.loader.INIT();return;}
utag.DB('utag.loader.LOAD:'+a);if(this.f[a]==0){this.f[a]=1;if(utag.cfg.noview!=true){if(utag.loader.cfg[a].send){utag.DB("SENDING: "+a);try{if(utag.loader.sendq.pending>0&&utag.loader.sendq[a]){utag.DB("utag.loader.LOAD:sendq: "+a);while(d=utag.loader.sendq[a].shift()){utag.DB(d);utag.sender[a].send(d.event,utag.handler.C(d.data));utag.loader.sendq.pending--;}}else{utag.sender[a].send('view',utag.handler.C(utag.data));}
utag.rpt['s_'+a]=0;}catch(e){utag.DB(e);utag.rpt['s_'+a]=1;}}}
if(utag.loader.rf==0)return;for(b in utag.loader.GV(this.f)){if(this.f[b]==0||this.f[b]==2)return}
utag.loader.END();}},EV:function(a,b,c,d){if(b=="ready"){if(!utag.data){try{utag.cl={'_all_':1};utag.loader.initdata();utag.loader.RD(utag.data);}catch(e){utag.DB(e)};}
if((document.attachEvent||utag.cfg.dom_complete)?document.readyState==="complete":document.readyState!=="loading")setTimeout(c,1);else{utag.loader.ready_q.push(c);var RH;if(utag.loader.ready_q.length<=1){if(document.addEventListener){RH=function(){document.removeEventListener("DOMContentLoaded",RH,false);utag.loader.run_ready_q()};if(!utag.cfg.dom_complete)document.addEventListener("DOMContentLoaded",RH,false);window.addEventListener("load",utag.loader.run_ready_q,false);}else if(document.attachEvent){RH=function(){if(document.readyState==="complete"){document.detachEvent("onreadystatechange",RH);utag.loader.run_ready_q()}};document.attachEvent("onreadystatechange",RH);window.attachEvent("onload",utag.loader.run_ready_q);}}}}else{if(a.addEventListener){a.addEventListener(b,c,false)}else if(a.attachEvent){a.attachEvent(((d==1)?"":"on")+b,c)}}},END:function(b,c,d,e,v,w){if(this.ended){return};this.ended=1;utag.DB("loader.END");b=utag.data;if(utag.handler.base&&utag.handler.base!='*'){e=utag.handler.base.split(",");for(d=0;d<e.length;d++){if(typeof b[e[d]]!="undefined")utag.handler.df[e[d]]=b[e[d]]}}else if(utag.handler.base=='*'){utag.ut.merge(utag.handler.df,b,1);}
utag.rpt['r_0']="t";for(var r in utag.loader.GV(utag.cond)){utag.rpt['r_'+r]=(utag.cond[r])?"t":"f";}
utag.rpt.ts['s']=new Date();v=utag.cfg.path;w=v.indexOf(".tiqcdn.");if(w>0&&b["cp.utag_main__ss"]==1&&!utag.cfg.no_session_count)utag.ut.loader({src:v.substring(0,v.indexOf("/ut"+"ag/")+6)+"tiqapp/ut"+"ag.v.js?a="+utag.cfg.utid+(utag.cfg.nocookie?"&nocookie=1":"&cb="+(new Date).getTime()),id:"tiqapp"})
if(utag.cfg.noview!=true)utag.handler.RE('view',b,"end");utag.handler.INIT();}},DB:function(a,b){if(utag.cfg.utagdb===false){return;}else if(typeof utag.cfg.utagdb=="undefined"){b=document.cookie+'';utag.cfg.utagdb=((b.indexOf('utagdb=true')>=0)?true:false);}
if(utag.cfg.utagdb===true){var t;if(utag.ut.typeOf(a)=="object"){t=utag.handler.C(a)}else{t=a}
utag.db_log.push(t);try{if(!utag.cfg.noconsole)console.log(t)}catch(e){}}},RP:function(a,b,c){if(typeof a!='undefined'&&typeof a.src!='undefined'&&a.src!=''){b=[];for(c in utag.loader.GV(a)){if(c!='src')b.push(c+'='+escape(a[c]))}
this.dbi.push((new Image()).src=a.src+'?utv='+utag.cfg.v+'&utid='+utag.cfg.utid+'&'+(b.join('&')))}},view:function(a,c,d){return this.track({event:'view',data:a||{},cfg:{cb:c,uids:d}})},link:function(a,c,d){return this.track({event:'link',data:a||{},cfg:{cb:c,uids:d}})},track:function(a,b,c,d,e){a=a||{};if(typeof a=="string"){a={event:a,data:b||{},cfg:{cb:c,uids:d}}}
for(e in utag.loader.GV(utag.o)){utag.o[e].handler.trigger(a.event||"view",a.data||a,a.cfg||{cb:b,uids:c})}
a.cfg=a.cfg||{cb:b};if(typeof a.cfg.cb=="function")a.cfg.cb();return true},handler:{base:"",df:{},o:{},send:{},iflag:0,INIT:function(a,b,c){utag.DB('utag.handler.INIT');if(utag.initcatch){utag.initcatch=0;return}
this.iflag=1;a=utag.loader.q.length;if(a>0){utag.DB("Loader queue");for(b=0;b<a;b++){c=utag.loader.q[b];utag.handler.trigger(c.a,c.b,c.c)}}
},test:function(){return 1},LR:function(b){utag.DB("Load Rules");for(var d in utag.loader.GV(utag.cond)){utag.cond[d]=false;}
utag.DB(b);utag.loader.loadrules(b);utag.DB(utag.cond);utag.loader.initcfg();utag.loader.OU();for(var r in utag.loader.GV(utag.cond)){utag.rpt['r_'+r]=(utag.cond[r])?"t":"f";}},RE:function(a,b,c,d,e,f,g){if(c!="alr"&&!this.cfg_extend){return 0;}
utag.DB("RE: "+c);if(c=="alr")utag.DB("All Tags EXTENSIONS");utag.DB(b);if(typeof this.extend!="undefined"){g=0;for(d=0;d<this.extend.length;d++){try{e=0;if(typeof this.cfg_extend!="undefined"){f=this.cfg_extend[d];if(typeof f.count=="undefined")f.count=0;if(f[a]==0||(f.once==1&&f.count>0)||f[c]==0){e=1}else{if(f[c]==1){g=1};f.count++}}
if(e!=1){this.extend[d](a,b);utag.rpt['ex_'+d]=0}}catch(er){utag.DB(er);utag.rpt['ex_'+d]=1;utag.ut.error({e:er.message,s:utag.cfg.path+'utag.js',l:d,t:'ge'});}}
utag.DB(b);return g;}},trigger:function(a,b,c,d,e,f){utag.DB('trigger:'+a+(c&&c.uids?":"+c.uids.join(","):""));b=b||{};utag.DB(b);if(!this.iflag){utag.DB("trigger:called before tags loaded");for(d in utag.loader.f){if(!(utag.loader.f[d]===1))utag.DB('Tag '+d+' did not LOAD')}
utag.loader.q.push({a:a,b:utag.handler.C(b),c:c});return;}
utag.ut.merge(b,this.df,0);utag.loader.RD(b,a);utag.cfg.noview=false;function sendTag(a,b,d){try{if(typeof utag.sender[d]!="undefined"){utag.DB("SENDING: "+d);utag.sender[d].send(a,utag.handler.C(b));utag.rpt['s_'+d]=0;}else if(utag.loader.cfg[d].load!=2){utag.loader.sendq[d]=utag.loader.sendq[d]||[];utag.loader.sendq[d].push({"event":a,"data":utag.handler.C(b)});utag.loader.sendq.pending++;utag.loader.AS({id:d,load:1});}}catch(e){utag.DB(e)}}
if(c&&c.uids){this.RE(a,b,"alr");for(f=0;f<c.uids.length;f++){d=c.uids[f];if(!utag.loader.OU(utag.loader.cfg[d].tid)){sendTag(a,b,d);}}}else if(utag.cfg.load_rules_ajax){this.RE(a,b,"blr");this.LR(b);this.RE(a,b,"alr");for(f=0;f<utag.loader.cfgsort.length;f++){d=utag.loader.cfgsort[f];if(utag.loader.cfg[d].load&&utag.loader.cfg[d].send){sendTag(a,b,d);}}}else{this.RE(a,b,"alr");for(d in utag.loader.GV(utag.sender)){sendTag(a,b,d);}}
this.RE(a,b,"end");},C:function(a,b,c){b={};for(c in utag.loader.GV(a)){if(utag.ut.typeOf(a[c])=="array"){b[c]=a[c].slice(0)}else{b[c]=a[c]}}
return b}},ut:{pad:function(a,b,c,d){a=""+((a-0).toString(16));d='';if(b>a.length){for(c=0;c<(b-a.length);c++){d+='0'}}return""+d+a},vi:function(t,a,b){if(!utag.v_id){a=this.pad(t,12);b=""+Math.random();a+=this.pad(b.substring(2,b.length),16);try{a+=this.pad((navigator.plugins.length?navigator.plugins.length:0),2);a+=this.pad(navigator.userAgent.length,3);a+=this.pad(document.URL.length,4);a+=this.pad(navigator.appVersion.length,3);a+=this.pad(screen.width+screen.height+parseInt((screen.colorDepth)?screen.colorDepth:screen.pixelDepth),5)}catch(e){utag.DB(e);a+="12345"};utag.v_id=a;}
return utag.v_id},hasOwn:function(o,a){return o!=null&&Object.prototype.hasOwnProperty.call(o,a)},isEmptyObject:function(o,a){for(a in o){if(utag.ut.hasOwn(o,a))return false}
return true},isEmpty:function(o){var t=utag.ut.typeOf(o);if(t=="number"){return isNaN(o)}else if(t=="boolean"){return false}else if(t=="string"){return o.length===0}else return utag.ut.isEmptyObject(o)},typeOf:function(e){return({}).toString.call(e).match(/\s([a-zA-Z]+)/)[1].toLowerCase();},flatten:function(o){var a={};function r(c,p){if(Object(c)!==c||utag.ut.typeOf(c)=="array"){a[p]=c;}else{if(utag.ut.isEmptyObject(c)){}else{for(var d in c){r(c[d],p?p+"."+d:d);}}}}
r(o,"");return a;},merge:function(a,b,c,d){if(c){for(d in utag.loader.GV(b)){a[d]=b[d]}}else{for(d in utag.loader.GV(b)){if(typeof a[d]=="undefined")a[d]=b[d]}}},decode:function(a,b){b="";try{b=decodeURIComponent(a)}catch(e){utag.DB(e)};if(b==""){b=unescape(a)};return b},encode:function(a,b){b="";try{b=encodeURIComponent(a)}catch(e){utag.DB(e)};if(b==""){b=escape(a)};return b},error:function(a,b,c){if(typeof utag_err!="undefined"){utag_err.push(a)}},loader:function(o,a,b,c,l,m){utag.DB(o);a=document;if(o.type=="iframe"){m=a.getElementById(o.id);if(m&&m.tagName=="IFRAME"){m.parentNode.removeChild(m);}
b=a.createElement("iframe");o.attrs=o.attrs||{};utag.ut.merge(o.attrs,{"height":"1","width":"1","style":"display:none"},0);}else if(o.type=="img"){utag.DB("Attach img: "+o.src);b=new Image();}else{b=a.createElement("script");b.language="javascript";b.type="text/javascript";b.async=1;b.charset="utf-8";}
if(o.id){b.id=o.id};for(l in utag.loader.GV(o.attrs)){b.setAttribute(l,o.attrs[l])}
b.setAttribute("src",o.src);if(typeof o.cb=="function"){if(b.addEventListener){b.addEventListener("load",function(){o.cb()},false);}else{b.onreadystatechange=function(){if(this.readyState=='complete'||this.readyState=='loaded'){this.onreadystatechange=null;o.cb()}};}}
if(typeof o.error=="function"){utag.loader.EV(b,"error",o.error);}
if(o.type!="img"){l=o.loc||"head";c=a.getElementsByTagName(l)[0];if(c){utag.DB("Attach to "+l+": "+o.src);if(l=="script"){c.parentNode.insertBefore(b,c);}else{c.appendChild(b)}}}}}};utag.o['mam.beige']=utag;utag.cfg={template:"ut4.51.",load_rules_ajax:true,load_rules_at_wait:false,lowerqp:false,noconsole:false,session_timeout:1800000,readywait:0,noload:0,domain:utag.loader.lh(),datasource:"##UTDATASOURCE##".replace("##"+"UTDATASOURCE##",""),secure_cookie:("##UTSECURECOOKIE##".replace("##"+"UTSECURECOOKIE##","")==="true")?true:false,path:"//s.uicdn.com/t/prod/iq/mam/beige/",utid:"mam/beige/202508060530",ignoreSessionStorage:false,ignoreLocalStorage:false,split_cookie:true};utag.cfg.v=utag.cfg.template+"202508060530";utag.cond={119:0,120:0,121:0,122:0,123:0,125:0,126:0,127:0,128:0,129:0,130:0,131:0,132:0,133:0,27:0,52:0,66:0,73:0,74:0};utag.pagevars=function(ud){ud=ud||utag.data;try{ud['js_page.addOnBrand']=addOnBrand}catch(e){utag.DB(e)};try{ud['js_page.addOnVersion']=addOnVersion}catch(e){utag.DB(e)};try{ud['js_page.addOnVariant']=addOnVariant}catch(e){utag.DB(e)};try{ud['js_page.schemaOrgProperties']=schemaOrgProperties}catch(e){utag.DB(e)};try{ud['js_page.siteDomain']=siteDomain}catch(e){utag.DB(e)};try{ud['js_page.contentVariantString']=contentVariantString}catch(e){utag.DB(e)};try{ud['js_page.visitId']=visitId}catch(e){utag.DB(e)};try{ud['js_page.infonline.cp']=infonline.cp}catch(e){utag.DB(e)};try{ud['js_page.infonline.st']=infonline.st}catch(e){utag.DB(e)};try{ud['js_page.isMobileDevice']=isMobileDevice}catch(e){utag.DB(e)};try{ud['js_page.oewa.accountId']=oewa.accountId}catch(e){utag.DB(e)};try{ud['js_page.viewportWidth']=viewportWidth}catch(e){utag.DB(e)};try{ud['js_page.viewportHeight']=viewportHeight}catch(e){utag.DB(e)};try{ud['js_page.legacy.region']=legacy.region}catch(e){utag.DB(e)};try{ud['js_page.tgp.et']=tgp.et}catch(e){utag.DB(e)};try{ud['js_page.tgp.tracking_domain']=tgp.tracking_domain}catch(e){utag.DB(e)};try{ud['js_page.tgp.md_helper']=tgp.md_helper}catch(e){utag.DB(e)};try{ud['js_page.tgp.md']=tgp.md}catch(e){utag.DB(e)};try{ud['js_page.tgp.sc']=tgp.sc}catch(e){utag.DB(e)};try{ud['js_page.tif.transitionhelper']=tif.transitionhelper}catch(e){utag.DB(e)};try{ud['js_page.browserLanguage']=browserLanguage}catch(e){utag.DB(e)};try{ud['js_page.pageUrl']=pageUrl}catch(e){utag.DB(e)};try{ud['js_page.pageReferrer']=pageReferrer}catch(e){utag.DB(e)};try{ud['js_page.pageTitle']=pageTitle}catch(e){utag.DB(e)};try{ud['js_page.oewa']=oewa}catch(e){utag.DB(e)};try{ud['js_page.eventType']=eventType}catch(e){utag.DB(e)};try{ud['js_page.uaid']=uaid}catch(e){utag.DB(e)};try{ud['js_page.originalReferrer']=originalReferrer}catch(e){utag.DB(e)};try{ud['js_page.pageUrl2']=pageUrl2}catch(e){utag.DB(e)};try{ud['js_page.pageReferrer2']=pageReferrer2}catch(e){utag.DB(e)};try{ud['js_page.meta.content_name']=meta.content_name}catch(e){utag.DB(e)};try{ud['js_page.meta.event_name']=meta.event_name}catch(e){utag.DB(e)};try{ud['js_page.meta.event_time']=meta.event_time}catch(e){utag.DB(e)};try{ud['js_page.meta.fbAccessToken']=meta.fbAccessToken}catch(e){utag.DB(e)};try{ud['js_page.meta.fbc']=meta.fbc}catch(e){utag.DB(e)};try{ud['js_page.meta.fbGraphVersion']=meta.fbGraphVersion}catch(e){utag.DB(e)};try{ud['js_page.meta.fbp']=meta.fbp}catch(e){utag.DB(e)};try{ud['js_page.meta.fbPixelId']=meta.fbPixelId}catch(e){utag.DB(e)};try{ud['js_page.meta.random_number']=meta.random_number}catch(e){utag.DB(e)};try{ud['js_page.google_ads.conversion_id']=google_ads.conversion_id}catch(e){utag.DB(e)};try{ud['js_page.google_ads.currency']=google_ads.currency}catch(e){utag.DB(e)};try{ud['js_page.google_ads.event_name']=google_ads.event_name}catch(e){utag.DB(e)};try{ud['js_page.google_ads.event_value']=google_ads.event_value}catch(e){utag.DB(e)};try{ud['js_page.google_ads.tag_id']=google_ads.tag_id}catch(e){utag.DB(e)};try{ud['js_page.google_ads.label']=google_ads.label}catch(e){utag.DB(e)};try{ud['js_page.originalReferrerDecoded']=originalReferrerDecoded}catch(e){utag.DB(e)};try{ud['js_page.tiktok.event_name']=tiktok.event_name}catch(e){utag.DB(e)};try{ud['js_page.tiktok.pixel_code']=tiktok.pixel_code}catch(e){utag.DB(e)};try{ud['js_page.tiktok.timestamp']=tiktok.timestamp}catch(e){utag.DB(e)};try{ud['js_page.tiktok.content_name']=tiktok.content_name}catch(e){utag.DB(e)};try{ud['js_page.tiktok.access_token']=tiktok.access_token}catch(e){utag.DB(e)};try{ud['js_page.configuration.environment']=configuration.environment}catch(e){utag.DB(e)};try{ud['js_page.trackingGatewayDomain']=trackingGatewayDomain}catch(e){utag.DB(e)};try{ud['js_page.clientTimestamp']=clientTimestamp}catch(e){utag.DB(e)};try{ud['js_page.brain.name']=brain.name}catch(e){utag.DB(e)};};utag.loader.chkCanRunTime=function(s,e,d,t,o,i){try{o={is:[s,e],dt:[],tm:[],hd:0,ms:0};for(i=0;i<2;i++){d=o.is[i].substring(0,8);t=o.is[i].substring(8);o.dt[i]=new Date();if(d!=='--------'){o.dt[i].setFullYear(d.substring(0,4));o.dt[i].setMonth(parseInt(d.substring(4,6))-1);o.dt[i].setDate(d.substring(6,8));}if(t!=='----'){o.dt[i].setHours(t.substring(0,2));o.dt[i].setMinutes(t.substring(2,4));}else{o.dt[i].setHours(o.hd);o.dt[i].setMinutes(o.ms);}o.dt[i].setSeconds(o.ms);o.tm[i]=o.dt[i].getTime();o.hd=23;o.ms=59;}o.n=new Date().getTime();return(o.n>=o.tm[0]&&o.n<=o.tm[1]);}catch(e){return false;}};utag.loader.initdata=function(){try{utag.data=(typeof utag_data!='undefined')?utag_data:{};utag.udoname='utag_data';}catch(e){utag.data={};utag.DB('idf:'+e);}};utag.loader.loadrules=function(_pd,_pc){var d=_pd||utag.data;var c=_pc||utag.cond;for(var l in utag.loader.GV(c)){switch(l){case'119':try{c[119]|=(typeof d['cp._fbc']!='undefined'&&d['cp._fbc']!=''&&typeof d['cp._fbp']!='undefined'&&d['cp._fbp']!=''&&d['js_page.meta.content_name']!='donotfire'&&d['js_page.meta.event_name']!='donotfire'&&typeof d['js_page.meta.event_time']!='undefined'&&d['js_page.meta.event_time']!=''&&typeof d['js_page.meta.content_name']!='undefined'&&d['js_page.meta.content_name']!=''&&typeof d['js_page.meta.event_name']!='undefined'&&d['js_page.meta.event_name']!=''&&d['cp.uiconsent'].toString().indexOf('fullConsent')>-1)}catch(e){utag.DB(e)};break;case'120':try{c[120]|=(typeof d['businessEventType']=='undefined')}catch(e){utag.DB(e)};break;case'121':try{c[121]|=(typeof d['businessEventType']!='undefined'&&d['businessEventType']!='')}catch(e){utag.DB(e)};break;case'122':try{c[122]|=(typeof d['cp._gclid']!='undefined'&&d['cp._gclid']!=''&&d['js_page.google_ads.event_name'].toString().toLowerCase()=='page_view'.toLowerCase()&&typeof d['js_page.google_ads.tag_id']!='undefined'&&d['js_page.google_ads.tag_id']!=''&&d['cp.uiconsent'].toString().indexOf('fullConsent')>-1)}catch(e){utag.DB(e)};break;case'123':try{c[123]|=(typeof d['cp._gclid']!='undefined'&&d['cp._gclid']!=''&&typeof d['js_page.google_ads.tag_id']!='undefined'&&d['js_page.google_ads.tag_id']!=''&&d['cp.uiconsent'].toString().indexOf('fullConsent')>-1)}catch(e){utag.DB(e)};break;case'125':try{c[125]|=(d['js_page.pageUrl'].toString().toLowerCase().indexOf('gmx.'.toLowerCase())>-1&&d['js_page.pageUrl'].toString().toLowerCase().indexOf('upsell'.toLowerCase())>-1)}catch(e){utag.DB(e)};break;case'126':try{c[126]|=(d['targetUrl'].toString().toLowerCase().indexOf('registrierung.gmx.'.toLowerCase())>-1&&d['js_page.pageUrl'].toString().toLowerCase().indexOf('gmx.'.toLowerCase())>-1&&d['js_page.pageUrl'].toString().toLowerCase().indexOf('/mail/ga/'.toLowerCase())>-1&&d['businessEventType'].toString().toLowerCase()=='userAction'.toLowerCase())}catch(e){utag.DB(e)};break;case'127':try{c[127]|=(d['js_page.pageUrl'].toString().toLowerCase().indexOf('gmx.'.toLowerCase())>-1&&d['js_page.pageUrl'].toString().toLowerCase().indexOf('/mail/ga/'.toLowerCase())>-1)}catch(e){utag.DB(e)};break;case'128':try{c[128]|=(typeof d['cp._fbc']!='undefined'&&d['cp._fbc']!=''&&typeof d['cp._fbp']!='undefined'&&d['cp._fbp']!=''&&d['js_page.meta.content_name']!='donotfire'&&d['js_page.meta.event_name']!='donotfire'&&typeof d['js_page.meta.event_time']!='undefined'&&d['js_page.meta.event_time']!=''&&typeof d['js_page.meta.content_name']!='undefined'&&d['js_page.meta.content_name']!=''&&typeof d['js_page.meta.event_name']!='undefined'&&d['js_page.meta.event_name']!=''&&d['ut.profile']=='playground')}catch(e){utag.DB(e)};break;case'129':try{c[129]|=(typeof d['cp._gclid']!='undefined'&&d['cp._gclid']!=''&&d['js_page.google_ads.event_name'].toString().toLowerCase()=='page_view'.toLowerCase()&&typeof d['js_page.google_ads.tag_id']!='undefined'&&d['js_page.google_ads.tag_id']!=''&&d['ut.profile']=='playground')}catch(e){utag.DB(e)};break;case'130':try{c[130]|=(d['js_page.pageUrl'].toString().toLowerCase().indexOf('.gmx'.toLowerCase())>-1&&d['js_page.pageUrl'].toString().toLowerCase().indexOf('allnet'.toLowerCase())>-1&&d['pageType'].toString().toLowerCase()=='shoppingcart'.toLowerCase())}catch(e){utag.DB(e)};break;case'131':try{c[131]|=(d['js_page.pageUrl'].toString().toLowerCase().indexOf('gmx.'.toLowerCase())>-1&&d['js_page.pageUrl'].toString().toLowerCase().indexOf('allnet'.toLowerCase())>-1&&d['pageType'].toString().toLowerCase()=='customerdata'.toLowerCase())}catch(e){utag.DB(e)};break;case'132':try{c[132]|=(d['js_page.pageUrl'].toString().toLowerCase().indexOf('gmx.'.toLowerCase())>-1&&d['js_page.pageUrl'].toString().toLowerCase().indexOf('allnet'.toLowerCase())>-1&&d['pageType'].toString().toLowerCase()=='checkout'.toLowerCase())}catch(e){utag.DB(e)};break;case'133':try{c[133]|=(d['js_page.pageUrl'].toString().toLowerCase().indexOf('gmx.'.toLowerCase())>-1&&d['js_page.pageUrl'].toString().toLowerCase().indexOf('allnet'.toLowerCase())>-1&&d['pageType'].toString().toLowerCase()=='welcome'.toLowerCase())}catch(e){utag.DB(e)};break;case'27':try{c[27]|=(d['dom.domain']!='carddav.web.de'&&d['dom.domain']!='carddav.gmx.net')}catch(e){utag.DB(e)};break;case'52':try{c[52]|=(d['contentName']=='consent-management')}catch(e){utag.DB(e)};break;case'66':try{c[66]|=(d['permission_oewaTracking']=='1'&&d['js_page.siteDomain'].toString().toLowerCase()=='gmx.at'.toLowerCase())||(d['permission_oewaTracking']=='1'&&d['addressCountry'].toString().toLowerCase()=='AT'.toLowerCase())}catch(e){utag.DB(e)};break;case'73':try{c[73]|=(d['permission_tgp']=='1')}catch(e){utag.DB(e)};break;case'74':try{c[74]|=(d['ut.domain']=='gmx.net')||(d['ut.domain']=='gmx.at')||(d['ut.domain']=='gmx.ch')||(d['ut.domain']=='web.de')||(d['ut.domain']=='1und1.de')}catch(e){utag.DB(e)};break;}}};utag.pre=function(){utag.loader.initdata();utag.pagevars();try{utag.loader.RD(utag.data)}catch(e){utag.DB(e)};utag.loader.loadrules();};utag.loader.GET=function(){utag.cl={'_all_':1};utag.pre();utag.handler.extend=[function(a,b){try{if(1){sdx=document.domain.toString();sdy=sdx.split(".");sdz=(/\.co\.|\.com\.|\.org\.|\.edu\.|\.net\.|\.asn\.|\...\.jp$/.test(sdx))?3:2;b['js_page.siteDomain']=sdy.splice(sdy.length-sdz,sdz).join(".");}}catch(e){utag.DB(e)}},function(a,b){try{if(1){try{b['js_page.configuration.environment']=utag.cfg.path.split('/')[4]}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(1){try{b['js_page.clientTimestamp']=new Date().getTime()/1000;}catch(e){}}}catch(e){utag.DB(e);}},function(a,b,c,d,e,f,g){if(1){d=b['js_page.siteDomain'];if(typeof d=='undefined')return;c=[{'web.de':'//tgw.web.de/events'},{'gmx.net':'//tgw.gmx.net/events'},{'1und1.de':'//tgw.1und1.de/events'},{'mail.com':'//tgw.mail.com/events'},{'gmx.fr':'//tgw.gmx.fr/events'},{'gmx.es':'//tgw.gmx.es/events'},{'gmx.at':'//tgw.gmx.at/events'},{'gmx.ch':'//tgw.gmx.ch/events'},{'gmx.com':'//tgw.gmx.com/events'},{'gmx.co.uk':'//tgw.gmx.co.uk/events'},{'united-internet-media.de':'//tgw.united-internet-media.de/events'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.trackingGatewayDomain']=c[e][f];m=true};};if(m)break};if(!m)b['js_page.trackingGatewayDomain']='default';}},function(a,b){try{if(1){try{b['js_page.addOnBrand']=document.querySelectorAll("html[united-toolbar-brand]")[0].attributes["united-toolbar-brand"].value}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(1){try{b['js_page.addOnVersion']=document.querySelectorAll("html[united-toolbar-version]")[0].attributes["united-toolbar-version"].value}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(1){try{b['js_page.addOnVariant']=document.querySelectorAll("html[united-toolbar-variant]")[0].attributes["united-toolbar-variant"].value}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(b['ut.profile'].toString().indexOf('green')>-1||b['ut.profile'].toString().indexOf('beige')>-1){function stripQuery(url){return url.includes("q=")?url.substring(0,url.indexOf("q=")):url;}
utag_data.debugData=utag_data.debugData||{};if(utag_data["manualUrl"]){utag_data.debugData.pageUrl=stripQuery(utag_data["manualUrl"]);}else if(utag_data["dom.url"]){utag_data.debugData.pageUrl=stripQuery(utag_data["dom.url"]);}else{utag_data.debugData.pageUrl="unknown";}}}catch(e){utag.DB(e)}},function(a,b){try{if(b['ut.profile'].toString().indexOf('green')>-1||b['ut.profile'].toString().indexOf('beige')>-1){function stripQuery(url){return url.includes("q=")?url.substring(0,url.indexOf("q=")):url;}
utag_data.debugData=utag_data.debugData||{};if(utag_data["manualReferrer"]){utag_data.debugData.pageReferrer=stripQuery(utag_data["manualReferrer"]);}else if(utag_data["dom.referrer"]){utag_data.debugData.pageReferrer=stripQuery(utag_data["dom.referrer"]);}else{utag_data.debugData.pageReferrer="unknown";}}}catch(e){utag.DB(e)}},function(a,b,c,d,e,f,g){if(b['ut.domain'].toString().toLowerCase()=='gmx.at'.toLowerCase()||b['addressCountry'].toString().toLowerCase()=='AT'.toLowerCase()){d=b['agof'];if(typeof d=='undefined')return;c=[{'264':'RedCont/Homepage/Homepage/gmx.at'},{'735':'RedCont/Nachrichten/Nachrichtenueberblick'},{'280':'RedCont/Sonstiges/Sonstiges'},{'136':'RedCont/AutoUndMotor/AutoUndMotor'},{'117':'RedCont/KulturUndFreizeit/Sonstiges'},{'148':'RedCont/Lifestyle/Mode'},{'106':'RedCont/ComputerUndTechnik/Sonstiges'},{'121':'RedCont/Gesundheit/Sonstiges'},{'124':'RedCont/Reisen/Sonstiges'},{'147':'RedCont/Lifestyle/Sonstiges'},{'14':'RedCont/ComputerUndTechnik/Spiele'},{'241':'RedCont/Nachrichten/Nachrichtenueberblick'},{'257':'RedCont/Wetter/Wetterueberblick'},{'216':'RedCont/Wirtschaft/Wirtschaftsueberblick'},{'740':'RedCont/Sonstiges/Sonstiges'},{'149':'RedCont/Lifestyle/EssenUndTrinken'},{'218':'RedCont/Wirtschaft/Sonstiges'},{'161':'RedCont/Lifestyle/Sonstiges'},{'157':'RedCont/Gesundheit/LiebeUndPsychologie'},{'294':'Service/Verzeichnisse/Routenplaner'},{'169':'RedCont/Sport/Sonstiges'},{'192':'RedCont/Sport/Sonstiges'},{'184':'RedCont/Sport/Fussball'},{'201':'RedCont/Sport/Sonstiges'},{'156':'RedCont/Nachrichten/GesellschaftUndLeute'},{'159':'RedCont/KulturUndFreizeit/KulturUeberblick'},{'155':'RedCont/Lifestyle/Horoskope'},{'163':'RedCont/KulturUndFreizeit/Musik'},{'736':'RedCont/KulturUndFreizeit/FilmUndKino'},{'266':'RedCont/Wissenschaft/Sonstiges'},{'15':'Unterhaltung/Games'},{'17':'Unterhaltung/Games'},{'140':'Unterhaltung/Games/Erotik/ErotikUeberblick'},{'89':'Ecommerce/OnlineShop/Sonstiges'},{'40':'Service/Sonstiges/Sonstiges'},{'97':'Service/Messaging/Email'},{'97_L':'Service/Messaging/Email/Logout/gmx.at'},{'977':'Service/Messaging/Email'},{'24':'Service/Suchmaschinen/Sonstiges'},{'100':'Service/Messaging/SMSundMMS'},{'320':'Service/Messaging/Email'},{'350':'Service/Messaging/Email'},{'105':'Service/Messaging/Email'},{'15':'UnterhaltungGames/Games/Gewinnspiele'},{'98':'Service/Messaging/Ecards'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.infonline.cp']=c[e][f];m=true};};if(m)break};if(!m)b['js_page.infonline.cp']='';}},function(a,b){try{if(1){try{b['js_page.isMobileDevice']=/Mobi|Kindle|KFAPWI|Android/i.test(navigator.userAgent);}catch(e){}}}catch(e){utag.DB(e);}},function(a,b,c,d,e,f,g){if(b['js_page.isMobileDevice'].toString().indexOf('true')>-1){d=b['brand'];if(typeof d=='undefined')return;c=[{'gmx':'mobgmx'},{'webde':'mobwebde'},{'1and1':'mob1und1'},{'gmxnet':'mobgmx'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.infonline.st']=c[e][f];m=true};};if(m)break};}},function(a,b,c,d,e,f,g){if(b['js_page.isMobileDevice'].toString().indexOf('false')>-1){d=b['brand'];if(typeof d=='undefined')return;c=[{'gmx':'gmx'},{'webde':'webdessl'},{'1and1':'1und1'},{'gmxnet':'gmx'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.infonline.st']=c[e][f];m=true};};if(m)break};}},function(a,b,c,d,e,f,g){if(1){d=b['js_page.siteDomain'];if(typeof d=='undefined')return;c=[{'gmx.net':'at_w_netgmx'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.oewa.accountId']=c[e][f];m=true};};if(m)break};if(!m)b['js_page.oewa.accountId']='at_w_atgmx';}},function(a,b){try{if(typeof b['viewportWidth']!='undefined'){b['js_page.viewportWidth']=b['viewportWidth']}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['viewportHeight']!='undefined'){b['js_page.viewportHeight']=b['viewportHeight']}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['viewportWidth']=='undefined'){b['js_page.viewportWidth']=b['dom.viewport_width']}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['viewportHeight']=='undefined'){b['js_page.viewportHeight']=b['dom.viewport_height']}}catch(e){utag.DB(e);}},function(a,b,c,d,e,f,g){if(1){d=b['ut.domain'];if(typeof d=='undefined')return;c=[{'web.de':'de'},{'gmx.net':'de'},{'gmx.at':'at'},{'gmx.ch':'ch'},{'1und1.de':'de'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.legacy.region']=c[e][f];m=true};};if(m)break};}},function(a,b,c,d,e,f,g){if(1){d=b['js_page.configuration.environment'];if(typeof d=='undefined')return;c=[{'dev':'XP'},{'qa':'XP'},{'prod':'CP'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.tgp.et']=c[e][f];m=true};};if(m)break};}},function(a,b,c,d,e,f,g){if(1){d=b['js_page.siteDomain'];if(typeof d=='undefined')return;c=[{'gmx.net':'ymprove.gmx.net'},{'gmx.at':'ymprove.gmx.at'},{'web.de':'ymprove.web.de'},{'1und1.de':'ymprove.1und1.de'},{'gmx.ch':'ymprove.gmx.ch'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.tgp.tracking_domain']=c[e][f];m=true};};if(m)break};if(!m)b['js_page.tgp.tracking_domain']='t.uimserv.net';}},function(a,b,c,d){try{if(1){c=[b['brand'],b['js_page.legacy.region']];b['js_page.tgp.md_helper']=c.join('_')}}catch(e){utag.DB(e);}},function(a,b,c,d,e,f,g){if(1){d=b['js_page.tgp.md_helper'];if(typeof d=='undefined')return;c=[{'webde_de':'webde'},{'gmx_at':'gmx_at'},{'gmx_ch':'gmx_ch'},{'gmx_de':'gmx'},{'1and1_de':'1und1'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.tgp.md']=c[e][f];m=true};};if(m)break};}},function(a,b,c,d){try{if(1){c=[b['applicationArea'],b['contentName']];b['js_page.tgp.sc']=c.join('.')}}catch(e){utag.DB(e);}},function(a,b,c,d){try{if(1){c=[b['applicationArea'],b['softwareVersion']];b['js_page.tif.transitionhelper']=c.join('_')}}catch(e){utag.DB(e);}},function(a,b){try{if(1){try{b['js_page.browserLanguage']=navigator.language||navigator.userLanguage;}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(b['softwareName'].toString().toLowerCase()!='search'.toLowerCase()){b['js_page.pageTitle']=b['dom.title']}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.url'].toString().indexOf('q=')>-1){try{b['js_page.pageUrl']=location.href.substring(0,location.href.indexOf("q="));}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.url'].toString().indexOf('q=')<0){b['js_page.pageUrl']=b['dom.url']}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.referrer'].toString().indexOf('q=')>-1){try{b['js_page.pageReferrer']=document.referrer.substring(0,document.referrer.indexOf("q="));}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.referrer'].toString().indexOf('q=')<0){b['js_page.pageReferrer']=b['dom.referrer']}}catch(e){utag.DB(e);}},function(a,b,c,d,e,f,g){if(b['ut.domain'].toString().toLowerCase()=='gmx.at'.toLowerCase()||b['addressCountry'].toString().toLowerCase()=='AT'.toLowerCase()){d=b['agof'];if(typeof d=='undefined')return;c=[{'264':'RedCont/Homepage/Homepage/gmx.at'},{'735':'RedCont/Nachrichten/Nachrichtenueberblick'},{'280':'RedCont/Sonstiges/Sonstiges'},{'136':'RedCont/AutoUndMotor/AutoUndMotor'},{'117':'RedCont/KulturUndFreizeit/Sonstiges'},{'148':'RedCont/Lifestyle/Mode'},{'106':'RedCont/ComputerUndTechnik/Sonstiges'},{'121':'RedCont/Gesundheit/Sonstiges'},{'124':'RedCont/Reisen/Sonstiges'},{'147':'RedCont/Lifestyle/Sonstiges'},{'14':'RedCont/ComputerUndTechnik/Spiele'},{'241':'RedCont/Nachrichten/Nachrichtenueberblick'},{'257':'RedCont/Wetter/Wetterueberblick'},{'216':'RedCont/Wirtschaft/Wirtschaftsueberblick'},{'740':'RedCont/Sonstiges/Sonstiges'},{'149':'RedCont/Lifestyle/EssenUndTrinken'},{'218':'RedCont/Wirtschaft/Sonstiges'},{'161':'RedCont/Lifestyle/Sonstiges'},{'157':'RedCont/Gesundheit/LiebeUndPsychologie'},{'294':'Service/Verzeichnisse/Routenplaner'},{'169':'RedCont/Sport/Sonstiges'},{'192':'RedCont/Sport/Sonstiges'},{'184':'RedCont/Sport/Fussball'},{'201':'RedCont/Sport/Sonstiges'},{'156':'RedCont/Nachrichten/GesellschaftUndLeute'},{'159':'RedCont/KulturUndFreizeit/KulturUeberblick'},{'155':'RedCont/Lifestyle/Horoskope'},{'163':'RedCont/KulturUndFreizeit/Musik'},{'736':'RedCont/KulturUndFreizeit/FilmUndKino'},{'266':'RedCont/Wissenschaft/Sonstiges'},{'15':'Unterhaltung/Games'},{'17':'Unterhaltung/Games'},{'140':'Unterhaltung/Games/Erotik/ErotikUeberblick'},{'89':'Ecommerce/OnlineShop/Sonstiges'},{'40':'Service/Sonstiges/Sonstiges'},{'97':'Service/Messaging/Email'},{'97_L':'Service/Messaging/Email/Logout/gmx.at'},{'977':'Service/Messaging/Email'},{'24':'Service/Suchmaschinen/Sonstiges'},{'100':'Service/Messaging/SMSundMMS'},{'320':'Service/Messaging/Email'},{'350':'Service/Messaging/Email'},{'105':'Service/Messaging/Email'},{'15':'UnterhaltungGames/Games/Gewinnspiele'},{'98':'Service/Messaging/Ecards'}];var m=false;for(e=0;e<c.length;e++){for(f in utag.loader.GV(c[e])){if(d==f){b['js_page.oewa']=c[e][f];m=true};};if(m)break};if(!m)b['js_page.oewa']='';}},function(a,b){try{if(typeof b['eventType']!='undefined'){b['js_page.eventType']=b['eventType']}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['eventType']=='undefined'){b['js_page.eventType']='load'}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['cp.ua_id']=='undefined'){try{b['js_page.uaid']=(crypto.randomUUID?crypto.randomUUID():'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g,c=>(c==='x'?(Math.random()*16|0).toString(16):(Math.random()*4+8|0).toString(16))));}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if((typeof b['cp.ua_id']=='undefined'&&typeof b['js_page.uaid']!='undefined')){if(typeof b['cp.ua_id']=='undefined'){document.cookie="ua_id="+b['js_page.uaid']+";path=/;domain="+utag.cfg.domain+";expires=";b['cp.ua_id']=b['js_page.uaid'];}}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.url'].toString().indexOf('consent-management')>-1){(function storeReferrer(){try{const referrer=document.referrer;sessionStorage.setItem('originalReferrer',referrer);}catch(error){console.error('Error storing referrer:',error);}})();}}catch(e){utag.DB(e)}},function(a,b){try{if((typeof b['ss.originalReferrer']!='undefined'&&b['dom.referrer'].toString().indexOf('consent-management')>-1)){b['js_page.originalReferrer']=b['ss.originalReferrer']}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['originalReferrer']!='undefined'){b['js_page.originalReferrer']=b['originalReferrer']}}catch(e){utag.DB(e);}},function(a,b){try{if(b['ut.profile'].toString().indexOf('scarlet')>-1){var uuidv7=function generateUUIDv7(){const cryptoObj=typeof crypto!=='undefined'&&crypto.getRandomValues?crypto:(typeof require!=='undefined'?require('crypto').webcrypto:null);if(!cryptoObj||!cryptoObj.getRandomValues){throw new Error('Secure random number generator is not available.');}
const timestamp=BigInt(Date.now());if(timestamp>0xFFFFFFFFFFFFn){throw new Error('Timestamp exceeds 48 bits. UUIDv7 not supported beyond year ~10889.');}
const timestampHex=timestamp.toString(16).padStart(12,'0');const randBytes=new Uint8Array(10);cryptoObj.getRandomValues(randBytes);randBytes[2]=(randBytes[2]&0b00111111)|0b10000000;const versionByte=((parseInt(timestampHex.slice(8,10),16)&0x0F)|0x70)
.toString(16)
.padStart(2,'0');const uuid=[timestampHex.slice(0,8),timestampHex.slice(8,12),versionByte+timestampHex.slice(10,12),Array.from(randBytes.slice(0,2)).map(b=>b.toString(16).padStart(2,'0')).join(''),Array.from(randBytes.slice(2)).map(b=>b.toString(16).padStart(2,'0')).join('')];return uuid.join('-');};utag_data.debugData=utag_data.debugData||{};utag_data.debugData.uuidv7=uuidv7();}}catch(e){utag.DB(e)}},function(a,b){try{if(b['dom.url'].toString().indexOf('q=')>-1){try{b['js_page.pageUrl2']=location.href.substring(0,location.href.indexOf("q="));}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.url'].toString().indexOf('q=')<0){b['js_page.pageUrl2']=b['dom.url']}}catch(e){utag.DB(e);}},function(a,b){try{if((b['manualUrl'].toString().indexOf('q=')>-1&&typeof b['manualUrl']!='undefined'&&b['manualUrl']!='')){try{b['js_page.pageUrl2']=utag_data.manualUrl.substring(0,utag_data.manualUrl.indexOf("q="));}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if((b['manualUrl'].toString().indexOf('q=')<0&&typeof b['manualUrl']!='undefined'&&b['manualUrl']!='')){b['js_page.pageUrl2']=b['manualUrl']}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.referrer'].toString().indexOf('q=')>-1){try{b['js_page.pageReferrer2']=document.referrer.substring(0,document.referrer.indexOf("q="));}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.referrer'].toString().indexOf('q=')<0){b['js_page.pageReferrer2']=b['dom.referrer']}}catch(e){utag.DB(e);}},function(a,b){try{if((b['manualReferrer'].toString().indexOf('q=')>-1&&typeof b['manualReferrer']!='undefined'&&b['manualReferrer']!='')){try{b['js_page.pageReferrer2']=utag_data.manualReferrer.substring(0,utag_data.manualReferrer.indexOf("q="));}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if((b['manualReferrer'].toString().indexOf('q=')<0&&typeof b['manualReferrer']!='undefined'&&b['manualReferrer']!='')){b['js_page.pageReferrer2']=b['manualReferrer']}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.google_ads.event_value']='0'}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.meta.event_name']='donotfire';b['js_page.meta.content_name']='donotfire'}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['businessEventType']=='undefined'||typeof b['businessEventType']!='undefined'&&b['businessEventType']==''){b['js_page.meta.event_name']='ViewContent'}}catch(e){utag.DB(e);}},function(a,b){try{if((b['businessEventType'].toString().toLowerCase()=='userAction'.toLowerCase()&&typeof b['targetUrl']!='undefined'&&b['targetUrl']!='')){b['js_page.meta.event_name']='ClickOut'}}catch(e){utag.DB(e);}},function(a,b){try{if((b['businessEventType'].toString().toLowerCase()=='technical.event'.toLowerCase()&&b['componentPath'].toString().toLowerCase()=='registration-success'.toLowerCase())){b['js_page.meta.event_name']='Lead'}}catch(e){utag.DB(e);}},function(a,b){try{if((b['businessEventType'].toString().toLowerCase()=='userAction'.toLowerCase()&&b['targetUrl'].toString().indexOf('apps.apple.com')>-1)){b['js_page.meta.content_name']='appstore'}}catch(e){utag.DB(e);}},function(a,b){try{if((b['businessEventType'].toString().toLowerCase()=='userAction'.toLowerCase()&&b['targetUrl'].toString().indexOf('play.google.com')>-1)){b['js_page.meta.content_name']='playstore'}}catch(e){utag.DB(e);}},function(a,b){try{if((b['businessEventType'].toString().toLowerCase()=='userAction'.toLowerCase()&&b['targetUrl'].toString().indexOf('registrierung.gmx.')>-1)||(b['businessEventType'].toString().toLowerCase()=='userAction'.toLowerCase()&&b['targetUrl'].toString().indexOf('registrierung.web.de')>-1)){b['js_page.meta.content_name']='registration'}}catch(e){utag.DB(e);}},function(a,b){try{if(1){try{b['js_page.meta.event_time']=Math.floor(new Date().getTime())}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if((b['js_page.pageUrl'].toString().indexOf('/mail/socialmedia')>-1&&typeof b['businessEventType']=='undefined')){b['js_page.meta.content_name']='lp_insta'}}catch(e){utag.DB(e);}},function(a,b){try{if((b['businessEventType'].toString().toLowerCase()=='technical.event'.toLowerCase()&&b['componentPath'].toString().toLowerCase()=='registration-success'.toLowerCase())){b['js_page.meta.content_name']='Lead'}}catch(e){utag.DB(e);}},function(a,b){try{if((b['js_page.pageUrl'].toString().indexOf('playground.html')>-1&&typeof b['businessEventType']=='undefined')){b['js_page.meta.content_name']='lp_insta'}}catch(e){utag.DB(e);}},function(a,b){try{if((b['contentName'].toString().toLowerCase()=='welcome_interception'.toLowerCase()&&typeof b['businessEventType']=='undefined')||(b['js_page.pageUrl'].toString().toLowerCase()=='welcome_interception'.toLowerCase()&&typeof b['businessEventType']=='undefined')){b['js_page.meta.content_name']='welcome_interception'}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.url'].toString().indexOf('q=')>-1){try{b['js_page.pageUrl']=location.href.substring(0,location.href.indexOf("q="));}catch(e){}}}catch(e){utag.DB(e);}},function(a,b){try{if(b['dom.url'].toString().indexOf('q=')<0){b['js_page.pageUrl']=b['dom.url']}}catch(e){utag.DB(e);}},function(a,b){try{if(1){try{b['js_page.meta.random_number']=Math.floor(1000000000+Math.random()*9000000000)}catch(e){}}}catch(e){utag.DB(e);}},function(a,b,c,d){try{if(1){c=['fb','1',b['js_page.meta.event_time'],b['qp.fbclid']];b['js_page.meta.fbc']=c.join('.')}}catch(e){utag.DB(e);}},function(a,b,c,d){try{if(1){c=['fb','1',b['js_page.meta.event_time'],b['js_page.meta.random_number']];b['js_page.meta.fbp']=c.join('.')}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['qp.fbclid']!='undefined'&&b['qp.fbclid']!=''){document.cookie="_fbp="+b['js_page.meta.fbp']+";path=/;domain="+utag.cfg.domain+";expires="+(function(){var d=new Date();d.setTime(d.getTime()+(90*864e5));return d.toGMTString();})()+"";b['cp._fbp']=b['js_page.meta.fbp'];}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['qp.fbclid']!='undefined'&&b['qp.fbclid']!=''){document.cookie="_fbc="+b['js_page.meta.fbc']+";path=/;domain="+utag.cfg.domain+";expires="+(function(){var d=new Date();d.setTime(d.getTime()+(90*864e5));return d.toGMTString();})()+"";b['cp._fbc']=b['js_page.meta.fbc'];}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.meta.fbAccessToken']='EAANXD9BSSdEBO7X2WA1amzn0eVIDjteDwfbkmpXODsTneDxujhDnaxv104duCdgnkTtqZBG8RjeaI4HA6eBZAOnLhsJht4o7W7uhZAC1DJLIj7xMZAbrOVsEOuBhnYDT4n1d7d0jW5uJ5KpVCCicW77SN9LgfZA9hvpZA4BZA0MTY558zURmNsHfLyNaZCaSHKeIiwZDZD'}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.meta.fbPixelId']='2798336453678996'}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.meta.fbGraphVersion']='v21.0'}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['qp.gclid']!='undefined'&&b['qp.gclid']!=''){document.cookie="_gclid="+b['qp.gclid']+";path=/;domain="+utag.cfg.domain+";expires="+(function(){var d=new Date();d.setTime(d.getTime()+(90*864e5));return d.toGMTString();})()+"";b['cp._gclid']=b['qp.gclid'];}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.google_ads.tag_id']='926741968'}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.google_ads.currency']='EUR'}}catch(e){utag.DB(e);}},function(a,b){try{if(typeof b['businessEventType']=='undefined'){b['js_page.google_ads.event_name']='page_view';}}catch(e){utag.DB(e)}},function(a,b){try{if(b['cp._gclid']=='12345'){(function(){var ref=(document.referrer||"").trim();var isInternal=false;function getBaseDomain(){var hostParts=location.hostname.split('.');if(hostParts.length>=3){var tld=hostParts.slice(-2).join('.');var compoundTLDs=["co.uk"];if(compoundTLDs.includes(tld)&&hostParts.length>=3){return"."+hostParts.slice(-3).join('.');}
return"."+tld;}else if(hostParts.length===2){return"."+hostParts.join('.');}else{return location.hostname;}}
let refHost="";try{refHost=new URL(ref).hostname;}catch(e){refHost="";}
const allowedDomains=["web.de","mail.com","gmx.net","gmx.at","gmx.ch","gmx.de","gmx.fr","gmx.es","gmx.co.uk"];isInternal=allowedDomains.some(domain=>refHost===domain||refHost.endsWith("."+domain));if(!isInternal&&ref&&ref!=="about:blank"){var currentCookie=utag.loader.RC("originalReferrer2");var currentDecoded=currentCookie?decodeURIComponent(currentCookie):"";if(!currentCookie||currentDecoded!==ref){var domainStr=";path=/;domain="+getBaseDomain();utag.loader.SC("originalReferrer2",encodeURIComponent(ref),domainStr);}}})();}}catch(e){utag.DB(e)}},function(a,b){try{if(b['cp._gclid']=='12345'){(function(){function getSessionReferrer(){try{var val=sessionStorage.getItem("originalReferrer");return(typeof val==="string"&&val.trim()!=="")?val.trim():null;}catch(e){return null;}}
function getCookieReferrer(){try{var raw=utag.loader.RC("originalReferrer2");if(typeof raw==="string"&&raw.trim()!==""){return decodeURIComponent(raw.trim());}}catch(e){return raw?raw.trim():null;}
return null;}
var referrer=getSessionReferrer()||getCookieReferrer();if(referrer){window.utag_data=window.utag_data||{};window.utag_data.js_page=window.utag_data.js_page||{};window.utag_data["js_page.originalReferrerDecoded"]=referrer;}})();}}catch(e){utag.DB(e)}},function(a,b){try{if(typeof b['businessEventType']=='undefined'||typeof b['businessEventType']!='undefined'&&b['businessEventType']==''){b['js_page.tiktok.event_name']='ViewContent'}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.tiktok.timestamp']=b['tealium_timestamp_local']}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.tiktok.access_token']='1f596d5faff263f1bc12688c58c44406a0bcbf70'}}catch(e){utag.DB(e);}},function(a,b){try{if(1){b['js_page.tiktok.pixel_code']='D18I71RC77U61SUDCMQG'}}catch(e){utag.DB(e);}},function(a,b,c,d){try{if(1){c=[b['cp.ua_id'],b['cp.utag_main_ses_id']];b['js_page.visitId']=c.join('@')}}catch(e){utag.DB(e);}}];utag.handler.cfg_extend=[{"id":"141","alr":0,"blr":1,"end":0,"bwq":0},{"end":0,"id":"140","blr":1,"alr":0,"bwq":0},{"end":0,"id":"4","blr":1,"alr":0,"bwq":0},{"bwq":0,"id":"3","alr":0,"blr":1,"end":0},{"bwq":0,"alr":0,"blr":1,"id":"26","end":0},{"alr":0,"blr":1,"id":"27","end":0,"bwq":0},{"id":"28","alr":0,"blr":1,"end":0,"bwq":0},{"bwq":0,"id":"303","alr":0,"blr":1,"end":0},{"end":0,"id":"304","blr":1,"alr":0,"bwq":0},{"bwq":0,"id":"68","alr":0,"blr":1,"end":0},{"bwq":0,"id":"72","alr":0,"blr":1,"end":0},{"end":0,"blr":1,"alr":0,"id":"73","bwq":0},{"id":"70","alr":0,"blr":1,"end":0,"bwq":0},{"end":0,"blr":1,"alr":0,"id":"79","bwq":0},{"id":"80","alr":0,"blr":1,"end":0,"bwq":0},{"end":0,"id":"81","blr":1,"alr":0,"bwq":0},{"bwq":0,"alr":0,"blr":1,"id":"82","end":0},{"bwq":0,"end":0,"id":"83","blr":1,"alr":0},{"end":0,"id":"93","blr":1,"alr":0,"bwq":0},{"bwq":0,"id":"94","alr":0,"blr":1,"end":0},{"bwq":0,"alr":0,"blr":1,"id":"95","end":0},{"bwq":0,"end":0,"id":"96","blr":1,"alr":0},{"bwq":0,"alr":0,"blr":1,"id":"97","end":0},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"98"},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"99"},{"alr":0,"blr":1,"id":"103","end":0,"bwq":0},{"bwq":0,"end":0,"id":"105","blr":1,"alr":0},{"end":0,"id":"106","blr":1,"alr":0,"bwq":0},{"bwq":0,"alr":0,"blr":1,"id":"107","end":0},{"id":"108","alr":0,"blr":1,"end":0,"bwq":0},{"end":0,"blr":1,"alr":0,"id":"109","bwq":0},{"alr":0,"blr":1,"id":"145","end":0,"bwq":0},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"185"},{"alr":0,"blr":1,"id":"186","end":0,"bwq":0},{"id":"187","alr":0,"blr":1,"end":0,"bwq":0},{"end":0,"id":"207","blr":1,"alr":0,"bwq":0},{"end":0,"blr":1,"alr":0,"id":"208","bwq":0},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"209"},{"bwq":0,"end":0,"id":"210","blr":1,"alr":0},{"end":0,"blr":1,"alr":0,"id":"294","bwq":0},{"id":"296","alr":0,"blr":1,"end":0,"bwq":0},{"end":0,"id":"295","blr":1,"alr":0,"bwq":0},{"end":0,"blr":1,"alr":0,"id":"297","bwq":0},{"alr":0,"blr":1,"id":"299","end":0,"bwq":0},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"301"},{"alr":0,"blr":1,"id":"302","end":0,"bwq":0},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"298"},{"alr":0,"blr":1,"id":"300","end":0,"bwq":0},{"id":"340","alr":0,"blr":1,"end":0,"bwq":0},{"bwq":0,"alr":0,"blr":1,"id":"343","end":0},{"id":"344","alr":0,"blr":1,"end":0,"bwq":0},{"end":0,"blr":1,"alr":0,"id":"345","bwq":0},{"id":"346","alr":0,"blr":1,"end":0,"bwq":0},{"bwq":0,"alr":0,"blr":1,"id":"348","end":0},{"id":"349","alr":0,"blr":1,"end":0,"bwq":0},{"end":0,"id":"350","blr":1,"alr":0,"bwq":0},{"end":0,"id":"351","blr":1,"alr":0,"bwq":0},{"bwq":0,"id":"352","alr":0,"blr":1,"end":0},{"bwq":0,"alr":0,"blr":1,"id":"353","end":0},{"id":"354","alr":0,"blr":1,"end":0,"bwq":0},{"end":0,"blr":1,"alr":0,"id":"355","bwq":0},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"356"},{"bwq":0,"end":0,"id":"357","blr":1,"alr":0},{"bwq":0,"id":"358","alr":0,"blr":1,"end":0},{"bwq":0,"alr":0,"blr":1,"id":"359","end":0},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"360"},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"361"},{"alr":0,"blr":1,"id":"362","end":0,"bwq":0},{"bwq":0,"end":0,"id":"363","blr":1,"alr":0},{"end":0,"id":"364","blr":1,"alr":0,"bwq":0},{"bwq":0,"alr":0,"blr":1,"id":"365","end":0},{"alr":0,"blr":1,"id":"368","end":0,"bwq":0},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"369"},{"alr":0,"blr":1,"id":"370","end":0,"bwq":0},{"bwq":0,"end":0,"id":"374","blr":1,"alr":0},{"bwq":0,"id":"377","alr":0,"blr":1,"end":0},{"end":0,"id":"378","blr":1,"alr":0,"bwq":0},{"bwq":0,"id":"380","alr":0,"blr":1,"end":0},{"bwq":0,"alr":0,"blr":1,"id":"383","end":0},{"bwq":0,"end":0,"blr":1,"alr":0,"id":"384"},{"bwq":0,"end":0,"id":"385","blr":1,"alr":0},{"end":0,"blr":0,"alr":1,"id":"67","bwq":0}];if(utag.gdpr){var consentEnabled=false;var preferencesEnabled=false;var doNotSellEnabled=false;utag.gdpr.doNotSell=utag.gdpr.doNotSell||{};utag.gdpr.preferences_prompt=utag.gdpr.preferences_prompt||{};utag.gdpr.consent_prompt=utag.gdpr.consent_prompt||{};utag.gdpr.applyConsentState=function(){var enforcementMode=utag.gdpr.getEnforcementMode();if(enforcementMode==='none')return;utag.DB('Consent Manager: Applying consent');try{var i,lc=utag.loader.cfg,cs=utag.gdpr.getConsentState(),ot=utag.gdpr.omittedTags||{};if(typeof cs==='number'){if((utag.gdpr.consent_prompt.isEnabled&&parseInt(cs)!==1)||((!consentEnabled&&preferencesEnabled&&enforcementMode==='opt-in')&&(parseInt(cs)===-1||parseInt(cs)===0))){utag.DB('Consent Manager: Setting all tags to off');for(i in utag.loader.GV(lc)){if(typeof ot[i]==='undefined'){lc[i].load=0;}}}}else if(((utag.gdpr.consent_prompt.isEnabled||utag.gdpr.preferences_prompt.isEnabled)||(!consentEnabled&&preferencesEnabled))&&enforcementMode==='opt-in'){utag.DB('Consent Manager: Partial Consent');for(i in utag.loader.GV(lc)){if(typeof ot[i]==='undefined'){if(lc[i].tcat>0&&cs[lc[i].tcat-1].ct!='1'){lc[i].load=0;}}}}var btl=utag.gdpr.dns?utag.gdpr.dns.getBlockedDnsTagLookup():null;utag.DB('Consent Manager: Do Not Sell Tags');if(enforcementMode==='opt-out'&&btl){for(i in utag.loader.GV(lc)){if(parseInt(btl[i])===1){lc[i].load=0;}}}try{if(window.tealiumConsentRegister&&window.tealiumConsentRegister.currentDecision===null){var cookieValues=utag.gdpr.getCookieValues();var hasDnsCookie=typeof cookieValues.dns==='string';var hasConsentCookie=typeof cookieValues.consent==='string';var decisionType=(enforcementMode==='opt-in'&&hasConsentCookie)||(enforcementMode==='opt-out'&&hasDnsCookie)?'explicit':'implicit';var decision=(decisionType==='implicit'&&enforcementMode==='opt-in')?[]:utag.gdpr.getSelectedCategories();decision.unshift('always_on');decision.type=decisionType;window.tealiumConsentRegister.addConsentDecision(decision);}}catch(e){utag.DB(e);}}catch(e){utag.DB(e);}};utag.gdpr.promptEnabledSetting=function(){if(!utag.gdpr.dr&&(utag.cfg.readywait||utag.cfg.waittimer)){utag.gdpr.dr=1;return;}utag.gdpr.consent_prompt.wasInitiallyEnabled=consentEnabled;utag.gdpr.preferences_prompt.wasInitiallyEnabled=preferencesEnabled;utag.gdpr.doNotSell.wasInitiallyEnabled=doNotSellEnabled;if(consentEnabled===true&&!(1)){utag.gdpr.consent_prompt.isEnabled=false;}if(preferencesEnabled===true&&(consentEnabled===true&&!(1))){utag.gdpr.preferences_prompt.isEnabled=false;}if(doNotSellEnabled===true&&!(1)){utag.gdpr.doNotSell.isEnabled=false;}if(preferencesEnabled===true&&consentEnabled===false&&!(1)){utag.gdpr.preferences_prompt.isEnabled=true;}};var splitGdprModules=false;if(typeof utag.gdpr.getEnforcementMode!=='function'){splitGdprModules=true;}utag.gdpr.getEnforcementMode=function(){utag.gdpr.promptEnabledSetting();var optInModulesAreActive=(utag.gdpr.consent_prompt&&utag.gdpr.consent_prompt.isEnabled===true);var optOutModuleIsActive=(utag.gdpr.doNotSell&&utag.gdpr.doNotSell.isEnabled===true);var optInPreferencesOnly=(!optInModulesAreActive&&!utag.gdpr.consent_prompt.wasInitiallyEnabled&&utag.gdpr.preferences_prompt.wasInitiallyEnabled&&!optOutModuleIsActive)||(splitGdprModules&&utag.gdpr.preferences_prompt&&utag.gdpr.preferences_prompt.wasInitiallyEnabled);var enforcementMode='opt-in';if(optOutModuleIsActive&&!optInModulesAreActive)enforcementMode='opt-out';if(!optOutModuleIsActive&&optInPreferencesOnly)enforcementMode='opt-in';if(!optOutModuleIsActive&&!optInModulesAreActive&&!optInPreferencesOnly)enforcementMode='none';return enforcementMode;};}utag.loader.initcfg=function(){utag.loader.cfg={"22":{load:((utag.cond[27])),send:1,v:202507290808,wait:0,tid:20010},"99":{load:(((utag.cond[119])&&(utag.cond[121]))),send:1,v:202507231002,wait:0,tid:20010},"21":{load:((utag.cond[27])),send:1,v:202507290808,wait:1,tid:20010},"50":{load:(((utag.cond[66])&&!(utag.cond[52]))),send:1,v:202412030809,wait:1,tid:20010},"56":{load:(((utag.cond[74])&&(utag.cond[73]))),send:1,v:202402211030,wait:1,tid:20067},"98":{load:(((utag.cond[119]||utag.cond[128])&&(utag.cond[120]))),send:1,v:202507231002,wait:1,tid:20010},"100":{load:((utag.cond[122]||utag.cond[129])),send:1,v:202508060530,wait:1,tid:20010},"102":{load:(((utag.cond[123])&&(utag.cond[125]))),send:1,v:202508060530,wait:1,tid:20010},"103":{load:(((utag.cond[123])&&(utag.cond[126]))),send:1,v:202508060530,wait:1,tid:20010},"104":{load:(((utag.cond[123])&&(utag.cond[127]))),send:1,v:202508060530,wait:1,tid:20010},"105":{load:(((utag.cond[125])&&(utag.cond[123]))),send:1,v:202508060530,wait:1,tid:20010},"106":{load:(((utag.cond[123])&&(utag.cond[126]))),send:1,v:202508060530,wait:1,tid:20010},"107":{load:(((utag.cond[123])&&(utag.cond[127]))),send:1,v:202508060530,wait:1,tid:20010},"109":{load:(((utag.cond[122])&&(utag.cond[130]))),send:1,v:202508060530,wait:1,tid:20010},"110":{load:(((utag.cond[122])&&(utag.cond[131]))),send:1,v:202508060530,wait:1,tid:20010},"111":{load:(((utag.cond[122])&&(utag.cond[132]))),send:1,v:202508060530,wait:1,tid:20010},"112":{load:(((utag.cond[122])&&(utag.cond[133]))),send:1,v:202508060530,wait:1,tid:20010}};utag.loader.cfgsort=["22","99","21","50","56","98","100","102","103","104","105","106","107","109","110","111","112"];if(utag.gdpr&&utag.gdpr.getEnforcementMode()==='opt-in'){Object.keys(utag.loader.cfg).forEach(function(tagId){if(utag.loader.cfg[tagId].tcat===16){utag.DB('Consent Manager: Removing uncategorized tag from utag.loader.cfg in opt-in mode: '+tagId);delete utag.loader.cfg[tagId];utag.loader.cfgsort=utag.loader.cfgsort.filter(function(id){return id!==tagId;});}})}}
utag.loader.initcfg();}
if(typeof utag_cfg_ovrd!='undefined'){for(utag._i in utag.loader.GV(utag_cfg_ovrd))utag.cfg[utag._i]=utag_cfg_ovrd[utag._i]};utag.loader.PINIT=function(a,b,c){utag.DB("Pre-INIT");if(utag.cfg.noload){return;}
try{this.GET();if(utag.handler.RE('view',utag.data,"blr")){utag.handler.LR(utag.data);}}catch(e){utag.DB(e)};a=this.cfg;c=0;for(b in this.GV(a)){if(a[b].block==1||(a[b].load>0&&(typeof a[b].src!='undefined'&&a[b].src!=''))){a[b].block=1;c=1;this.bq[b]=1;}}
if(c==1){for(b in this.GV(a)){if(a[b].block){a[b].id=b;if(a[b].load==4)a[b].load=1;a[b].cb=function(){var d=this.uid;utag.loader.cfg[d].cbf=1;utag.loader.LOAD(d)};this.AS(a[b]);}}}
if(c==0)this.INIT();};utag.loader.INIT=function(a,b,c,d,e){utag.DB('utag.loader.INIT');if(this.ol==1)return-1;else this.ol=1;if(utag.cfg.noview!=true)utag.handler.RE('view',utag.data,"alr");utag.rpt.ts['i']=new Date();d=this.cfgsort;for(a=0;a<d.length;a++){e=d[a];b=this.cfg[e];b.id=e;if(b.block!=1){if(utag.loader.bk[b.id]||((utag.cfg.readywait||utag.cfg.noview)&&b.load==4)){this.f[b.id]=0;utag.loader.LOAD(b.id)}else if(b.wait==1&&utag.loader.rf==0){utag.DB('utag.loader.INIT: waiting '+b.id);this.wq.push(b)
this.f[b.id]=2;}else if(b.load>0){utag.DB('utag.loader.INIT: loading '+b.id);this.lq.push(b);this.AS(b);}}}
if(this.wq.length>0)utag.loader.EV('','ready',function(a){if(utag.loader.rf==0){utag.DB('READY:utag.loader.wq');utag.loader.rf=1;utag.loader.WQ();}});else if(this.lq.length>0)utag.loader.rf=1;else if(this.lq.length==0)utag.loader.END();return 1};if(utag.cfg.readywait||utag.cfg.waittimer){utag.loader.EV('','ready',function(a){if(utag.loader.rf==0){utag.loader.rf=1;utag.cfg.readywait=1;utag.DB('READY:utag.cfg.readywait');setTimeout(function(){utag.loader.PINIT()},utag.cfg.waittimer||1);}})}else{utag.loader.PINIT()}}