"""
Test script to demonstrate the captcha solver fallback mechanism.
This shows how the solver automatically falls back to solvecaptcha.com when 2cap<PERSON><PERSON> fails.
"""

import logging
from Captcha_files.captcha_solver import CaptchaSolver

# Setup logging to see the fallback in action
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s - %(levelname)s - %(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def test_fallback_mechanism():
    """Test the fallback mechanism with example parameters."""
    
    print("=" * 60)
    print("CAPTCHA SOLVER FALLBACK TEST")
    print("=" * 60)
    
    # Initialize captcha solver
    captcha_solver = CaptchaSolver()
    
    # Example parameters (same as would be used in GMX)
    website_url = "https://signup.gmx.com/"
    website_key = "sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w"
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    
    # Example proxy config (optional)
    proxy_config = {
        "type": "http",
        "address": "*************",
        "port": "12323",
        "username": "14a638c2105fb",
        "password": "10ef8e713c"
    }
    
    print(f"Website URL: {website_url}")
    print(f"Website Key: {website_key}")
    print(f"User Agent: {user_agent[:50]}...")
    print(f"Proxy Config: {proxy_config}")
    print()
    
    print("Starting captcha solving with fallback mechanism...")
    print("This will:")
    print("1. Try 2captcha first")
    print("2. If 2captcha fails, automatically try solvecaptcha.com")
    print("3. Return the solution from whichever service succeeds")
    print()
    
    # Call the solve method - fallback happens automatically
    solution = captcha_solver.solve_captcha_fox(
        website_url=website_url,
        website_key=website_key,
        user_agent=user_agent,
        proxy_config=proxy_config
    )
    
    print()
    if solution:
        print(f"✅ SUCCESS: Captcha solved! Token: {solution[:30]}...")
        print("The fallback mechanism worked - either 2captcha or solvecaptcha.com succeeded")
    else:
        print("❌ FAILED: Both 2captcha and solvecaptcha.com failed")
        print("Check your API keys and network connection")
    
    print("=" * 60)
    return solution

def integration_example():
    """Show how the fallback integrates with existing GMX code."""
    
    example_code = '''
    # Your existing GMX code doesn't need to change at all!
    # The fallback happens automatically inside solve_captcha_fox()
    
    def solve_gmx_captcha(self):
        """Your existing method - no changes needed."""
        captcha_solver = CaptchaSolver()
        
        # This call now automatically includes fallback
        solution = captcha_solver.solve_captcha_fox(
            website_url=self.browser.current_url,
            website_key="sk_vKdD8WGlPF5FKpRDs1U4qTuu6Jv0w",
            user_agent=self.browser.execute_script("return navigator.userAgent;")
        )
        
        if solution:
            self.logger.info("Captcha solved (could be 2captcha or solvecaptcha)")
            return True
        else:
            self.logger.error("Both captcha services failed")
            return False
    
    # The process flow is now:
    # 1. Try 2captcha
    # 2. If 2captcha returns None -> automatically try solvecaptcha.com
    # 3. Return result from successful service
    # 4. If both fail -> return None
    '''
    
    print("INTEGRATION EXAMPLE:")
    print("=" * 40)
    print(example_code)

def fallback_flow_explanation():
    """Explain how the fallback mechanism works."""
    
    explanation = """
    FALLBACK MECHANISM FLOW:
    =======================
    
    1. solve_captcha_fox() is called with parameters
    
    2. PRIMARY: Try 2captcha service
       - Create task with 2captcha API
       - Wait for solution
       - If solution received -> return it ✅
    
    3. FALLBACK: If 2captcha fails (returns None)
       - Log: "2captcha failed, trying solvecaptcha.com fallback..."
       - Call _solve_with_solvecaptcha_fallback() with SAME parameters
       - Create task with solvecaptcha.com API
       - Wait for solution
       - If solution received -> return it ✅
    
    4. If both services fail -> return None ❌
    
    KEY FEATURES:
    ============
    ✓ Seamless integration - no code changes needed
    ✓ Same parameters used for both services
    ✓ Automatic proxy format conversion (login:password@ip:port)
    ✓ Proper error handling and logging
    ✓ Simple, readable code
    ✓ Only modifies solve_captcha_fox() method
    
    CONFIGURATION:
    =============
    - Add SOLVECAPTCHA_API_KEY to config.py
    - Both API keys are loaded automatically
    - Fallback triggers only when primary service fails
    """
    
    print(explanation)

if __name__ == "__main__":
    print("Captcha Solver Fallback Mechanism")
    print()
    
    # Show how it works
    fallback_flow_explanation()
    print()
    
    # Show integration example
    integration_example()
    print()
    
    # Uncomment to test with real API calls
    # test_fallback_mechanism()
    
    print("✅ Fallback mechanism successfully implemented!")
    print("Your GMX creator will now automatically try solvecaptcha.com if 2captcha fails.")
