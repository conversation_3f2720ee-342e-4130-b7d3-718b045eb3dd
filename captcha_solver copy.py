"""
Captcha solver module for handling 2captcha integration.
"""
import time
import base64
from twocaptcha import TwoCaptcha
from loguru import logger
import config

class CaptchaSolver:
    """Class to handle captcha solving using 2captcha service."""
    
    def __init__(self, api_key=None):
        """Initialize the captcha solver.
        
        Args:
            api_key: 2captcha API key (default from config)
        """
        if api_key is None:
            api_key = config.TWOCAPTCHA_API_KEY
            
        self.solver = TwoCaptcha(api_key)
        self.max_retries = 3
    
    async def solve_slider_captcha(self, page):
        """Solve a slider captcha on the page.

        Returns:
            bool: True if solved successfully, False otherwise
        """
        try:
            # Check if captcha iframe exists
            captcha_iframe = page.locator('iframe[src*="captcha"]').first
            if not await captcha_iframe.count():
                logger.info("No captcha iframe found")
                return True
            
            # Get the iframe source
            iframe_src = await captcha_iframe.get_attribute('src')
            logger.info(f"Found captcha iframe: {iframe_src}")
            

            # Send to 2captcha
            for attempt in range(self.max_retries):
                try:
                    logger.info(f"Solving captcha, attempt {attempt + 1}/{self.max_retries}")
                    
                    result = self.solver.(
                        base64=captcha_base64,
                        site_key=self._extract_site_key(iframe_src),
                        page_url=page.url
                    )
                    
                    logger.info(f"Captcha solved: {result}")
                    
                    # Apply the solution
                    await self._apply_slider_solution(page, captcha_iframe, result)
                    return True
                    
                except Exception as e:
                    logger.error(f"Error solving captcha (attempt {attempt + 1}): {e}")
                    if attempt < self.max_retries - 1:
                        time.sleep(3)  # Wait before retry
                    else:
                        return False
            
            return False
            
        except Exception as e:
            logger.error(f"Error in solve_slider_captcha: {e}")
            return False
    
    def _extract_site_key(self, iframe_src):
        """Extract site key from iframe source.
        
        Args:
            iframe_src: Source URL of the captcha iframe
            
        Returns:
            str: Site key or empty string if not found
        """
        # This is a placeholder - actual implementation depends on GMX's captcha structure
        # Common patterns include 'sitekey=XXXXX' or 'k=XXXXX'
        import re
        match = re.search(r'[?&](sitekey|k)=([^&]+)', iframe_src)
        if match:
            return match.group(2)
        return ""
    
    async def _apply_slider_solution(self, page, iframe, solution):
        """Apply the slider captcha solution.
        
        Args:
            page: Playwright page object
            iframe: Captcha iframe locator
            solution: Solution from 2captcha
            
        Returns:
            None
        """
        # This implementation is a placeholder and will need to be adjusted
        # based on the actual structure of GMX's slider captcha
        try:
            # Switch to the iframe context
            frame = await iframe.content_frame()
            
            # Find the slider element
            slider = frame.locator('.slider-handle, .captcha-slider')
            
            # Get slider position
            slider_box = await slider.bounding_box()
            
            # Calculate the target position based on the solution
            # The solution from 2captcha typically provides a percentage or pixel value
            target_x = slider_box['x'] + (solution.get('position', 0) * slider_box['width'])
            
            # Perform the drag operation
            await slider.hover()
            await page.mouse.down()
            await page.mouse.move(target_x, slider_box['y'] + slider_box['height'] / 2)
            await page.mouse.up()
            
            # Wait for verification
            await page.wait_for_timeout(2000)
            
        except Exception as e:
            logger.error(f"Error applying slider solution: {e}")
            raise
