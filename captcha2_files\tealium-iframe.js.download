(function(){"use strict";var c=(e=>(e.INIT="init",e.SET_DATA="setData",e.LOAD_SCRIPT="loadScript",e.READ_PERMISSIONS="readPermissions",e.TRACK="track",e.SETUP_SENTRY="setupSentry",e))(c||{}),x=(e=>(e[e.WAITING=0]="WAITING",e[e.RUNNING=1]="RUNNING",e[e.COMPLETED=2]="COMPLETED",e))(x||{});function f(){return"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".replace(/x/g,()=>Math.floor(Math.random()*16).toString(16))}function o({generateIfMissing:e=!1,generateNew:t=!1,cacheName:a="PageViewId"}={}){const r=sessionStorage.getItem(a);return t||!r&&e?(sessionStorage.setItem(a,f()),o({generateIfMissing:!1})):r}function d({tealiumWindow:e,scriptUrl:t,timeout:a,crossOrigin:r}){return new Promise((s,i)=>{const g=setTimeout(()=>{i(new Error(`Loading script "${t}" exceeded timeout of ${a} ms`))},a),n=document.createElement("script");n.type="text/javascript",r&&(n.crossOrigin=r),n.setAttribute("src",t),n.addEventListener("load",()=>{clearTimeout(g),s()}),n.addEventListener("error",u=>{clearTimeout(g),i(u.error||new Error(`Failed to load script "${t}".`))}),e.document.querySelector("head").appendChild(n)})}async function E(e,t,a){await d({tealiumWindow:e,scriptUrl:t.sdkUrl,timeout:a,crossOrigin:"anonymous"}),e.Sentry.init(t.options)}function T({command:e,payload:t=null,timeout:a=1500,tealiumWindow:r=window}){return new Promise(s=>{switch(e){case c.INIT:r.utag_data={pageViewId:o({generateIfMissing:!0})},s();break;case c.SET_DATA:r.utag_data={...r.utag_data,...t},s();break;case c.LOAD_SCRIPT:d({tealiumWindow:r,scriptUrl:t.toString(),timeout:a}).catch(n=>console.warn(n.message)).then(()=>s());break;case c.READ_PERMISSIONS:const i=setTimeout(()=>{console.warn("Timeout getting permissions"),s()},a);r.__tcfapi("getPermissionFeatures",2,n=>{n.forEach(u=>{Object.keys(u).forEach(I=>{r.utag_data[`permission_${I}`]=u[I]===!0?"1":"0"})}),clearTimeout(i),s()});break;case c.TRACK:switch(t.businessEventType){case"pageView":r.utag_data.pageViewId=o({generateNew:!0}),r.utag.view({...r.utag_data,...t});break;default:r.utag_data.pageViewId=o(),r.utag.link({...r.utag_data,...t});break}s();break;case c.SETUP_SENTRY:E(r,t,a).catch(n=>console.warn(`Could not load Sentry SDK: ${n.message}`)).then(()=>s());break;default:s();break}})}async function S(e){let t=e.find(a=>a.state!==2);for(;(t==null?void 0:t.state)===0;)t.state=1,await T(t),t.state=2,t=e.find(a=>a.state!==2)}function p(e){const t=[];e.addEventListener("message",a=>{if(a.source===e.parent){const{command:r,payload:s,timeout:i}=a.data;t.push({command:r,payload:s,timeout:i,state:x.WAITING}),S(t)}})}p(window)})();
