@font-face {
  font-family: RobotoMedium;
  font-style: normal;
  font-weight: 400;
  src: url('//img.ui-portal.de/ci/gmx/global/fonts/roboto/Roboto-Medium-webfont.woff') format('woff');
}

@font-face {
  font-family: RobotoCondensedRegular;
  font-style: normal;
  font-weight: 400;
  src: url('//img.ui-portal.de/ci/gmx/global/fonts/roboto/RobotoCondensed-Regular-webfont.woff') format('woff');
}

@font-face {
  font-family: RobotoRegular;
  font-style: normal;
  font-weight: 400;
  src: url('//img.ui-portal.de/ci/gmx/global/fonts/roboto/Roboto-Regular-webfont.woff') format('woff');
}

body {
  margin: 0;
  padding: 0;
  background-color: #fff;
  font-size: 16px;
  line-height: 24px;
  color: #1c449b;
  font-family: RobotoMedium, Arial, Verdana, Helvetica, sans-serif;
}

.teaser {
  width: 304px;
}

h1,
h2 {
  margin: 0;
  text-align: center;
  font-weight: normal;
}

h1 {
  font-family: RobotoCondensedRegular, Arial, Verdana, Helvetica, sans-serif;
  font-size: 40px;
  line-height: 48px;
}

h2 {
  font-size: 24px;
  line-height: 32px;
}

.teaser-text {
  margin: 0;
  padding: 20px 0 20px 68px;
  font-family: RobotoRegular, Arial, Verdana, Helvetica, sans-serif;
  color: #515151;
}

.teaser-text li {
  list-style-type: none;
  position: relative;
  margin-bottom: 5px;
}

.teaser-text li:before {
  position: absolute;
  top: 8px;
  content: '';
  width: 8px;
  height: 8px;
  background-color: #1c449b;
  border-radius: 50%;
  line-height: 24px;
  margin: 0 8px 0 -22px;
}
