//tealium universal tag - utag.22 ut4.0.202508060530, Copyright 2025 Tealium.com Inc. All Rights Reserved.
try{(function(id,loader){var u={};utag.o[loader].sender[id]=u;if(utag===undefined){utag={};}if(utag.ut===undefined){utag.ut={};}if(utag.ut.loader===undefined){u.loader=function(o){var a,b,c,l;a=document;if(o.type==="iframe"){b=a.createElement("iframe");b.setAttribute("height","1");b.setAttribute("width","1");b.setAttribute("style","display:none");b.setAttribute("src",o.src);}else if(o.type==="img"){utag.DB("Attach img: "+o.src);b=new Image();b.src=o.src;return;}else{b=a.createElement("script");b.language="javascript";b.type="text/javascript";b.async=1;b.charset="utf-8";b.src=o.src;}if(o.id){b.id=o.id;}if(typeof o.cb==="function"){if(b.addEventListener){b.addEventListener("load",function(){o.cb();},false);}else{b.onreadystatechange=function(){if(this.readyState==="complete"||this.readyState==="loaded"){this.onreadystatechange=null;o.cb();}};}}l=o.loc||"head";c=a.getElementsByTagName(l)[0];if(c){utag.DB("Attach to "+l+": "+o.src);if(l==="script"){c.parentNode.insertBefore(b,c);}else{c.appendChild(b);}}};}else{u.loader=utag.ut.loader;}
u.ev={'link':1};u.initialized=false;u.map_func=function(arr,obj,item){var i=arr.shift();obj[i]=obj[i]||{};if(arr.length>0){u.map_func(arr,obj[i],item);}else{obj[i]=item;}}
u.map={"js_page.clientTimestamp":"webEventData.clientEventData.clientTimestamp","js_page.pageUrl":"webEventData.clientEventData.userHttpRequest.referrer,webEventData.pageUrl","clientFeatures":"webEventData.clientEventData.clientInfo.clientFeatures","connectionType":"webEventData.clientEventData.clientInfo.connectionType","screen_height":"webEventData.clientEventData.clientInfo.deviceInfo.screenHeight","screen_width":"webEventData.clientEventData.clientInfo.deviceInfo.screenWidth","screen_ppi":"webEventData.clientEventData.clientInfo.deviceInfo.screenPpi","js_page.viewportHeight":"webEventData.clientEventData.clientInfo.deviceInfo.viewportHeight","hid":"webEventData.clientEventData.legacyUserIds.hid","js_page.viewportWidth":"webEventData.clientEventData.clientInfo.deviceInfo.viewportWidth","addressCountry":"webEventData.clientEventData.clientSideMasterData.addressCountry","userLevel":"webEventData.clientEventData.clientSideMasterData.userLevel","registrationCountry":"webEventData.clientEventData.clientSideMasterData.registrationCountry","cp.NGUserID":"webEventData.clientEventData.legacyUserIds.NGUserID","js_page.brain.name":"webEventData.clientEventData.legacyParameters.legacyBrainName","_sm_22_16":"webEventData.clientEventData.trackingSoftware.tswName","ut.version":"webEventData.clientEventData.trackingSoftware.tswVersion","ut.profile":"webEventData.clientEventData.trackingSoftware.tswProfile","_sm_22_19":"webEventData.clientEventData.trackingSoftware.tagVersion","trackingInitiator":"webEventData.clientEventData.trackingSoftware.trackingInitiator","trackingInitiatorVersion":"webEventData.clientEventData.trackingSoftware.trackingInitiatorVersion","userSegment":"webEventData.clientEventData.variantInfo.userSegment","cp.euconsent-v2":"webEventData.clientEventData.euConsentV2","contentLanguage":"webEventData.clientEventData.contentLanguage","eventType":"webEventData.eventType","businessEventType":"webEventData.clientEventData.businessEventType","appEnvironment":"webEventData.clientEventData.appEnvironment","softwareName":"webEventData.clientEventData.softwareName","softwareVersion":"webEventData.clientEventData.softwareVersion","applicationArea":"webEventData.clientEventData.applicationArea","contentName":"webEventData.clientEventData.contentName","pageType":"webEventData.clientEventData.pageType","brand":"webEventData.clientEventData.brand","contentCountry":"webEventData.clientEventData.contentCountry","pageViewId":"webEventData.pageViewId","mediaCode":"webEventData.mediaCode","agof":"webEventData.agof,webEventData.clientEventData.reachInfo.agof","js_page.pageTitle":"pageTitle","js_page.pageReferrer":"pageReferrer","elementTagName":"userInteractionInfo.elementTagName","componentName":"userInteractionInfo.componentName","componentPath":"userInteractionInfo.componentPath","componentLabel":"userInteractionInfo.componentLabel","targetUrl":"userInteractionInfo.targetUrl","js_page.trackingGatewayDomain":"req.url","_sm_22_46":"req.type","componentBadge":"userInteractionInfo.componentBadge","userAction":"userInteractionInfo.userAction","userActionObject":"userInteractionInfo.userActionObject","componentVariant":"userInteractionInfo.componentVariant","mailMessagesInfo":"webEventData.clientEventData.mailMessagesInfo","cp.ua_id":"webEventData.clientEventData.uaId","js_page.addOnBrand":"webEventData.addOnInfo.addOnBrand","js_page.addOnVersion":"webEventData.addOnInfo.addOnVersion","js_page.addOnVariant":"webEventData.addOnInfo.addOnVariant","addOnName":"webEventData.addOnInfo.addOnName","addOnEnabled":"webEventData.addOnInfo.addOnEnabled","addOnInstallDate":"webEventData.addOnInfo.addOnInstallDate","addOnAccountsCount":"webEventData.addOnInfo.addOnAccountsCount","addOnAccountsLoggedInCount":"webEventData.addOnInfo.addOnAccountsLoggedInCount","addOnAccountsExternalCount":"webEventData.addOnInfo.addOnExternalAccountsCount","teasersInfo":"webEventData.clientEventData.teasersInfo","alertInfo":"webEventData.clientEventData.alertInfo","js_page.visitId":"webEventData.clientEventData.visitInfo.visitId","cp.utag_main__se":"webEventData.clientEventData.visitInfo.visitEventCounter","contentLayout":"webEventData.clientEventData.layoutInfo.contentLayout","layoutClass":"webEventData.clientEventData.layoutInfo.layoutClass","mailListInfo":"webEventData.clientEventData.mailListInfo","articleId":"webEventData.clientEventData.editorialInfo.articleId","articleType":"webEventData.clientEventData.editorialInfo.articleType","contentCategory":"webEventData.clientEventData.editorialInfo.contentCategory","contentType":"webEventData.clientEventData.editorialInfo.contentType","debugData":"debugInfo.debugData","publicationTimestamp":"webEventData.clientEventData.editorialInfo.publicationTimestamp","authorId":"webEventData.clientEventData.editorialInfo.authorId","adblockDetected":"webEventData.browserInfo.adblockDetected","js_page.browserLanguage":"webEventData.browserInfo.browserLanguage","zoomFactor":"webEventData.browserInfo.zoomFactor","contentProvider":"webEventData.clientEventData.editorialInfo.contentProvider","commentCount":"webEventData.clientEventData.editorialInfo.commentCount","subContentCount":"webEventData.clientEventData.editorialInfo.subContentCount","subContentIndex":"webEventData.clientEventData.editorialInfo.subContentIndex","nativeAppVersion":"webEventData.nativeAppVersion","nativeAppName":"webEventData.nativeAppName","orders":"webEventData.clientEventData.smartInboxComponent.orders","listInfo":"webEventData.clientEventData.smartInboxComponent.listInfo","contracts":"webEventData.clientEventData.smartInboxComponent.contracts","contentDesignName":"webEventData.clientEventData.designInfo.contentDesignName","format":"webEventData.adInfo.format","property":"webEventData.adInfo.property","teaserHeadline":"webEventData.clientEventData.editorialInfo.teaserHeadline","type":"webEventData.clientEventData.smartInboxComponent.type","componentResultState":"userInteractionInfo.componentResultState","prefersColorScheme":"webEventData.clientEventData.clientInfo.prefersColorScheme","js_page.oewa":"webEventData.clientEventData.reachInfo.oewa","offerId":"webEventData.clientEventData.productSalesInfo.offerId","productName":"webEventData.clientEventData.productSalesInfo.productName","productGroupName":"webEventData.clientEventData.productSalesInfo.productGroupName","productGroupId":"webEventData.clientEventData.productSalesInfo.productGroupId","funnelName":"webEventData.clientEventData.productSalesInfo.funnelName","funnelId":"webEventData.clientEventData.productSalesInfo.funnelId","partnerId":"webEventData.clientEventData.productSalesInfo.partnerId","partnerName":"webEventData.clientEventData.productSalesInfo.partnerName","campaigns":"webEventData.clientEventData.campaigns","videoInfo":"webEventData.clientEventData.videoInfo","cloudInfo":"webEventData.clientEventData.cloudInfo","affectedFiles":"webEventData.clientEventData.affectedFiles","technicalEventInfo":"webEventData.clientEventData.technicalEventInfo","contentVariant":"webEventData.clientEventData.variantInfo.contentVariant","editorialInfo":"webEventData.clientEventData.editorialInfo","layoutInfo":"webEventData.clientEventData.layoutInfo","adInfo":"webEventData.adInfo","js_page.originalReferrer":"webEventData.originalReferrer","registrationFlowId":"webEventData.clientEventData.processInfo.registrationFlowId.uid","eventId":"header.eventId","js_page.pageUrl2":"debugInfo.debugData.pageUrl2","js_page.pageReferrer2":"debugInfo.debugData.pageReferrer2","js_page.originalReferrerDecoded":"debugInfo.debugData.originalReferrerDecoded"};u.extend=[function(a,b){try{b['_sm_22_16']="tmiq";}catch(e){utag.DB(e);}
try{b['_sm_22_19']="1.0";}catch(e){utag.DB(e);}
try{b['_sm_22_46']="POST";}catch(e){utag.DB(e);}}];u.send=function(a,b){if(u.ev[a]||u.ev.all!==undefined){var c,d,e,f,i;u.data={};for(c=0;c<u.extend.length;c++){try{d=u.extend[c](a,b);if(d==false)return}catch(e){}};for(d in utag.loader.GV(u.map)){if(b[d]!==undefined&&b[d]!==""){e=u.map[d].split(",");for(f=0;f<e.length;f++){u.map_func(e[f].split("."),u.data,b[d]);}}else{h=d.split(":");if(h.length===2&&b[h[0]]===h[1]){if(u.map[d]){u.data.event_name=u.map[d];}}}}
if(u.data.req){if(!u.data.req.type){utag.DB('Please check your request type')
return false;}
if(!u.data.req.url){utag.DB('Please check your request URL')
return false;}
var data={};for(var prop in u.data){if(prop!=='req'){data[prop]=u.data[prop];}}
if(typeof navigator.sendBeacon==="function"){var beaconUrl=u.data.req.url;var dataBlob=new Blob([JSON.stringify(data)],{type:'application/vnd.UserEvent-v3+json'});navigator.sendBeacon(beaconUrl,dataBlob);utag.DB("Tracking with Beacon.");}else{var xhr=new XMLHttpRequest();xhr.withCredentials=true;xhr.open(u.data.req.type,u.data.req.url);xhr.setRequestHeader("Content-Type","application/vnd.UserEvent-v3+json");if(u.data.req.type=="POST"){xhr.send(JSON.stringify(data));}else{xhr.send();}}}else{utag.DB('please check your request mappings')
return false;}
}};utag.o[loader].loader.LOAD(id);})("22","mam.beige");}catch(error){utag.DB(error);}
