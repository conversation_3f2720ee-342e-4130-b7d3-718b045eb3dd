//tealium universal tag - utag.21 ut4.0.202508060530, Copyright 2025 Tealium.com Inc. All Rights Reserved.
try{(function(id,loader){var u={};utag.o[loader].sender[id]=u;if(utag===undefined){utag={};}if(utag.ut===undefined){utag.ut={};}if(utag.ut.loader===undefined){u.loader=function(o){var a,b,c,l;a=document;if(o.type==="iframe"){b=a.createElement("iframe");b.setAttribute("height","1");b.setAttribute("width","1");b.setAttribute("style","display:none");b.setAttribute("src",o.src);}else if(o.type==="img"){utag.DB("Attach img: "+o.src);b=new Image();b.src=o.src;return;}else{b=a.createElement("script");b.language="javascript";b.type="text/javascript";b.async=1;b.charset="utf-8";b.src=o.src;}if(o.id){b.id=o.id;}if(typeof o.cb==="function"){if(b.addEventListener){b.addEventListener("load",function(){o.cb();},false);}else{b.onreadystatechange=function(){if(this.readyState==="complete"||this.readyState==="loaded"){this.onreadystatechange=null;o.cb();}};}}l=o.loc||"head";c=a.getElementsByTagName(l)[0];if(c){utag.DB("Attach to "+l+": "+o.src);if(l==="script"){c.parentNode.insertBefore(b,c);}else{c.appendChild(b);}}};}else{u.loader=utag.ut.loader;}
u.ev={'view':1};u.initialized=false;u.map_func=function(arr,obj,item){var i=arr.shift();obj[i]=obj[i]||{};if(arr.length>0){u.map_func(arr,obj[i],item);}else{obj[i]=item;}}
u.map={"js_page.clientTimestamp":"webEventData.clientEventData.clientTimestamp","js_page.pageUrl":"webEventData.clientEventData.userHttpRequest.referrer,webEventData.pageUrl","clientFeatures":"webEventData.clientEventData.clientInfo.clientFeatures","connectionType":"webEventData.clientEventData.clientInfo.connectionType","screen_height":"webEventData.clientEventData.clientInfo.deviceInfo.screenHeight","screen_width":"webEventData.clientEventData.clientInfo.deviceInfo.screenWidth","screen_ppi":"webEventData.clientEventData.clientInfo.deviceInfo.screenPpi","js_page.viewportHeight":"webEventData.clientEventData.clientInfo.deviceInfo.viewportHeight","js_page.viewportWidth":"webEventData.clientEventData.clientInfo.deviceInfo.viewportWidth","addressCountry":"webEventData.clientEventData.clientSideMasterData.addressCountry","userLevel":"webEventData.clientEventData.clientSideMasterData.userLevel","registrationCountry":"webEventData.clientEventData.clientSideMasterData.registrationCountry","hid":"webEventData.clientEventData.legacyUserIds.hid","cp.NGUserID":"webEventData.clientEventData.legacyUserIds.NGUserID","js_page.brain.name":"webEventData.clientEventData.legacyParameters.legacyBrainName","_sm_21_16":"webEventData.clientEventData.trackingSoftware.tswName","ut.version":"webEventData.clientEventData.trackingSoftware.tswVersion","ut.profile":"webEventData.clientEventData.trackingSoftware.tswProfile","_sm_21_19":"webEventData.clientEventData.trackingSoftware.tagVersion","trackingInitiator":"webEventData.clientEventData.trackingSoftware.trackingInitiator","trackingInitiatorVersion":"webEventData.clientEventData.trackingSoftware.trackingInitiatorVersion","userSegment":"webEventData.clientEventData.variantInfo.userSegment","cp.euconsent-v2":"webEventData.clientEventData.euConsentV2","contentLanguage":"webEventData.clientEventData.contentLanguage","_sm_21_25":"webEventData.clientEventData.businessEventType","appEnvironment":"webEventData.clientEventData.appEnvironment","softwareName":"webEventData.clientEventData.softwareName","softwareVersion":"webEventData.clientEventData.softwareVersion","applicationArea":"webEventData.clientEventData.applicationArea","contentName":"webEventData.clientEventData.contentName","pageType":"webEventData.clientEventData.pageType","brand":"webEventData.clientEventData.brand","funnelId":"webEventData.clientEventData.productSalesInfo.funnelId","funnelName":"webEventData.clientEventData.productSalesInfo.funnelName","productGroupId":"webEventData.clientEventData.productSalesInfo.productGroupId","productGroupName":"webEventData.clientEventData.productSalesInfo.productGroupName","offerId":"webEventData.clientEventData.productSalesInfo.offerId","productName":"webEventData.clientEventData.productSalesInfo.productName","contentCountry":"webEventData.clientEventData.contentCountry","pageViewId":"webEventData.pageViewId","mediaCode":"webEventData.mediaCode","agof":"webEventData.agof,webEventData.clientEventData.reachInfo.agof","js_page.pageTitle":"pageTitle","js_page.pageReferrer":"pageReferrer","js_page.trackingGatewayDomain":"req.url","_sm_21_46":"req.type","mailMessagesInfo":"webEventData.clientEventData.mailMessagesInfo","cp.ua_id":"webEventData.clientEventData.uaId","js_page.addOnBrand":"webEventData.addOnInfo.addOnBrand","js_page.addOnVersion":"webEventData.addOnInfo.addOnVersion","js_page.addOnVariant":"webEventData.addOnInfo.addOnVariant","addOnName":"webEventData.addOnInfo.addOnName","addOnEnabled":"webEventData.addOnInfo.addOnEnabled","addOnInstallDate":"webEventData.addOnInfo.addOnInstallDate","addOnAccountsCount":"webEventData.addOnInfo.addOnAccountsCount","addOnAccountsLoggedInCount":"webEventData.addOnInfo.addOnAccountsLoggedInCount","addOnAccountsExternalCount":"webEventData.addOnInfo.addOnExternalAccountsCount","js_page.visitId":"webEventData.clientEventData.visitInfo.visitId","cp.utag_main__se":"webEventData.clientEventData.visitInfo.visitEventCounter","contentLayout":"webEventData.clientEventData.layoutInfo.contentLayout","layoutClass":"webEventData.clientEventData.layoutInfo.layoutClass","mailListInfo":"webEventData.clientEventData.mailListInfo","articleId":"webEventData.clientEventData.editorialInfo.articleId","articleType":"webEventData.clientEventData.editorialInfo.articleType","contentCategory":"webEventData.clientEventData.editorialInfo.contentCategory","contentType":"webEventData.clientEventData.editorialInfo.contentType","publicationTimestamp":"webEventData.clientEventData.editorialInfo.publicationTimestamp","debugData":"debugInfo.debugData","authorId":"webEventData.clientEventData.editorialInfo.authorId","js_page.browserLanguage":"webEventData.browserInfo.browserLanguage","zoomFactor":"webEventData.browserInfo.zoomFactor","adblockDetected":"webEventData.browserInfo.adblockDetected","contentProvider":"webEventData.clientEventData.editorialInfo.contentProvider","commentCount":"webEventData.clientEventData.editorialInfo.commentCount","subContentCount":"webEventData.clientEventData.editorialInfo.subContentCount","subContentIndex":"webEventData.clientEventData.editorialInfo.subContentIndex","nativeAppName":"webEventData.nativeAppName","nativeAppVersion":"webEventData.nativeAppVersion","contentDesignName":"webEventData.clientEventData.designInfo.contentDesignName","type":"webEventData.clientEventData.smartInboxComponent.type","orders":"webEventData.clientEventData.smartInboxComponent.orders","listInfo":"webEventData.clientEventData.smartInboxComponent.listInfo","contracts":"webEventData.clientEventData.smartInboxComponent.contracts","format":"webEventData.adInfo.format","property":"webEventData.adInfo.property","teaserHeadline":"webEventData.clientEventData.editorialInfo.teaserHeadline","prefersColorScheme":"webEventData.clientEventData.clientInfo.prefersColorScheme","js_page.oewa":"webEventData.clientEventData.reachInfo.oewa","partnerId":"webEventData.clientEventData.productSalesInfo.partnerId","partnerName":"webEventData.clientEventData.productSalesInfo.partnerName","campaigns":"webEventData.clientEventData.campaigns","cloudInfo":"webEventData.clientEventData.cloudInfo","affectedFiles":"webEventData.clientEventData.affectedFiles","deviceRegion":"webEventData.clientEventData.clientInfo.deviceInfo.deviceRegion","contentVariant":"webEventData.clientEventData.variantInfo.contentVariant","js_page.eventType":"webEventData.eventType","editorialInfo":"webEventData.clientEventData.editorialInfo","layoutInfo":"webEventData.clientEventData.layoutInfo","js_page.uaid":"debugInfo.debugData.uaidTest","adInfo":"webEventData.adInfo","js_page.originalReferrer":"webEventData.originalReferrer","registrationFlowId":"webEventData.clientEventData.processInfo.registrationFlowId.uid","eventId":"header.eventId","js_page.pageUrl2":"debugInfo.debugData.pageUrl2","js_page.pageReferrer2":"debugInfo.debugData.pageReferrer2","js_page.originalReferrerDecoded":"debugInfo.debugData.originalReferrerDecoded"};u.extend=[function(a,b){try{b['_sm_21_16']="tmiq";}catch(e){utag.DB(e);}
try{b['_sm_21_19']="1.0";}catch(e){utag.DB(e);}
try{b['_sm_21_25']="pageView";}catch(e){utag.DB(e);}
try{b['_sm_21_46']="POST";}catch(e){utag.DB(e);}}];u.send=function(a,b){if(u.ev[a]||u.ev.all!==undefined){var c,d,e,f,i;u.data={};for(c=0;c<u.extend.length;c++){try{d=u.extend[c](a,b);if(d==false)return}catch(e){}};for(d in utag.loader.GV(u.map)){if(b[d]!==undefined&&b[d]!==""){e=u.map[d].split(",");for(f=0;f<e.length;f++){u.map_func(e[f].split("."),u.data,b[d]);}}else{h=d.split(":");if(h.length===2&&b[h[0]]===h[1]){if(u.map[d]){u.data.event_name=u.map[d];}}}}
if(u.data.req){if(!u.data.req.type){utag.DB('Please check your request type')
return false;}
if(!u.data.req.url){utag.DB('Please check your request URL')
return false;}
var xhr=new XMLHttpRequest();xhr.withCredentials=true;xhr.open(u.data.req.type,u.data.req.url);xhr.setRequestHeader("Content-Type","application/vnd.PageViewEvent-v3+json");if(u.data.req.type=="POST"){var data={};for(var prop in u.data){if(prop!=='req'){data[prop]=u.data[prop];}}
xhr.send(JSON.stringify(data));}else{xhr.send();}}else{utag.DB('please check your request mappings')
return false;}
}};utag.o[loader].loader.LOAD(id);})("21","mam.beige");}catch(error){utag.DB(error);}
