"""
Test script to debug captcha detection on GMX registration page.
Run this to see what captcha elements are found on the page.
"""

import logging
from Captcha_files.captcha_solver import CaptchaSolver

# Setup logging to see debug messages
logging.basicConfig(
    level=logging.DEBUG,
    format='[%(asctime)s - %(levelname)s - %(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def test_captcha_detection_with_existing_driver(browser):
    """
    Test captcha detection using your existing browser instance.
    Call this from your GMX creator code when you're on the registration page.
    """
    print("=" * 60)
    print("CAPTCHA DETECTION TEST")
    print("=" * 60)
    
    # Initialize captcha solver
    captcha_solver = CaptchaSolver()
    
    print(f"Current URL: {browser.current_url}")
    print()
    
    # Step 1: Run comprehensive debug
    print("Step 1: Running comprehensive debug...")
    captcha_solver.debug_captcha_elements(browser)
    print()
    
    # Step 2: Try to detect captcha
    print("Step 2: Attempting captcha detection...")
    captcha_info = captcha_solver.detect_captcha(browser)
    
    if captcha_info:
        print("✅ CAPTCHA DETECTED!")
        print(f"Type: {captcha_info.get('type')}")
        print(f"Website Key: {captcha_info.get('website_key')}")
        print(f"Detection Method: {captcha_info.get('detected_from', 'element_detection')}")
        if 'indicators' in captcha_info:
            print(f"Indicators: {captcha_info['indicators']}")
    else:
        print("❌ NO CAPTCHA DETECTED")
    
    print()
    
    # Step 3: Manual element search
    print("Step 3: Manual element search...")
    manual_search_results = []
    
    # Test different ways to find elements
    search_methods = [
        ("CSS: onereg-captcha", "css selector", "onereg-captcha"),
        ("XPath: //onereg-captcha", "xpath", "//onereg-captcha"),
        ("Tag: onereg-captcha", "tag name", "onereg-captcha"),
        ("CSS: onereg-captcha-fox", "css selector", "onereg-captcha-fox"),
        ("XPath: //onereg-captcha-fox", "xpath", "//onereg-captcha-fox"),
        ("CSS: .captchafox", "css selector", ".captchafox"),
        ("CSS: textarea[name='cf-captcha-response']", "css selector", "textarea[name='cf-captcha-response']"),
    ]
    
    for description, method, selector in search_methods:
        try:
            elements = browser.find_elements(method, selector)
            result = f"{description}: {len(elements)} found"
            if elements:
                result += f" (first element tag: {elements[0].tag_name})"
            manual_search_results.append(result)
            print(f"  {result}")
        except Exception as e:
            manual_search_results.append(f"{description}: ERROR - {str(e)}")
            print(f"  {description}: ERROR - {str(e)}")
    
    print()
    
    # Step 4: Page source analysis
    print("Step 4: Page source analysis...")
    try:
        page_source = browser.page_source
        keywords_to_check = [
            'onereg-captcha',
            'captchafox', 
            'cf-widget',
            'cf-captcha-response',
            'data-test="form-captcha"',
            'trackingsection="onereg-captcha-panel"'
        ]
        
        found_in_source = []
        for keyword in keywords_to_check:
            if keyword.lower() in page_source.lower():
                found_in_source.append(keyword)
                print(f"  ✅ Found '{keyword}' in page source")
            else:
                print(f"  ❌ '{keyword}' not found in page source")
        
        if found_in_source:
            print(f"\n  Keywords found in source: {found_in_source}")
        else:
            print("\n  ❌ No captcha keywords found in page source")
            
    except Exception as e:
        print(f"  ERROR analyzing page source: {e}")
    
    print()
    print("=" * 60)
    print("TEST COMPLETE")
    print("=" * 60)
    
    return {
        'captcha_detected': captcha_info is not None,
        'captcha_info': captcha_info,
        'manual_search': manual_search_results,
        'source_keywords': found_in_source if 'found_in_source' in locals() else []
    }

def add_to_gmx_worker():
    """
    Example of how to add this test to your GMX Worker class.
    """
    code_example = '''
    def debug_captcha_on_page(self):
        """Add this method to your Worker class for debugging."""
        from test_captcha_detection import test_captcha_detection_with_existing_driver
        
        self.logger.info("Running captcha detection test...")
        results = test_captcha_detection_with_existing_driver(self.browser)
        
        if results['captcha_detected']:
            self.logger.info("Captcha detected! Proceeding with solving...")
            # Your captcha solving code here
        else:
            self.logger.warning("No captcha detected. Check the debug output above.")
            
        return results
    '''
    
    print("Add this method to your Worker class:")
    print(code_example)

if __name__ == "__main__":
    print("Captcha Detection Test Script")
    print("This script is designed to be called from your GMX creator code.")
    print("When you're on the GMX registration page, call:")
    print("  test_captcha_detection_with_existing_driver(your_browser_instance)")
    print()
    add_to_gmx_worker()
